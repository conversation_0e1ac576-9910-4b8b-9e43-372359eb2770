// frontend/src/store/slices/encryptionSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type {
  EncryptionState,
  EncryptionError,
  StoredIdentityKeys,
  StoredSignedPreKey,
  StoredOneTimePreKey,
  ConversationSession,
  PreKeyCountResponse,
  KeyBundleResponse,
  EncryptionStatusResponse
} from '../../types/encryption';
import {
  initializeEncryption,
  generateKeyBundle,
  generateOneTimePreKeyBatch,
  needsInitialization,
  needsSignedPreKeyRotation,
  needsMoreOneTimePreKeys
} from '../../crypto/keyManager';
import {
  getIdentityKeys,
  getCurrentSignedPreKey,
  getAllOneTimePreKeys,
  getAllSessions,
  getStorageStats
} from '../../crypto/keyStorage';

// ============================================================================
// Initial State
// ============================================================================

const initialState: EncryptionState = {
  // Key management
  identityKeys: null,
  signedPreKey: null,
  oneTimePreKeys: [],
  
  // Session management
  sessions: {},
  
  // UI state
  isInitialized: false,
  isGeneratingKeys: false,
  isUploadingKeys: false,
  keyGenerationProgress: 0,
  
  // Error state
  error: null,
  
  // Statistics
  preKeyCount: null,
  lastKeyUpload: null
};

// ============================================================================
// Async Thunks
// ============================================================================

/**
 * Initialize encryption system
 */
export const initializeEncryptionAsync = createAsyncThunk(
  'encryption/initialize',
  async (_, { rejectWithValue }) => {
    try {
      await initializeEncryption((progress, message) => {
        // Progress updates are handled in the pending state
      });
      
      // Load initial data
      const [identityKeys, signedPreKey, oneTimePreKeys, sessions] = await Promise.all([
        getIdentityKeys(),
        getCurrentSignedPreKey(),
        getAllOneTimePreKeys(),
        getAllSessions()
      ]);
      
      return {
        identityKeys,
        signedPreKey,
        oneTimePreKeys,
        sessions: sessions.reduce((acc, session) => {
          acc[session.conversationId] = session;
          return acc;
        }, {} as Record<string, ConversationSession>)
      };
    } catch (error) {
      return rejectWithValue({
        code: 'INITIALIZATION_FAILED',
        message: error instanceof Error ? error.message : 'Failed to initialize encryption',
        details: { error }
      } as EncryptionError);
    }
  }
);

/**
 * Load encryption data from storage
 */
export const loadEncryptionDataAsync = createAsyncThunk(
  'encryption/loadData',
  async (_, { rejectWithValue }) => {
    try {
      const [identityKeys, signedPreKey, oneTimePreKeys, sessions, stats] = await Promise.all([
        getIdentityKeys(),
        getCurrentSignedPreKey(),
        getAllOneTimePreKeys(),
        getAllSessions(),
        getStorageStats()
      ]);
      
      return {
        identityKeys,
        signedPreKey,
        oneTimePreKeys,
        sessions: sessions.reduce((acc, session) => {
          acc[session.conversationId] = session;
          return acc;
        }, {} as Record<string, ConversationSession>),
        stats
      };
    } catch (error) {
      return rejectWithValue({
        code: 'STORAGE_FAILED',
        message: 'Failed to load encryption data',
        details: { error }
      } as EncryptionError);
    }
  }
);

/**
 * Check encryption status and needs
 */
export const checkEncryptionStatusAsync = createAsyncThunk(
  'encryption/checkStatus',
  async (_, { rejectWithValue }) => {
    try {
      const [
        needsInit,
        needsPreKeyRotation,
        needsMorePreKeys
      ] = await Promise.all([
        needsInitialization(),
        needsSignedPreKeyRotation(),
        needsMoreOneTimePreKeys()
      ]);
      
      return {
        needsInit,
        needsPreKeyRotation,
        needsMorePreKeys
      };
    } catch (error) {
      return rejectWithValue({
        code: 'STATUS_CHECK_FAILED',
        message: 'Failed to check encryption status',
        details: { error }
      } as EncryptionError);
    }
  }
);

// ============================================================================
// Slice Definition
// ============================================================================

const encryptionSlice = createSlice({
  name: 'encryption',
  initialState,
  reducers: {
    // UI state management
    setKeyGenerationProgress: (state, action: PayloadAction<number>) => {
      state.keyGenerationProgress = action.payload;
    },
    
    setIsGeneratingKeys: (state, action: PayloadAction<boolean>) => {
      state.isGeneratingKeys = action.payload;
    },
    
    setIsUploadingKeys: (state, action: PayloadAction<boolean>) => {
      state.isUploadingKeys = action.payload;
    },
    
    // Error management
    setError: (state, action: PayloadAction<EncryptionError | null>) => {
      state.error = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    // Pre-key count updates
    updatePreKeyCount: (state, action: PayloadAction<PreKeyCountResponse>) => {
      state.preKeyCount = action.payload;
    },
    
    // Session management
    addSession: (state, action: PayloadAction<ConversationSession>) => {
      state.sessions[action.payload.conversationId] = action.payload;
    },
    
    updateSession: (state, action: PayloadAction<{
      conversationId: string;
      updates: Partial<ConversationSession>;
    }>) => {
      const { conversationId, updates } = action.payload;
      if (state.sessions[conversationId]) {
        state.sessions[conversationId] = {
          ...state.sessions[conversationId],
          ...updates,
          updatedAt: new Date().toISOString()
        };
      }
    },
    
    removeSession: (state, action: PayloadAction<string>) => {
      delete state.sessions[action.payload];
    },
    
    // Key management
    updateOneTimePreKeys: (state, action: PayloadAction<StoredOneTimePreKey[]>) => {
      state.oneTimePreKeys = action.payload;
    },
    
    markPreKeysAsUploaded: (state, action: PayloadAction<number[]>) => {
      const uploadedIds = action.payload;
      state.oneTimePreKeys = state.oneTimePreKeys.map(preKey => 
        uploadedIds.includes(preKey.id) 
          ? { ...preKey, uploaded: true }
          : preKey
      );
      state.lastKeyUpload = new Date().toISOString();
    },
    
    markPreKeyAsUsed: (state, action: PayloadAction<number>) => {
      const keyId = action.payload;
      state.oneTimePreKeys = state.oneTimePreKeys.map(preKey => 
        preKey.id === keyId 
          ? { ...preKey, used: true }
          : preKey
      );
    },
    
    // Encryption status updates
    updateEncryptionStatus: (state, action: PayloadAction<{
      conversationId: string;
      status: EncryptionStatusResponse;
    }>) => {
      // This could be used to track per-conversation encryption status
      // For now, we'll just clear any related errors
      if (state.error?.details?.conversationId === action.payload.conversationId) {
        state.error = null;
      }
    },
    
    // Reset state (for logout)
    resetEncryptionState: () => initialState
  },
  
  extraReducers: (builder) => {
    // Initialize encryption
    builder
      .addCase(initializeEncryptionAsync.pending, (state) => {
        state.isGeneratingKeys = true;
        state.error = null;
        state.keyGenerationProgress = 0;
      })
      .addCase(initializeEncryptionAsync.fulfilled, (state, action) => {
        state.isGeneratingKeys = false;
        state.isInitialized = true;
        state.keyGenerationProgress = 100;
        state.identityKeys = action.payload.identityKeys;
        state.signedPreKey = action.payload.signedPreKey;
        state.oneTimePreKeys = action.payload.oneTimePreKeys;
        state.sessions = action.payload.sessions;
        state.error = null;
      })
      .addCase(initializeEncryptionAsync.rejected, (state, action) => {
        state.isGeneratingKeys = false;
        state.keyGenerationProgress = 0;
        state.error = action.payload as EncryptionError;
      });
    
    // Load encryption data
    builder
      .addCase(loadEncryptionDataAsync.pending, (state) => {
        state.error = null;
      })
      .addCase(loadEncryptionDataAsync.fulfilled, (state, action) => {
        state.isInitialized = !!action.payload.identityKeys;
        state.identityKeys = action.payload.identityKeys;
        state.signedPreKey = action.payload.signedPreKey;
        state.oneTimePreKeys = action.payload.oneTimePreKeys;
        state.sessions = action.payload.sessions;
        state.error = null;
      })
      .addCase(loadEncryptionDataAsync.rejected, (state, action) => {
        state.error = action.payload as EncryptionError;
      });
    
    // Check encryption status
    builder
      .addCase(checkEncryptionStatusAsync.fulfilled, (state, action) => {
        // Status check results can be used by components to trigger actions
        state.error = null;
      })
      .addCase(checkEncryptionStatusAsync.rejected, (state, action) => {
        state.error = action.payload as EncryptionError;
      });
  }
});

// ============================================================================
// Actions and Selectors Export
// ============================================================================

export const {
  setKeyGenerationProgress,
  setIsGeneratingKeys,
  setIsUploadingKeys,
  setError,
  clearError,
  updatePreKeyCount,
  addSession,
  updateSession,
  removeSession,
  updateOneTimePreKeys,
  markPreKeysAsUploaded,
  markPreKeyAsUsed,
  updateEncryptionStatus,
  resetEncryptionState
} = encryptionSlice.actions;

export default encryptionSlice.reducer;

// ============================================================================
// Selectors
// ============================================================================

export const selectEncryptionState = (state: { encryption: EncryptionState }) => state.encryption;
export const selectIsEncryptionInitialized = (state: { encryption: EncryptionState }) => state.encryption.isInitialized;
export const selectEncryptionError = (state: { encryption: EncryptionState }) => state.encryption.error;
export const selectIsGeneratingKeys = (state: { encryption: EncryptionState }) => state.encryption.isGeneratingKeys;
export const selectKeyGenerationProgress = (state: { encryption: EncryptionState }) => state.encryption.keyGenerationProgress;
export const selectPreKeyCount = (state: { encryption: EncryptionState }) => state.encryption.preKeyCount;
export const selectSessions = (state: { encryption: EncryptionState }) => state.encryption.sessions;
export const selectSessionByConversation = (conversationId: string) => 
  (state: { encryption: EncryptionState }) => state.encryption.sessions[conversationId];
export const selectUnuploadedPreKeys = (state: { encryption: EncryptionState }) => 
  state.encryption.oneTimePreKeys.filter(preKey => !preKey.uploaded);
export const selectUnusedPreKeys = (state: { encryption: EncryptionState }) => 
  state.encryption.oneTimePreKeys.filter(preKey => !preKey.used);
