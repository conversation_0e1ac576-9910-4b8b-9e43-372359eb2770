generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  password                                           String                    @db.VarChar(128)
  last_login                                         DateTime?                 @db.Timestamptz(6)
  is_superuser                                       Boolean
  username                                           String                    @unique @db.VarChar(150)
  is_staff                                           Boolean
  is_active                                          Boolean
  date_joined                                        DateTime                  @db.Timestamptz(6)
  id                                                 String                    @id @default(uuid()) @db.Uuid
  email                                              String                    @unique @db.VarChar(254)
  firstName                                          String                    @map("first_name") @db.VarChar(30)
  lastName                                           String                    @map("last_name") @db.VarChar(30)
  profilePicture                                     String?                   @map("profile_picture") @db.VarChar(200)
  isVerified                                         Boolean                   @map("is_verified")
  lastSeen                                           DateTime                  @updatedAt @map("last_seen") @db.Timestamptz(6)
  createdAt                                          DateTime                  @map("created_at") @db.Timestamptz(6)
  updatedAt                                          DateTime                  @updatedAt @map("updated_at") @db.Timestamptz(6)
  callEvents                                         CallEvent[]
  callParticipations                                 CallParticipant[]
  callQualityReports                                 CallQualityMetric[]
  receivedCalls                                      Call[]                    @relation("CallCallee")
  initiatedCalls                                     Call[]                    @relation("CallCaller")
  conversationParticipants                           ConversationParticipant[]
  conversationSessions                               ConversationSession[]
  createdConversations                               Conversation[]
  django_admin_log                                   django_admin_log[]
  group_events_group_events_actor_idTousers          group_events[]            @relation("group_events_actor_idTousers")
  group_events_group_events_target_user_idTousers    group_events[]            @relation("group_events_target_user_idTousers")
  group_invites_group_invites_invited_by_idTousers   group_invites[]           @relation("group_invites_invited_by_idTousers")
  group_invites_group_invites_invited_user_idTousers group_invites[]           @relation("group_invites_invited_user_idTousers")
  group_key_rotations                                group_key_rotations[]
  group_member_keys                                  group_member_keys[]
  keyBundleUploadLogs                                KeyBundleUploadLog[]
  media_downloads                                    media_downloads[]
  media_files                                        media_files[]
  messageStatuses                                    MessageStatus[]           @relation("MessageStatuses")
  sentMessages                                       Message[]                 @relation("MessageSender")
  oneTimePreKeys                                     OneTimePreKey[]
  keyBundle                                          UserKeyBundle?
  users_groups                                       users_groups[]
  users_user_permissions                             users_user_permissions[]

  @@index([email], map: "users_email_0ea73cca_like")
  @@index([username], map: "users_username_e8658fc8_like")
  @@map("users")
}

model Conversation {
  id                                                 String                    @id @default(uuid()) @db.Uuid
  type                                               String                    @db.VarChar(10)
  name                                               String?                   @db.VarChar(100)
  createdAt                                          DateTime                  @map("created_at") @db.Timestamptz(6)
  updatedAt                                          DateTime                  @updatedAt @map("updated_at") @db.Timestamptz(6)
  avatar_url                                         String?                   @db.VarChar(200)
  created_by_id                                      String?                   @db.Uuid
  description                                        String
  group_settings                                     Json
  invite_link                                        String?                   @unique @db.VarChar(100)
  is_public                                          Boolean
  max_participants                                   Int
  pinned_message_id                                  String?                   @db.Uuid
  calls                                              Call[]
  participants                                       ConversationParticipant[]
  encryptionSessions                                 ConversationSession[]
  users                                              User?                     @relation(fields: [created_by_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "conversations_created_by_id_67a75e1e_fk_users_id")
  messages_conversations_pinned_message_idTomessages Message?                  @relation("conversations_pinned_message_idTomessages", fields: [pinned_message_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "conversations_pinned_message_id_13304712_fk_messages_id")
  group_events                                       group_events[]
  group_invites                                      group_invites[]
  group_sessions                                     group_sessions?
  messages                                           Message[]

  @@index([created_by_id, createdAt], map: "conversatio_created_2bfc89_idx")
  @@index([is_public], map: "conversatio_is_publ_ddc6d2_idx")
  @@index([type, createdAt], map: "conversatio_type_4693ab_idx")
  @@index([created_by_id], map: "conversations_created_by_id_67a75e1e")
  @@index([invite_link], map: "conversations_invite_link_b73a5c0c_like")
  @@index([pinned_message_id], map: "conversations_pinned_message_id_13304712")
  @@map("conversations")
}

model ConversationParticipant {
  id                         String       @id @default(uuid()) @db.Uuid
  role                       String       @db.VarChar(15)
  joinedAt                   DateTime     @map("joined_at") @db.Timestamptz(6)
  conversationId             String       @map("conversation_id") @db.Uuid
  userId                     String       @map("user_id") @db.Uuid
  can_add_members            Boolean
  can_delete_messages        Boolean
  can_edit_group_info        Boolean
  can_pin_messages           Boolean
  can_remove_members         Boolean
  is_active                  Boolean
  mention_notifications_only Boolean
  notifications_enabled      Boolean
  conversation               Conversation @relation(fields: [conversationId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "conversation_partici_conversation_id_58e662d4_fk_conversat")
  user                       User         @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "conversation_participants_user_id_c65bf2e6_fk_users_id")

  @@unique([conversationId, userId], map: "conversation_participants_conversation_id_user_id_eeefe97d_uniq")
  @@index([conversationId], map: "conversation_participants_conversation_id_58e662d4")
  @@index([userId], map: "conversation_participants_user_id_c65bf2e6")
  @@index([conversationId, is_active], map: "conversatio_convers_b28779_idx")
  @@index([role], map: "conversatio_role_7e11b3_idx")
  @@index([userId, is_active], map: "conversatio_user_id_ae9f1a_idx")
  @@map("conversation_participants")
}

model Message {
  id                                                      String                    @id @default(uuid()) @db.Uuid
  content                                                 String
  messageType                                             String                    @map("message_type") @db.VarChar(10)
  createdAt                                               DateTime                  @map("created_at") @db.Timestamptz(6)
  updatedAt                                               DateTime                  @updatedAt @map("updated_at") @db.Timestamptz(6)
  conversationId                                          String                    @map("conversation_id") @db.Uuid
  senderId                                                String                    @map("sender_id") @db.Uuid
  encryptedContent                                        String                    @map("encrypted_content")
  iv                                                      String
  messageNumber                                           Int                       @map("message_number")
  previousChainLength                                     Int                       @map("previous_chain_length")
  senderRatchetKey                                        String                    @map("sender_ratchet_key")
  has_media                                               Boolean
  media_count                                             Int
  conversations_conversations_pinned_message_idTomessages Conversation[]            @relation("conversations_pinned_message_idTomessages")
  group_message_signatures                                group_message_signatures?
  media_files                                             media_files[]
  statuses                                                MessageStatus[]
  conversation                                            Conversation              @relation(fields: [conversationId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "messages_conversation_id_5ef638db_fk_conversations_id")
  sender                                                  User                      @relation("MessageSender", fields: [senderId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "messages_sender_id_dc5a0bbd_fk_users_id")

  @@index([conversationId], map: "messages_conversation_id_5ef638db")
  @@index([senderId], map: "messages_sender_id_dc5a0bbd")
  @@index([conversationId, createdAt], map: "messages_convers_3ebb41_idx")
  @@index([conversationId, messageNumber], map: "messages_convers_a829c8_idx")
  @@index([conversationId, has_media, createdAt], map: "messages_convers_b998f9_idx")
  @@index([has_media], map: "messages_has_media_3fd32058")
  @@index([senderId, createdAt], map: "messages_sender__bf8b1c_idx")
  @@map("messages")
}

model MessageStatus {
  id        String   @id @default(uuid()) @db.Uuid
  status    String   @db.VarChar(10)
  createdAt DateTime @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamptz(6)
  messageId String   @map("message_id") @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  message   Message  @relation(fields: [messageId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "message_statuses_message_id_497b4101_fk_messages_id")
  user      User     @relation("MessageStatuses", fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "message_statuses_user_id_619e2277_fk_users_id")

  @@unique([messageId, userId], map: "message_statuses_message_id_user_id_d84c4322_uniq")
  @@index([messageId], map: "message_statuses_message_id_497b4101")
  @@index([userId], map: "message_statuses_user_id_619e2277")
  @@map("message_statuses")
}

model UserKeyBundle {
  id                    String   @id @default(uuid()) @db.Uuid
  identityPublicKey     String   @map("identity_public_key")
  signedPrekeyId        Int      @map("signed_prekey_id")
  signedPrekeyPublic    String   @map("signed_prekey_public")
  signedPrekeySignature String   @map("signed_prekey_signature")
  createdAt             DateTime @map("created_at") @db.Timestamptz(6)
  updatedAt             DateTime @updatedAt @map("updated_at") @db.Timestamptz(6)
  userId                String   @unique @map("user_id") @db.Uuid
  user                  User     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "user_key_bundles_user_id_e121d3b4_fk_users_id")

  @@map("user_key_bundles")
}

model OneTimePreKey {
  id        String    @id @default(uuid()) @db.Uuid
  keyId     Int       @map("key_id")
  publicKey String    @map("public_key")
  isUsed    Boolean   @map("is_used")
  createdAt DateTime  @map("created_at") @db.Timestamptz(6)
  usedAt    DateTime? @map("used_at") @db.Timestamptz(6)
  userId    String    @map("user_id") @db.Uuid
  user      User      @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "one_time_prekeys_user_id_c3428282_fk_users_id")

  @@unique([userId, keyId], map: "one_time_prekeys_user_id_key_id_6c0070f0_uniq")
  @@index([createdAt], map: "one_time_pr_created_1f3399_idx")
  @@index([userId, isUsed], map: "one_time_pr_user_id_565628_idx")
  @@index([userId], map: "one_time_prekeys_user_id_c3428282")
  @@map("one_time_prekeys")
}

model ConversationSession {
  id                   String       @id @default(uuid()) @db.Uuid
  sessionState         Json         @map("session_state")
  rootKey              String       @map("root_key")
  chainKeySend         String?      @map("chain_key_send")
  chainKeyReceive      String?      @map("chain_key_receive")
  messageNumberSend    Int          @map("message_number_send")
  messageNumberReceive Int          @map("message_number_receive")
  previousChainLength  Int          @map("previous_chain_length")
  createdAt            DateTime     @map("created_at") @db.Timestamptz(6)
  updatedAt            DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  conversationId       String       @map("conversation_id") @db.Uuid
  participantId        String       @map("participant_id") @db.Uuid
  conversation         Conversation @relation(fields: [conversationId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "conversation_session_conversation_id_f7d9cbcd_fk_conversat")
  participant          User         @relation(fields: [participantId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "conversation_sessions_participant_id_c8e379f0_fk_users_id")
  messageKeys          MessageKey[]

  @@unique([conversationId, participantId], map: "conversation_sessions_conversation_id_particip_1012dfaa_uniq")
  @@index([conversationId, participantId], map: "conversatio_convers_170dde_idx")
  @@index([updatedAt], map: "conversatio_updated_2cfa54_idx")
  @@index([conversationId], map: "conversation_sessions_conversation_id_f7d9cbcd")
  @@index([participantId], map: "conversation_sessions_participant_id_c8e379f0")
  @@map("conversation_sessions")
}

model MessageKey {
  id               String              @id @default(uuid()) @db.Uuid
  messageNumber    Int                 @map("message_number")
  messageKey       String              @map("message_key")
  senderRatchetKey String              @map("sender_ratchet_key")
  createdAt        DateTime            @map("created_at") @db.Timestamptz(6)
  sessionId        String              @map("session_id") @db.Uuid
  session          ConversationSession @relation(fields: [sessionId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "message_keys_session_id_7de76111_fk_conversation_sessions_id")

  @@unique([sessionId, senderRatchetKey, messageNumber], map: "message_keys_session_id_sender_ratche_bffd6856_uniq")
  @@index([createdAt], map: "message_key_created_fd0ccb_idx")
  @@index([sessionId, messageNumber], map: "message_key_session_78960f_idx")
  @@index([sessionId], map: "message_keys_session_id_7de76111")
  @@map("message_keys")
}

model KeyBundleUploadLog {
  id           String   @id @default(uuid()) @db.Uuid
  ipAddress    String   @map("ip_address") @db.Inet
  success      Boolean
  errorMessage String   @map("error_message")
  createdAt    DateTime @map("created_at") @db.Timestamptz(6)
  userId       String   @map("user_id") @db.Uuid
  user         User     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "key_bundle_upload_logs_user_id_9187c577_fk_users_id")

  @@index([ipAddress, createdAt], map: "key_bundle__ip_addr_d36791_idx")
  @@index([success, createdAt], map: "key_bundle__success_b70a97_idx")
  @@index([userId, createdAt], map: "key_bundle__user_id_de82a1_idx")
  @@index([userId], map: "key_bundle_upload_logs_user_id_9187c577")
  @@map("key_bundle_upload_logs")
}

model auth_group {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @unique @db.VarChar(150)
  auth_group_permissions auth_group_permissions[]
  users_groups           users_groups[]

  @@index([name], map: "auth_group_name_a6ea08ec_like")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model auth_group_permissions {
  id              BigInt          @id @default(autoincrement())
  group_id        Int
  permission_id   Int
  auth_permission auth_permission @relation(fields: [permission_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "auth_group_permissio_permission_id_84c5c92e_fk_auth_perm")
  auth_group      auth_group      @relation(fields: [group_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "auth_group_permissions_group_id_b120cbf9_fk_auth_group_id")

  @@unique([group_id, permission_id], map: "auth_group_permissions_group_id_permission_id_0cd325b0_uniq")
  @@index([group_id], map: "auth_group_permissions_group_id_b120cbf9")
  @@index([permission_id], map: "auth_group_permissions_permission_id_84c5c92e")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model auth_permission {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @db.VarChar(255)
  content_type_id        Int
  codename               String                   @db.VarChar(100)
  auth_group_permissions auth_group_permissions[]
  django_content_type    django_content_type      @relation(fields: [content_type_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "auth_permission_content_type_id_2f476e4b_fk_django_co")
  users_user_permissions users_user_permissions[]

  @@unique([content_type_id, codename], map: "auth_permission_content_type_id_codename_01ab375a_uniq")
  @@index([content_type_id], map: "auth_permission_content_type_id_2f476e4b")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model django_admin_log {
  id                  Int                  @id @default(autoincrement())
  action_time         DateTime             @db.Timestamptz(6)
  object_id           String?
  object_repr         String               @db.VarChar(200)
  action_flag         Int                  @db.SmallInt
  change_message      String
  content_type_id     Int?
  user_id             String               @db.Uuid
  django_content_type django_content_type? @relation(fields: [content_type_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "django_admin_log_content_type_id_c4bce8eb_fk_django_co")
  users               User                 @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "django_admin_log_user_id_c564eba6_fk_users_id")

  @@index([content_type_id], map: "django_admin_log_content_type_id_c4bce8eb")
  @@index([user_id], map: "django_admin_log_user_id_c564eba6")
}

model django_content_type {
  id               Int                @id @default(autoincrement())
  app_label        String             @db.VarChar(100)
  model            String             @db.VarChar(100)
  auth_permission  auth_permission[]
  django_admin_log django_admin_log[]

  @@unique([app_label, model], map: "django_content_type_app_label_model_76bd3d3b_uniq")
}

model django_migrations {
  id      BigInt   @id @default(autoincrement())
  app     String   @db.VarChar(255)
  name    String   @db.VarChar(255)
  applied DateTime @db.Timestamptz(6)
}

model django_session {
  session_key  String   @id @db.VarChar(40)
  session_data String
  expire_date  DateTime @db.Timestamptz(6)

  @@index([expire_date], map: "django_session_expire_date_a5c62663")
  @@index([session_key], map: "django_session_session_key_c0390e0f_like")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model users_groups {
  id         BigInt     @id @default(autoincrement())
  user_id    String     @db.Uuid
  group_id   Int
  auth_group auth_group @relation(fields: [group_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "users_groups_group_id_2f3517aa_fk_auth_group_id")
  users      User       @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "users_groups_user_id_f500bee5_fk_users_id")

  @@unique([user_id, group_id], map: "users_groups_user_id_group_id_fc7788e8_uniq")
  @@index([group_id], map: "users_groups_group_id_2f3517aa")
  @@index([user_id], map: "users_groups_user_id_f500bee5")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model users_user_permissions {
  id              BigInt          @id @default(autoincrement())
  user_id         String          @db.Uuid
  permission_id   Int
  auth_permission auth_permission @relation(fields: [permission_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "users_user_permissio_permission_id_6d08dcd2_fk_auth_perm")
  users           User            @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "users_user_permissions_user_id_92473840_fk_users_id")

  @@unique([user_id, permission_id], map: "users_user_permissions_user_id_permission_id_3b86cbdf_uniq")
  @@index([permission_id], map: "users_user_permissions_permission_id_6d08dcd2")
  @@index([user_id], map: "users_user_permissions_user_id_92473840")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model group_events {
  id                                       String       @id @db.Uuid
  event_type                               String       @db.VarChar(20)
  event_data                               Json
  created_at                               DateTime     @db.Timestamptz(6)
  actor_id                                 String       @db.Uuid
  conversation_id                          String       @db.Uuid
  target_user_id                           String?      @db.Uuid
  users_group_events_actor_idTousers       User         @relation("group_events_actor_idTousers", fields: [actor_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "group_events_actor_id_c44c7ac4_fk_users_id")
  conversations                            Conversation @relation(fields: [conversation_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "group_events_conversation_id_d3486cda_fk_conversations_id")
  users_group_events_target_user_idTousers User?        @relation("group_events_target_user_idTousers", fields: [target_user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "group_events_target_user_id_0014214e_fk_users_id")

  @@index([actor_id, created_at], map: "group_event_actor_i_04ae36_idx")
  @@index([conversation_id, created_at], map: "group_event_convers_6e968a_idx")
  @@index([event_type, created_at], map: "group_event_event_t_520b89_idx")
  @@index([actor_id], map: "group_events_actor_id_c44c7ac4")
  @@index([conversation_id], map: "group_events_conversation_id_d3486cda")
  @@index([target_user_id], map: "group_events_target_user_id_0014214e")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model group_invites {
  id                                         String       @id @db.Uuid
  invited_email                              String?      @db.VarChar(254)
  invite_code                                String       @unique @db.VarChar(50)
  expires_at                                 DateTime     @db.Timestamptz(6)
  is_used                                    Boolean
  used_at                                    DateTime?    @db.Timestamptz(6)
  created_at                                 DateTime     @db.Timestamptz(6)
  conversation_id                            String       @db.Uuid
  invited_by_id                              String       @db.Uuid
  invited_user_id                            String?      @db.Uuid
  conversations                              Conversation @relation(fields: [conversation_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "group_invites_conversation_id_2f7baa66_fk_conversations_id")
  users_group_invites_invited_by_idTousers   User         @relation("group_invites_invited_by_idTousers", fields: [invited_by_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "group_invites_invited_by_id_9240375e_fk_users_id")
  users_group_invites_invited_user_idTousers User?        @relation("group_invites_invited_user_idTousers", fields: [invited_user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "group_invites_invited_user_id_6a878cc8_fk_users_id")

  @@unique([conversation_id, invited_user_id], map: "group_invites_conversation_id_invited_user_id_ede62ab4_uniq")
  @@index([conversation_id, is_used], map: "group_invit_convers_690689_idx")
  @@index([expires_at, is_used], map: "group_invit_expires_c92527_idx")
  @@index([invite_code], map: "group_invit_invite__b0e1c8_idx")
  @@index([conversation_id], map: "group_invites_conversation_id_2f7baa66")
  @@index([invite_code], map: "group_invites_invite_code_1e31896a_like")
  @@index([invited_by_id], map: "group_invites_invited_by_id_9240375e")
  @@index([invited_user_id], map: "group_invites_invited_user_id_6a878cc8")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model group_key_rotations {
  id               String         @id @db.Uuid
  old_epoch        Int
  new_epoch        Int
  reason           String         @db.VarChar(50)
  old_key_hash     String         @db.VarChar(64)
  created_at       DateTime       @db.Timestamptz(6)
  rotated_by_id    String         @db.Uuid
  group_session_id String         @db.Uuid
  group_sessions   group_sessions @relation(fields: [group_session_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "group_key_rotations_group_session_id_30263bde_fk_group_ses")
  users            User           @relation(fields: [rotated_by_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "group_key_rotations_rotated_by_id_16c3ee91_fk_users_id")

  @@index([created_at], map: "group_key_r_created_45d4d5_idx")
  @@index([group_session_id, new_epoch], map: "group_key_r_group_s_a4a619_idx")
  @@index([rotated_by_id, created_at], map: "group_key_r_rotated_48c12f_idx")
  @@index([group_session_id], map: "group_key_rotations_group_session_id_30263bde")
  @@index([rotated_by_id], map: "group_key_rotations_rotated_by_id_16c3ee91")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model group_member_keys {
  id                   String         @id @db.Uuid
  ephemeral_public_key String
  encrypted_group_key  String
  auth_tag             String         @db.VarChar(32)
  iv                   String         @db.VarChar(32)
  epoch                Int
  is_claimed           Boolean
  claimed_at           DateTime?      @db.Timestamptz(6)
  created_at           DateTime       @db.Timestamptz(6)
  member_id            String         @db.Uuid
  group_session_id     String         @db.Uuid
  group_sessions       group_sessions @relation(fields: [group_session_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "group_member_keys_group_session_id_070ce7dc_fk_group_ses")
  users                User           @relation(fields: [member_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "group_member_keys_member_id_d906f8ab_fk_users_id")

  @@unique([group_session_id, member_id, epoch], map: "group_member_keys_group_session_id_member__bd290a0d_uniq")
  @@index([epoch, created_at], map: "group_membe_epoch_fa7541_idx")
  @@index([group_session_id, member_id, is_claimed], map: "group_membe_group_s_0065a0_idx")
  @@index([is_claimed, created_at], map: "group_membe_is_clai_e556a7_idx")
  @@index([group_session_id], map: "group_member_keys_group_session_id_070ce7dc")
  @@index([member_id], map: "group_member_keys_member_id_d906f8ab")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model group_message_signatures {
  id               String   @id @db.Uuid
  sender_signature String
  signature_data   Json
  created_at       DateTime @db.Timestamptz(6)
  message_id       String   @unique @db.Uuid
  messages         Message  @relation(fields: [message_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "group_message_signatures_message_id_92a7f28a_fk_messages_id")

  @@index([created_at], map: "group_messa_created_a7fdc9_idx")
  @@index([message_id], map: "group_messa_message_511f3d_idx")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model group_sessions {
  id                  String                @id @db.Uuid
  session_id          String                @unique @db.VarChar(100)
  current_epoch       Int
  encrypted_group_key String
  group_key_iv        String                @db.VarChar(32)
  created_at          DateTime              @db.Timestamptz(6)
  updated_at          DateTime              @db.Timestamptz(6)
  conversation_id     String                @unique @db.Uuid
  group_key_rotations group_key_rotations[]
  group_member_keys   group_member_keys[]
  conversations       Conversation          @relation(fields: [conversation_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "group_sessions_conversation_id_719caaa4_fk_conversations_id")

  @@index([conversation_id], map: "group_sessi_convers_28b68a_idx")
  @@index([current_epoch], map: "group_sessi_current_8cded8_idx")
  @@index([session_id], map: "group_sessi_session_27e8bf_idx")
  @@index([session_id], map: "group_sessions_session_id_83caa935_like")
}

model media_chunks {
  id             String   @id @db.Uuid
  upload_session String   @db.VarChar(100)
  chunk_number   Int
  total_chunks   Int
  chunk_data     Bytes
  chunk_hash     String   @db.VarChar(64)
  uploaded_at    DateTime @db.Timestamptz(6)

  @@unique([upload_session, chunk_number], map: "media_chunks_upload_session_chunk_number_60352f03_uniq")
  @@index([upload_session, chunk_number], map: "media_chunk_upload__e4884a_idx")
  @@index([upload_session], map: "media_chunks_upload_session_32cb47d2")
  @@index([upload_session], map: "media_chunks_upload_session_32cb47d2_like")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model media_downloads {
  id                 String      @id @db.Uuid
  download_token     String      @unique @db.VarChar(100)
  expires_at         DateTime    @db.Timestamptz(6)
  download_count     Int
  max_downloads      Int
  last_downloaded_at DateTime?   @db.Timestamptz(6)
  ip_address         String      @db.Inet
  user_agent         String
  created_at         DateTime    @db.Timestamptz(6)
  downloaded_by_id   String      @db.Uuid
  media_file_id      String      @db.Uuid
  users              User        @relation(fields: [downloaded_by_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "media_downloads_downloaded_by_id_f4c88792_fk_users_id")
  media_files        media_files @relation(fields: [media_file_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "media_downloads_media_file_id_0dbb7a70_fk_media_files_id")

  @@index([download_token, expires_at], map: "media_downl_downloa_28e66f_idx")
  @@index([download_token], map: "media_downloads_download_token_66261008_like")
  @@index([downloaded_by_id], map: "media_downloads_downloaded_by_id_f4c88792")
  @@index([media_file_id], map: "media_downloads_media_file_id_0dbb7a70")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model media_files {
  id                       String                  @id @db.Uuid
  original_filename        String                  @db.VarChar(255)
  file_type                String                  @db.VarChar(10)
  mime_type                String                  @db.VarChar(100)
  file_size                BigInt
  encrypted_file_path      String                  @db.VarChar(500)
  encrypted_thumbnail_path String?                 @db.VarChar(500)
  wrapped_file_key         String
  file_nonce               String
  thumbnail_nonce          String?
  processing_status        String                  @db.VarChar(15)
  processing_error         String?
  encrypted_metadata       String?
  virus_scan_status        String                  @db.VarChar(20)
  virus_scan_result        String?
  virus_scan_hash          String?                 @db.VarChar(64)
  created_at               DateTime                @db.Timestamptz(6)
  updated_at               DateTime                @db.Timestamptz(6)
  message_id               String?                 @db.Uuid
  uploader_id              String                  @db.Uuid
  media_downloads          media_downloads[]
  messages                 Message?                @relation(fields: [message_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "media_files_message_id_0b7aff0a_fk_messages_id")
  users                    User                    @relation(fields: [uploader_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "media_files_uploader_id_82715845_fk_users_id")
  media_processing_jobs    media_processing_jobs[]

  @@index([message_id, file_type], map: "media_files_message_349291_idx")
  @@index([message_id], map: "media_files_message_id_0b7aff0a")
  @@index([uploader_id, created_at], map: "media_files_uploade_9fbe4e_idx")
  @@index([uploader_id], map: "media_files_uploader_id_82715845")
  @@index([virus_scan_status], map: "media_files_virus_s_60f726_idx")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model media_processing_jobs {
  id            String      @id @db.Uuid
  job_type      String      @db.VarChar(20)
  status        String      @db.VarChar(15)
  priority      Int
  started_at    DateTime?   @db.Timestamptz(6)
  completed_at  DateTime?   @db.Timestamptz(6)
  error_message String?
  result_data   Json
  retry_count   Int
  max_retries   Int
  created_at    DateTime    @db.Timestamptz(6)
  media_file_id String      @db.Uuid
  media_files   media_files @relation(fields: [media_file_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "media_processing_jobs_media_file_id_8c57c344_fk_media_files_id")

  @@index([job_type, status], map: "media_proce_job_typ_4491c0_idx")
  @@index([status, priority], map: "media_proce_status_857309_idx")
  @@index([media_file_id], map: "media_processing_jobs_media_file_id_8c57c344")
}

model Call {
  id              String                   @id @default(uuid()) @db.Uuid
  call_type       String                   @db.VarChar(10)
  status          String                   @db.VarChar(15)
  initiated_at    DateTime                 @db.Timestamptz(6)
  answered_at     DateTime?                @db.Timestamptz(6)
  ended_at        DateTime?                @db.Timestamptz(6)
  duration        Unsupported("interval")?
  session_id      String                   @unique @db.VarChar(100)
  caller_sdp      String?
  callee_sdp      String?
  quality_rating  Int?
  quality_issues  Json
  metadata        Json
  callee_id       String                   @db.Uuid
  caller_id       String                   @db.Uuid
  conversation_id String                   @db.Uuid
  events          CallEvent[]
  participants    CallParticipant[]
  qualityMetrics  CallQualityMetric[]
  callee          User                     @relation("CallCallee", fields: [callee_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "calls_callee_id_e4fe2513_fk_users_id")
  caller          User                     @relation("CallCaller", fields: [caller_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "calls_caller_id_a3b7a850_fk_users_id")
  conversation    Conversation             @relation(fields: [conversation_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "calls_conversation_id_684540fe_fk_conversations_id")

  @@index([conversation_id, status], map: "calls_convers_72e827_idx")
  @@index([caller_id, initiated_at], map: "calls_caller__0860d2_idx")
  @@index([callee_id, initiated_at], map: "calls_callee__e66bec_idx")
  @@index([session_id], map: "calls_session_02b3bf_idx")
  @@index([callee_id], map: "calls_callee_id_e4fe2513")
  @@index([caller_id], map: "calls_caller_id_a3b7a850")
  @@index([conversation_id], map: "calls_conversation_id_684540fe")
  @@index([session_id], map: "calls_session_id_a54b7d3f_like")
  @@map("calls")
}

model CallParticipant {
  id             String    @id @default(uuid()) @db.Uuid
  status         String    @db.VarChar(15)
  joined_at      DateTime? @db.Timestamptz(6)
  left_at        DateTime? @db.Timestamptz(6)
  audio_enabled  Boolean
  video_enabled  Boolean
  screen_sharing Boolean
  call_id        String    @db.Uuid
  user_id        String    @db.Uuid
  call           Call      @relation(fields: [call_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "call_participants_call_id_9bea7380_fk_calls_id")
  user           User      @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "call_participants_user_id_42fd8d77_fk_users_id")

  @@unique([call_id, user_id], map: "call_participants_call_id_user_id_8fc81a57_uniq")
  @@index([call_id, status], map: "call_partic_call_id_d59434_idx")
  @@index([user_id, joined_at], map: "call_partic_user_id_028821_idx")
  @@index([call_id], map: "call_participants_call_id_9bea7380")
  @@index([user_id], map: "call_participants_user_id_42fd8d77")
  @@map("call_participants")
}

model CallEvent {
  id         String   @id @default(uuid()) @db.Uuid
  event_type String   @db.VarChar(20)
  event_data Json
  timestamp  DateTime @db.Timestamptz(6)
  call_id    String   @db.Uuid
  user_id    String   @db.Uuid
  call       Call     @relation(fields: [call_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "call_events_call_id_3abe9994_fk_calls_id")
  user       User     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "call_events_user_id_cc527922_fk_users_id")

  @@index([call_id, timestamp], map: "call_events_call_id_70a3f4_idx")
  @@index([event_type, timestamp], map: "call_events_event_t_adf59e_idx")
  @@index([call_id], map: "call_events_call_id_3abe9994")
  @@index([user_id], map: "call_events_user_id_cc527922")
  @@map("call_events")
}

model CallQualityMetric {
  id                  String   @id @default(uuid()) @db.Uuid
  packet_loss         Float?
  jitter              Float?
  round_trip_time     Float?
  bandwidth_upload    Int?
  bandwidth_download  Int?
  audio_level         Float?
  audio_quality_score Float?
  video_resolution    String?  @db.VarChar(20)
  video_framerate     Float?
  video_quality_score Float?
  recorded_at         DateTime @db.Timestamptz(6)
  call_id             String   @db.Uuid
  user_id             String   @db.Uuid
  call                Call     @relation(fields: [call_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "call_quality_metrics_call_id_9180c3f3_fk_calls_id")
  user                User     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "call_quality_metrics_user_id_58ddb1c7_fk_users_id")

  @@index([call_id, recorded_at], map: "call_qualit_call_id_309dd2_idx")
  @@index([user_id, recorded_at], map: "call_qualit_user_id_0df7c8_idx")
  @@index([call_id], map: "call_quality_metrics_call_id_9180c3f3")
  @@index([user_id], map: "call_quality_metrics_user_id_58ddb1c7")
  @@map("call_quality_metrics")
}
