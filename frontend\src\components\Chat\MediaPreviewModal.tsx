// frontend/src/components/Chat/MediaPreviewModal.tsx
import React, { useState, useEffect } from 'react';
import { X, Send, File, Image, Video, Music, Archive, FileText, AlertCircle, RotateCcw } from 'lucide-react';
import { type MediaUploadFile } from '../../store/slices/mediaUploadSlice';
import { fileManager } from '../../utils/fileManager';

interface MediaPreviewModalProps {
  isOpen: boolean;
  files: MediaUploadFile[];
  onClose: () => void;
  onSend: () => void; // Changed to match actual usage
  onRemoveFile: (fileId: string) => void;
  uploading?: boolean;
  uploadProgress?: number;
  uploadError?: string;
  onRetry?: () => void;
}

const MediaPreviewModal: React.FC<MediaPreviewModalProps> = ({
  isOpen,
  files,
  onClose,
  onSend,
  onRemoveFile,
  uploading = false,
  uploadProgress = 0,
  uploadError,
  onRetry,
}) => {
  const [currentFileIndex, setCurrentFileIndex] = useState(0);

  useEffect(() => {
    if (files.length === 0) {
      onClose();
    }
  }, [files.length, onClose]);

  if (!isOpen || files.length === 0) return null;

  const currentFile = files[currentFileIndex];

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <Image className="w-8 h-8 text-blue-500" />;
      case 'video':
        return <Video className="w-8 h-8 text-purple-500" />;
      case 'audio':
        return <Music className="w-8 h-8 text-green-500" />;
      case 'document':
        return <FileText className="w-8 h-8 text-red-500" />;
      case 'archive':
        return <Archive className="w-8 h-8 text-yellow-500" />;
      default:
        return <File className="w-8 h-8 text-gray-500" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderFilePreview = (file: MediaUploadFile) => {
    const actualFile = fileManager.getFile(file.fileId);
    if (!actualFile) {
      return (
        <div className="w-64 h-64 bg-gray-100 rounded-lg flex flex-col items-center justify-center p-4">
          {getFileIcon(file.type)}
          <p className="mt-2 text-sm text-gray-600 text-center break-all">
            {file.name}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            {formatFileSize(file.size)}
          </p>
        </div>
      );
    }

    switch (file.type) {
      case 'image':
        return file.preview ? (
          <img
            src={file.preview}
            alt={file.name}
            className="max-w-full max-h-96 object-contain rounded-lg"
          />
        ) : (
          <div className="w-64 h-64 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image className="w-16 h-16 text-gray-400" />
          </div>
        );
      
      case 'video':
        return file.preview ? (
          <video
            src={file.preview}
            controls
            className="max-w-full max-h-96 rounded-lg"
          />
        ) : (
          <div className="w-64 h-64 bg-gray-100 rounded-lg flex items-center justify-center">
            <Video className="w-16 h-16 text-gray-400" />
          </div>
        );
      
      case 'audio':
        return file.preview ? (
          <div className="w-full max-w-md">
            <audio src={file.preview} controls className="w-full" />
          </div>
        ) : (
          <div className="w-64 h-32 bg-gray-100 rounded-lg flex items-center justify-center">
            <Music className="w-16 h-16 text-gray-400" />
          </div>
        );
      
      default:
        return (
          <div className="w-64 h-64 bg-gray-100 rounded-lg flex flex-col items-center justify-center p-4">
            {getFileIcon(file.type)}
            <p className="mt-2 text-sm text-gray-600 text-center break-all">
              {file.name}
            </p>
            <p className="text-xs text-gray-500 mt-1">
              {formatFileSize(file.size)}
            </p>
          </div>
        );
    }
  };

  const handleSend = () => {
    if (!uploading && files.length > 0) {
      onSend();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl max-h-[90vh] w-full mx-4 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <h2 className="text-lg font-semibold text-gray-900">
              Preview Media ({files.length} file{files.length !== 1 ? 's' : ''})
            </h2>
            {files.length > 1 && (
              <span className="text-sm text-gray-500">
                {currentFileIndex + 1} of {files.length}
              </span>
            )}
          </div>
          <button
            onClick={onClose}
            disabled={uploading}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors disabled:opacity-50"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* File Navigation */}
          {files.length > 1 && (
            <div className="flex space-x-2 mb-4 overflow-x-auto pb-2">
              {files.map((file, index) => (
                <button
                  key={file.id}
                  onClick={() => setCurrentFileIndex(index)}
                  className={`flex-shrink-0 p-2 rounded-lg border-2 transition-colors ${
                    index === currentFileIndex
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="w-12 h-12 flex items-center justify-center">
                    {getFileIcon(file.type)}
                  </div>
                </button>
              ))}
            </div>
          )}

          {/* Main Preview */}
          <div className="flex flex-col items-center space-y-4">
            {renderFilePreview(currentFile)}
            
            {/* File Info */}
            <div className="text-center">
              <h3 className="font-medium text-gray-900 break-all">
                {currentFile.name}
              </h3>
              <p className="text-sm text-gray-500">
                {formatFileSize(currentFile.size)} • {currentFile.mimeType}
              </p>
            </div>

            {/* Remove File Button */}
            <button
              onClick={() => onRemoveFile(currentFile.id)}
              disabled={uploading}
              className="text-red-600 hover:text-red-700 text-sm disabled:opacity-50"
            >
              Remove this file
            </button>
          </div>

          {/* Upload Progress */}
          {uploading && (
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-blue-900">
                  Uploading files...
                </span>
                <span className="text-sm text-blue-700">{uploadProgress}%</span>
              </div>
              <div className="w-full bg-blue-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                />
              </div>
            </div>
          )}

          {/* Upload Error */}
          {uploadError && (
            <div className="mt-6 p-4 bg-red-50 rounded-lg">
              <div className="flex items-center space-x-2 text-red-800">
                <AlertCircle className="w-5 h-5" />
                <span className="font-medium">Upload failed</span>
              </div>
              <p className="text-sm text-red-700 mt-1">{uploadError}</p>
              {onRetry && (
                <button
                  onClick={onRetry}
                  className="mt-2 flex items-center space-x-1 text-sm text-red-700 hover:text-red-800"
                >
                  <RotateCcw className="w-4 h-4" />
                  <span>Retry upload</span>
                </button>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            {files.length} file{files.length !== 1 ? 's' : ''} selected
          </div>
          <div className="flex space-x-2">
            <button
              onClick={onClose}
              disabled={uploading}
              className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSend}
              disabled={uploading || files.length === 0}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <Send className="w-4 h-4" />
              <span>{uploading ? 'Sending...' : 'Send'}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MediaPreviewModal;
