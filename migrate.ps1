# migrate.ps1
$ErrorActionPreference = "Stop"

# Paths
$BackendDir = "backend"
$SocketServerDir = "socket-server"
$VenvActivate = "$BackendDir\venv\Scripts\activate.ps1"

Write-Output "🚀 Activating Django virtual environment..."
& $VenvActivate

Write-Output "📦 Applying Django migrations..."
cd $BackendDir
$env:ALLOW_MIGRATION = "migrate.ps1"
python manage.py migrate

Write-Output "🔄 Syncing Prisma schema..."
cd ..\$SocketServerDir
npx prisma db pull
npx prisma generate

Write-Output "✅ Migration completed successfully via migrate.ps1"
