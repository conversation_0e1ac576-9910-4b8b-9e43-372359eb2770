import { Page, Locator, expect } from '@playwright/test';

export class LoginPage {
  readonly page: Page;
  readonly emailInput: Locator;
  readonly passwordInput: Locator;
  readonly showPasswordButton: Locator;
  readonly loginButton: Locator;
  readonly registerLink: Locator;
  readonly errorMessage: Locator;
  readonly loadingSpinner: Locator;

  constructor(page: Page) {
    this.page = page;
    this.emailInput = page.locator('[data-testid="email-input"], input[type="email"]');
    this.passwordInput = page.locator('[data-testid="password-input"], input[type="password"]');
    this.showPasswordButton = page.locator('[data-testid="show-password-button"]');
    this.loginButton = page.locator('[data-testid="login-button"], button[type="submit"]');
    this.registerLink = page.locator('[data-testid="register-link"], a[href="/register"]');
    this.errorMessage = page.locator('[data-testid="error-message"], .error, .alert-error');
    this.loadingSpinner = page.locator('[data-testid="loading-spinner"], .loading, .spinner');
  }

  async goto() {
    await this.page.goto('/login');
    await this.waitForPageLoad();
  }

  async waitForPageLoad() {
    await expect(this.emailInput).toBeVisible();
    await expect(this.passwordInput).toBeVisible();
    await expect(this.loginButton).toBeVisible();
  }

  async fillEmail(email: string) {
    await this.emailInput.fill(email);
  }

  async fillPassword(password: string) {
    await this.passwordInput.fill(password);
  }

  async togglePasswordVisibility() {
    if (await this.showPasswordButton.isVisible()) {
      await this.showPasswordButton.click();
    }
  }

  async clickLogin() {
    await this.loginButton.click();
  }

  async clickRegisterLink() {
    await this.registerLink.click();
  }

  async login(email: string, password: string) {
    await this.fillEmail(email);
    await this.fillPassword(password);
    await this.clickLogin();
  }

  async waitForLoginSuccess() {
    // Wait for redirect to dashboard
    await this.page.waitForURL('**/dashboard', { timeout: 10000 });
  }

  async waitForLoginError() {
    await expect(this.errorMessage).toBeVisible({ timeout: 5000 });
  }

  async getErrorMessage(): Promise<string> {
    await this.waitForLoginError();
    return await this.errorMessage.textContent() || '';
  }

  async isLoading(): Promise<boolean> {
    return await this.loadingSpinner.isVisible();
  }

  async waitForLoadingToFinish() {
    if (await this.isLoading()) {
      await expect(this.loadingSpinner).toBeHidden({ timeout: 10000 });
    }
  }

  // Validation helpers
  async expectEmailValidationError() {
    const emailField = this.emailInput;
    await expect(emailField).toHaveAttribute('aria-invalid', 'true');
  }

  async expectPasswordValidationError() {
    const passwordField = this.passwordInput;
    await expect(passwordField).toHaveAttribute('aria-invalid', 'true');
  }

  async expectLoginButtonDisabled() {
    await expect(this.loginButton).toBeDisabled();
  }

  async expectLoginButtonEnabled() {
    await expect(this.loginButton).toBeEnabled();
  }

  // Form state helpers
  async clearForm() {
    await this.emailInput.clear();
    await this.passwordInput.clear();
  }

  async getEmailValue(): Promise<string> {
    return await this.emailInput.inputValue();
  }

  async getPasswordValue(): Promise<string> {
    return await this.passwordInput.inputValue();
  }

  async isPasswordVisible(): Promise<boolean> {
    const type = await this.passwordInput.getAttribute('type');
    return type === 'text';
  }

  // Accessibility helpers
  async checkAccessibility() {
    // Check for proper labels
    await expect(this.emailInput).toHaveAttribute('aria-label');
    await expect(this.passwordInput).toHaveAttribute('aria-label');
    
    // Check for proper form structure
    await expect(this.page.locator('form')).toBeVisible();
  }

  // Security helpers
  async checkPasswordMasking() {
    await this.fillPassword('test123');
    const type = await this.passwordInput.getAttribute('type');
    expect(type).toBe('password');
  }

  async testXSSPrevention() {
    const xssPayload = '<script>alert("xss")</script>';
    await this.fillEmail(xssPayload);
    await this.fillPassword(xssPayload);
    
    // Verify the script is not executed
    const emailValue = await this.getEmailValue();
    const passwordValue = await this.getPasswordValue();
    
    expect(emailValue).toBe(xssPayload); // Should be stored as text, not executed
    expect(passwordValue).toBe(xssPayload);
  }
}
