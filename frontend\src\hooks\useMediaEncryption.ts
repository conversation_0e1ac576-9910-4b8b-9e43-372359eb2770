// frontend/src/hooks/useMediaEncryption.ts
import { useCallback } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../store';

interface EncryptionResult {
  encryptedData: ArrayBuffer;
  nonce: string;
  wrappedFileKey: string;
  fileHash: string;
}

interface EncryptionProgress {
  stage: 'hashing' | 'encrypting' | 'wrapping';
  progress: number;
}

export const useMediaEncryption = (conversationId: string) => {
  // Get conversation session from encryption store
  const conversationSession = useSelector((state: RootState) => 
    state.encryption.sessions[conversationId]
  );
  
  // Derive conversation key from session's root key
  const conversationKey = conversationSession?.rootKey;

  const generateFileKey = useCallback((): Uint8Array => {
    return crypto.getRandomValues(new Uint8Array(32));
  }, []);

  const generateNonce = useCallback((): Uint8Array => {
    return crypto.getRandomValues(new Uint8Array(12));
  }, []);

  const calculateFileHash = useCallback(async (file: File): Promise<string> => {
    const buffer = await file.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
    return Array.from(new Uint8Array(hashBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }, []);

  const encryptFile = useCallback(async (
    file: File,
    onProgress?: (progress: EncryptionProgress) => void
  ): Promise<EncryptionResult> => {
    if (!conversationKey) {
      throw new Error('Conversation key not available');
    }

    try {
      // Step 1: Calculate file hash
      onProgress?.({ stage: 'hashing', progress: 0 });
      const fileHash = await calculateFileHash(file);
      onProgress?.({ stage: 'hashing', progress: 100 });

      // Step 2: Generate file key and nonce
      const fileKey = generateFileKey();
      const nonce = generateNonce();

      // Step 3: Encrypt file content
      onProgress?.({ stage: 'encrypting', progress: 0 });
      
      const fileBuffer = await file.arrayBuffer();
      
      // Import file key for AES-GCM
      const cryptoKey = await crypto.subtle.importKey(
        'raw',
        fileKey,
        { name: 'AES-GCM' },
        false,
        ['encrypt']
      );

      // Encrypt the file
      const encryptedData = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: nonce,
        },
        cryptoKey,
        fileBuffer
      );

      onProgress?.({ stage: 'encrypting', progress: 100 });

      // Step 4: Wrap file key with conversation key
      onProgress?.({ stage: 'wrapping', progress: 0 });
      
      // Convert conversation key from base64 if needed
      let conversationKeyBytes: Uint8Array;
      if (typeof conversationKey === 'string') {
        conversationKeyBytes = new Uint8Array(
          atob(conversationKey).split('').map(c => c.charCodeAt(0))
        );
      } else {
        conversationKeyBytes = conversationKey;
      }

      // Import conversation key
      const conversationCryptoKey = await crypto.subtle.importKey(
        'raw',
        conversationKeyBytes,
        { name: 'AES-GCM' },
        false,
        ['encrypt']
      );

      // Generate nonce for key wrapping
      const keyWrapNonce = crypto.getRandomValues(new Uint8Array(12));
      
      // Encrypt file key with conversation key
      const wrappedKeyBuffer = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: keyWrapNonce,
        },
        conversationCryptoKey,
        fileKey
      );

      // Combine nonce and wrapped key
      const wrappedKeyWithNonce = new Uint8Array(keyWrapNonce.length + wrappedKeyBuffer.byteLength);
      wrappedKeyWithNonce.set(keyWrapNonce);
      wrappedKeyWithNonce.set(new Uint8Array(wrappedKeyBuffer), keyWrapNonce.length);

      const wrappedFileKey = btoa(String.fromCharCode(...wrappedKeyWithNonce));
      const nonceBase64 = btoa(String.fromCharCode(...nonce));

      onProgress?.({ stage: 'wrapping', progress: 100 });

      return {
        encryptedData,
        nonce: nonceBase64,
        wrappedFileKey,
        fileHash,
      };

    } catch (error) {
      throw new Error(`Encryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [conversationKey, calculateFileHash, generateFileKey, generateNonce]);

  const decryptFile = useCallback(async (
    encryptedData: ArrayBuffer,
    nonce: string,
    wrappedFileKey: string
  ): Promise<ArrayBuffer> => {
    if (!conversationKey) {
      throw new Error('Conversation key not available');
    }

    try {
      // Convert conversation key from base64 if needed
      let conversationKeyBytes: Uint8Array;
      if (typeof conversationKey === 'string') {
        conversationKeyBytes = new Uint8Array(
          atob(conversationKey).split('').map(c => c.charCodeAt(0))
        );
      } else {
        conversationKeyBytes = conversationKey;
      }

      // Import conversation key
      const conversationCryptoKey = await crypto.subtle.importKey(
        'raw',
        conversationKeyBytes,
        { name: 'AES-GCM' },
        false,
        ['decrypt']
      );

      // Decode wrapped key
      const wrappedKeyWithNonce = new Uint8Array(
        atob(wrappedFileKey).split('').map(c => c.charCodeAt(0))
      );

      // Extract nonce and wrapped key
      const keyWrapNonce = wrappedKeyWithNonce.slice(0, 12);
      const wrappedKeyBuffer = wrappedKeyWithNonce.slice(12);

      // Decrypt file key
      const fileKeyBuffer = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: keyWrapNonce,
        },
        conversationCryptoKey,
        wrappedKeyBuffer
      );

      // Import file key
      const fileCryptoKey = await crypto.subtle.importKey(
        'raw',
        fileKeyBuffer,
        { name: 'AES-GCM' },
        false,
        ['decrypt']
      );

      // Decode nonce
      const fileNonce = new Uint8Array(
        atob(nonce).split('').map(c => c.charCodeAt(0))
      );

      // Decrypt file
      const decryptedData = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: fileNonce,
        },
        fileCryptoKey,
        encryptedData
      );

      return decryptedData;

    } catch (error) {
      throw new Error(`Decryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [conversationKey]);

  return {
    encryptFile,
    decryptFile,
    generateFileKey,
    generateNonce,
    calculateFileHash,
    isReady: !!conversationKey,
  };
};
