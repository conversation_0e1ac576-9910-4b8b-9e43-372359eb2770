// frontend/src/components/Chat/MessageInput.tsx
import React, { useState, useRef } from 'react';
import { useSelector } from 'react-redux';
import { Button } from '../ui/Button';
import { Icon } from '../ui/Icon';
import { useSocket } from '../../contexts/SocketContext';
import SimpleMediaUpload from './SimpleMediaUpload';
import type { RootState } from '../../store';
import type { UploadProgress } from '../../services/mediaApi';

interface MessageInputProps {
  conversationId: string;
  disabled?: boolean;
  replyTo?: {
    id: string;
    content: string;
    sender: string;
  } | null;
  onCancelReply?: () => void;
}

const MessageInput: React.FC<MessageInputProps> = ({
  conversationId,
  disabled = false,
  replyTo,
  onCancelReply,
}) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [uploadingFiles, setUploadingFiles] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const typingTimeoutRef = useRef<number | undefined>(undefined);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { sendMessage, startTyping, stopTyping, isConnected } = useSocket();
  const loading = useSelector((state: RootState) => state.messages.loading);

  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = event.target.value;
    setMessage(value);

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }

    // Handle typing indicators
    if (value && !isTyping && isConnected) {
      setIsTyping(true);
      startTyping(conversationId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = window.setTimeout(() => {
      if (isTyping && isConnected) {
        setIsTyping(false);
        stopTyping(conversationId);
      }
    }, 1000);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (message.trim() && !disabled && isConnected) {
      // Send message through SocketContext with optimistic update
      await sendMessage(conversationId, message.trim());

      setMessage('');

      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }

      // Stop typing indicator
      if (isTyping) {
        setIsTyping(false);
        stopTyping(conversationId);
      }

      // Clear timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSubmit(event);
    }
  };

  // Media upload handlers
  const handleUploadStart = (file: File) => {
    const fileId = `${file.name}_${Date.now()}`;
    setUploadingFiles(prev => [...prev, fileId]);
    setUploadProgress(prev => ({ ...prev, [fileId]: 0 }));
  };

  const handleUploadProgress = (progress: UploadProgress) => {
    // Update progress for the current file
    // Note: In a real implementation, you'd need to track which file this progress belongs to
    setUploadProgress(prev => {
      const fileId = Object.keys(prev)[Object.keys(prev).length - 1];
      return { ...prev, [fileId]: progress.percentage };
    });
  };

  const handleUploadComplete = async (uploadResults: any[]) => {
    // Remove from uploading files
    setUploadingFiles(prev => prev.slice(0, -1));

    // Clear progress
    setUploadProgress(prev => {
      const newProgress = { ...prev };
      const fileId = Object.keys(newProgress)[Object.keys(newProgress).length - 1];
      delete newProgress[fileId];
      return newProgress;
    });

    console.log('Media upload completed:', uploadResults);
    
    // The backend auto-creates messages for media uploads
    // The socket system will automatically receive these messages through the SocketContext
    setTimeout(() => {
      console.log('✅ Media upload integration completed');
    }, 100);
  };

  const handleUploadError = (error: string) => {
    console.error('Media upload error:', error);

    // Remove from uploading files
    setUploadingFiles(prev => prev.slice(0, -1));

    // Clear progress
    setUploadProgress(prev => {
      const newProgress = { ...prev };
      const fileId = Object.keys(newProgress)[Object.keys(newProgress).length - 1];
      delete newProgress[fileId];
      return newProgress;
    });
  };

  return (
    <div className="border-t border-gray-200 bg-white p-4">
      {/* Connection status indicator */}
      {!isConnected && (
        <div className="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center space-x-2 text-yellow-800 text-sm">
            <Icon name="alert-triangle" size={16} />
            <span>Reconnecting to chat server...</span>
          </div>
        </div>
      )}

      {/* Reply indicator */}
      {replyTo && (
        <div className="mb-3 p-3 bg-gray-50 rounded-lg border-l-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium text-gray-900">
                Replying to {replyTo.sender}
              </div>
              <div className="text-sm text-gray-600 truncate">
                {replyTo.content}
              </div>
            </div>
            {onCancelReply && (
              <button
                onClick={onCancelReply}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <Icon name="x" size={16} />
              </button>
            )}
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="flex items-end space-x-3">
        {/* Simple Media upload component */}
        <div className="flex-shrink-0 relative">
          <SimpleMediaUpload
            conversationId={conversationId}
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
            disabled={disabled || !isConnected}
          />
        </div>

        {/* Message input */}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={isConnected ? "Type a message..." : "Connecting..."}
            disabled={disabled || loading || !isConnected}
            rows={1}
            className="w-full resize-none rounded-lg border border-gray-300 px-4 py-2 text-sm placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-50 disabled:cursor-not-allowed"
            style={{ minHeight: '40px', maxHeight: '120px' }}
            data-testid="message-input"
          />
        </div>

        {/* Send button */}
        <Button
          type="submit"
          disabled={!message.trim() || disabled || loading || !isConnected}
          className="flex-shrink-0"
          data-testid="send-button"
        >
          <Icon name="send" size={16} />
          <span className="ml-2">Send</span>
        </Button>
      </form>
    </div>
  );
};

export default MessageInput;
