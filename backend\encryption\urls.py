# backend/encryption/urls.py
from django.urls import path
from . import views

app_name = 'encryption'

urlpatterns = [
    # Key bundle management
    path('bundles/', views.upload_key_bundle, name='upload_key_bundle'),
    path('bundles/<uuid:user_id>/', views.get_key_bundle, name='get_key_bundle'),
    
    # One-time pre-key management
    path('prekeys/', views.upload_one_time_prekeys, name='upload_one_time_prekeys'),
    path('prekeys/count/', views.get_prekey_count, name='get_prekey_count'),
]
