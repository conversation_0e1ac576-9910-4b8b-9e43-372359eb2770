import { Page, Locator, expect } from '@playwright/test';

export class RegisterPage {
  readonly page: Page;
  readonly firstNameInput: Locator;
  readonly lastNameInput: Locator;
  readonly usernameInput: Locator;
  readonly emailInput: Locator;
  readonly passwordInput: Locator;
  readonly confirmPasswordInput: Locator;
  readonly showPasswordButton: Locator;
  readonly registerButton: Locator;
  readonly loginLink: Locator;
  readonly errorMessage: Locator;
  readonly successMessage: Locator;
  readonly loadingSpinner: Locator;

  constructor(page: Page) {
    this.page = page;
    this.firstNameInput = page.locator('[data-testid="first-name-input"], input[name="firstName"]');
    this.lastNameInput = page.locator('[data-testid="last-name-input"], input[name="lastName"]');
    this.usernameInput = page.locator('[data-testid="username-input"], input[name="username"]');
    this.emailInput = page.locator('[data-testid="email-input"], input[type="email"]');
    this.passwordInput = page.locator('[data-testid="password-input"], input[name="password"]');
    this.confirmPasswordInput = page.locator('[data-testid="confirm-password-input"], input[name="confirmPassword"]');
    this.showPasswordButton = page.locator('[data-testid="show-password-button"]');
    this.registerButton = page.locator('[data-testid="register-button"], button[type="submit"]');
    this.loginLink = page.locator('[data-testid="login-link"], a[href="/login"]');
    this.errorMessage = page.locator('[data-testid="error-message"], .error, .alert-error');
    this.successMessage = page.locator('[data-testid="success-message"], .success, .alert-success');
    this.loadingSpinner = page.locator('[data-testid="loading-spinner"], .loading, .spinner');
  }

  async goto() {
    await this.page.goto('/register');
    await this.waitForPageLoad();
  }

  async waitForPageLoad() {
    await expect(this.firstNameInput).toBeVisible();
    await expect(this.lastNameInput).toBeVisible();
    await expect(this.emailInput).toBeVisible();
    await expect(this.passwordInput).toBeVisible();
    await expect(this.registerButton).toBeVisible();
  }

  async fillFirstName(firstName: string) {
    await this.firstNameInput.fill(firstName);
  }

  async fillLastName(lastName: string) {
    await this.lastNameInput.fill(lastName);
  }

  async fillUsername(username: string) {
    if (await this.usernameInput.isVisible()) {
      await this.usernameInput.fill(username);
    }
  }

  async fillEmail(email: string) {
    await this.emailInput.fill(email);
  }

  async fillPassword(password: string) {
    await this.passwordInput.fill(password);
  }

  async fillConfirmPassword(confirmPassword: string) {
    await this.confirmPasswordInput.fill(confirmPassword);
  }

  async togglePasswordVisibility() {
    if (await this.showPasswordButton.isVisible()) {
      await this.showPasswordButton.click();
    }
  }

  async clickRegister() {
    await this.registerButton.click();
  }

  async clickLoginLink() {
    await this.loginLink.click();
  }

  async register(userData: {
    firstName: string;
    lastName: string;
    username?: string;
    email: string;
    password: string;
    confirmPassword?: string;
  }) {
    await this.fillFirstName(userData.firstName);
    await this.fillLastName(userData.lastName);
    
    if (userData.username) {
      await this.fillUsername(userData.username);
    }
    
    await this.fillEmail(userData.email);
    await this.fillPassword(userData.password);
    
    if (userData.confirmPassword !== undefined) {
      await this.fillConfirmPassword(userData.confirmPassword);
    } else {
      await this.fillConfirmPassword(userData.password);
    }
    
    await this.clickRegister();
  }

  async waitForRegistrationSuccess() {
    // Wait for redirect to dashboard or success message
    try {
      await this.page.waitForURL('**/dashboard', { timeout: 10000 });
    } catch {
      // If no redirect, check for success message
      await expect(this.successMessage).toBeVisible({ timeout: 5000 });
    }
  }

  async waitForRegistrationError() {
    await expect(this.errorMessage).toBeVisible({ timeout: 5000 });
  }

  async getErrorMessage(): Promise<string> {
    await this.waitForRegistrationError();
    return await this.errorMessage.textContent() || '';
  }

  async getSuccessMessage(): Promise<string> {
    await expect(this.successMessage).toBeVisible({ timeout: 5000 });
    return await this.successMessage.textContent() || '';
  }

  async isLoading(): Promise<boolean> {
    return await this.loadingSpinner.isVisible();
  }

  async waitForLoadingToFinish() {
    if (await this.isLoading()) {
      await expect(this.loadingSpinner).toBeHidden({ timeout: 10000 });
    }
  }

  // Validation helpers
  async expectFieldValidationError(field: 'firstName' | 'lastName' | 'username' | 'email' | 'password' | 'confirmPassword') {
    const fieldMap = {
      firstName: this.firstNameInput,
      lastName: this.lastNameInput,
      username: this.usernameInput,
      email: this.emailInput,
      password: this.passwordInput,
      confirmPassword: this.confirmPasswordInput
    };
    
    const fieldLocator = fieldMap[field];
    await expect(fieldLocator).toHaveAttribute('aria-invalid', 'true');
  }

  async expectRegisterButtonDisabled() {
    await expect(this.registerButton).toBeDisabled();
  }

  async expectRegisterButtonEnabled() {
    await expect(this.registerButton).toBeEnabled();
  }

  // Form state helpers
  async clearForm() {
    await this.firstNameInput.clear();
    await this.lastNameInput.clear();
    if (await this.usernameInput.isVisible()) {
      await this.usernameInput.clear();
    }
    await this.emailInput.clear();
    await this.passwordInput.clear();
    await this.confirmPasswordInput.clear();
  }

  async getFormValues() {
    return {
      firstName: await this.firstNameInput.inputValue(),
      lastName: await this.lastNameInput.inputValue(),
      username: await this.usernameInput.isVisible() ? await this.usernameInput.inputValue() : '',
      email: await this.emailInput.inputValue(),
      password: await this.passwordInput.inputValue(),
      confirmPassword: await this.confirmPasswordInput.inputValue()
    };
  }

  async isPasswordVisible(): Promise<boolean> {
    const type = await this.passwordInput.getAttribute('type');
    return type === 'text';
  }

  // Validation testing helpers
  async testEmailValidation() {
    const invalidEmails = [
      'invalid-email',
      '@example.com',
      'test@',
      'test.example.com',
      ''
    ];

    for (const email of invalidEmails) {
      await this.clearForm();
      await this.fillEmail(email);
      await this.fillFirstName('Test');
      await this.fillLastName('User');
      await this.fillPassword('Password123!');
      await this.fillConfirmPassword('Password123!');
      
      // Try to submit and expect validation error
      await this.clickRegister();
      await this.expectFieldValidationError('email');
    }
  }

  async testPasswordValidation() {
    const weakPasswords = [
      '123',
      'password',
      'PASSWORD',
      'Pass123',
      ''
    ];

    for (const password of weakPasswords) {
      await this.clearForm();
      await this.fillFirstName('Test');
      await this.fillLastName('User');
      await this.fillEmail('<EMAIL>');
      await this.fillPassword(password);
      await this.fillConfirmPassword(password);
      
      // Try to submit and expect validation error
      await this.clickRegister();
      await this.expectFieldValidationError('password');
    }
  }

  async testPasswordConfirmation() {
    await this.clearForm();
    await this.fillFirstName('Test');
    await this.fillLastName('User');
    await this.fillEmail('<EMAIL>');
    await this.fillPassword('Password123!');
    await this.fillConfirmPassword('DifferentPassword123!');
    
    await this.clickRegister();
    await this.expectFieldValidationError('confirmPassword');
  }

  async testRequiredFields() {
    const requiredFields = ['firstName', 'lastName', 'email', 'password'] as const;
    
    for (const field of requiredFields) {
      await this.clearForm();
      
      // Fill all fields except the one being tested
      if (field !== 'firstName') await this.fillFirstName('Test');
      if (field !== 'lastName') await this.fillLastName('User');
      if (field !== 'email') await this.fillEmail('<EMAIL>');
      if (field !== 'password') {
        await this.fillPassword('Password123!');
        await this.fillConfirmPassword('Password123!');
      }
      
      await this.clickRegister();
      await this.expectFieldValidationError(field);
    }
  }

  // Security helpers
  async testXSSPrevention() {
    const xssPayload = '<script>alert("xss")</script>';
    
    await this.register({
      firstName: xssPayload,
      lastName: xssPayload,
      username: xssPayload,
      email: '<EMAIL>',
      password: 'Password123!',
      confirmPassword: 'Password123!'
    });
    
    // Verify the script is not executed and values are properly escaped
    const formValues = await this.getFormValues();
    expect(formValues.firstName).toBe(xssPayload);
    expect(formValues.lastName).toBe(xssPayload);
  }

  // Accessibility helpers
  async checkAccessibility() {
    // Check for proper labels
    await expect(this.firstNameInput).toHaveAttribute('aria-label');
    await expect(this.lastNameInput).toHaveAttribute('aria-label');
    await expect(this.emailInput).toHaveAttribute('aria-label');
    await expect(this.passwordInput).toHaveAttribute('aria-label');
    await expect(this.confirmPasswordInput).toHaveAttribute('aria-label');
    
    // Check for proper form structure
    await expect(this.page.locator('form')).toBeVisible();
  }
}
