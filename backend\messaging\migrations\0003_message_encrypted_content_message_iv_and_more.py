# Generated by Django 5.2.4 on 2025-08-27 05:13

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('messaging', '0002_messagestatus'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='message',
            name='encrypted_content',
            field=models.TextField(blank=True, help_text='AES-GCM encrypted message content (base64 encoded)'),
        ),
        migrations.AddField(
            model_name='message',
            name='iv',
            field=models.TextField(blank=True, help_text='96-bit initialization vector (base64 encoded)'),
        ),
        migrations.AddField(
            model_name='message',
            name='message_number',
            field=models.IntegerField(default=0, help_text='Message counter in Double Ratchet (N)'),
        ),
        migrations.AddField(
            model_name='message',
            name='previous_chain_length',
            field=models.IntegerField(default=0, help_text='Previous chain length in Double Ratchet (PN)'),
        ),
        migrations.AddField(
            model_name='message',
            name='sender_ratchet_key',
            field=models.TextField(blank=True, help_text="Sender's current DH ratchet public key (SPKI format, base64)"),
        ),
        migrations.AlterField(
            model_name='message',
            name='content',
            field=models.TextField(blank=True, help_text='Plaintext content (Phase 2 compatibility, empty for encrypted messages)'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['conversation', 'created_at'], name='messages_convers_3ebb41_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['sender', 'created_at'], name='messages_sender__bf8b1c_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['conversation', 'message_number'], name='messages_convers_a829c8_idx'),
        ),
    ]
