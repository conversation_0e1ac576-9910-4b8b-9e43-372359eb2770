// frontend/src/crypto/webCrypto.ts
/**
 * WebCrypto utilities for Phase 3 end-to-end encryption.
 * Implements ECDSA P-256 (identity keys) and ECDH P-256 (pre-keys) operations.
 */

import type {
  KeyPair,
  SerializedKeyPair,
  IdentityKeyPair,
  PreKeyPair,
  EncryptionResult,
  DecryptionParams,
  EncryptionError,
  EncryptionErrorCode,
  Base64String
} from '../types/encryption';

// ============================================================================
// Constants and Configuration
// ============================================================================

export const CRYPTO_CONFIG = {
  // ECDSA P-256 for identity keys (signing)
  IDENTITY_KEY_ALGORITHM: {
    name: 'ECDSA',
    namedCurve: 'P-256'
  } as EcKeyGenParams,
  
  // ECDH P-256 for pre-keys (key exchange)
  PREKEY_ALGORITHM: {
    name: 'ECDH',
    namedCurve: 'P-256'
  } as EcKeyGenParams,
  
  // Signature algorithm
  SIGNATURE_ALGORITHM: {
    name: 'ECDSA',
    hash: 'SHA-256'
  } as EcdsaParams,
  
  // AES-GCM for message encryption
  AES_ALGORITHM: {
    name: 'AES-GCM',
    length: 256
  } as AesKeyGenParams,
  
  // IV length for AES-GCM (96 bits = 12 bytes)
  IV_LENGTH: 12,
  
  // Key derivation
  HKDF_ALGORITHM: {
    name: 'HKDF',
    hash: 'SHA-256'
  } as HkdfParams
} as const;

// ============================================================================
// Error Handling
// ============================================================================

export class CryptoError extends Error {
  constructor(
    public code: EncryptionErrorCode,
    message: string,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'CryptoError';
  }
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Convert ArrayBuffer to Base64 string
 */
export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}

/**
 * Convert Base64 string to ArrayBuffer
 */
export function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binary = atob(base64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
}

/**
 * Generate cryptographically secure random bytes
 */
export function generateRandomBytes(length: number): ArrayBuffer {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return array.buffer;
}

// ============================================================================
// Key Generation
// ============================================================================

/**
 * Generate ECDSA P-256 identity key pair for signing
 */
export async function generateIdentityKeyPair(): Promise<IdentityKeyPair> {
  try {
    const keyPair = await crypto.subtle.generateKey(
      CRYPTO_CONFIG.IDENTITY_KEY_ALGORITHM,
      true, // extractable
      ['sign', 'verify']
    );
    
    return keyPair as IdentityKeyPair;
  } catch (error) {
    throw new CryptoError(
      'KEY_GENERATION_FAILED',
      'Failed to generate identity key pair',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Generate ECDH P-256 pre-key pair for key exchange
 */
export async function generatePreKeyPair(): Promise<PreKeyPair> {
  try {
    const keyPair = await crypto.subtle.generateKey(
      CRYPTO_CONFIG.PREKEY_ALGORITHM,
      true, // extractable
      ['deriveKey', 'deriveBits']
    );
    
    return keyPair as PreKeyPair;
  } catch (error) {
    throw new CryptoError(
      'KEY_GENERATION_FAILED',
      'Failed to generate pre-key pair',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Generate AES-GCM key for message encryption
 */
export async function generateAESKey(): Promise<CryptoKey> {
  try {
    return await crypto.subtle.generateKey(
      CRYPTO_CONFIG.AES_ALGORITHM,
      true, // extractable
      ['encrypt', 'decrypt']
    );
  } catch (error) {
    throw new CryptoError(
      'KEY_GENERATION_FAILED',
      'Failed to generate AES key',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

// ============================================================================
// Key Import/Export
// ============================================================================

/**
 * Export public key to SPKI format (base64 encoded)
 */
export async function exportPublicKey(publicKey: CryptoKey): Promise<Base64String> {
  try {
    const exported = await crypto.subtle.exportKey('spki', publicKey);
    return arrayBufferToBase64(exported);
  } catch (error) {
    throw new CryptoError(
      'KEY_EXPORT_FAILED',
      'Failed to export public key',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Export private key to PKCS#8 format (base64 encoded)
 */
export async function exportPrivateKey(privateKey: CryptoKey): Promise<Base64String> {
  try {
    const exported = await crypto.subtle.exportKey('pkcs8', privateKey);
    return arrayBufferToBase64(exported);
  } catch (error) {
    throw new CryptoError(
      'KEY_EXPORT_FAILED',
      'Failed to export private key',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Export key pair to serialized format
 */
export async function exportKeyPair(keyPair: KeyPair): Promise<SerializedKeyPair> {
  const [publicKey, privateKey] = await Promise.all([
    exportPublicKey(keyPair.publicKey),
    exportPrivateKey(keyPair.privateKey)
  ]);
  
  return { publicKey, privateKey };
}

/**
 * Import public key from SPKI format
 */
export async function importPublicKey(
  publicKeyData: Base64String,
  algorithm: 'ECDSA' | 'ECDH',
  usages: KeyUsage[]
): Promise<CryptoKey> {
  try {
    const keyData = base64ToArrayBuffer(publicKeyData);
    const algorithmParams = algorithm === 'ECDSA' 
      ? CRYPTO_CONFIG.IDENTITY_KEY_ALGORITHM 
      : CRYPTO_CONFIG.PREKEY_ALGORITHM;
    
    return await crypto.subtle.importKey(
      'spki',
      keyData,
      algorithmParams,
      false, // not extractable
      usages
    );
  } catch (error) {
    throw new CryptoError(
      'KEY_IMPORT_FAILED',
      'Failed to import public key',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Import private key from PKCS#8 format
 */
export async function importPrivateKey(
  privateKeyData: Base64String,
  algorithm: 'ECDSA' | 'ECDH',
  usages: KeyUsage[]
): Promise<CryptoKey> {
  try {
    const keyData = base64ToArrayBuffer(privateKeyData);
    const algorithmParams = algorithm === 'ECDSA' 
      ? CRYPTO_CONFIG.IDENTITY_KEY_ALGORITHM 
      : CRYPTO_CONFIG.PREKEY_ALGORITHM;
    
    return await crypto.subtle.importKey(
      'pkcs8',
      keyData,
      algorithmParams,
      true, // extractable
      usages
    );
  } catch (error) {
    throw new CryptoError(
      'KEY_IMPORT_FAILED',
      'Failed to import private key',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Import key pair from serialized format
 */
export async function importKeyPair(
  serializedKeyPair: SerializedKeyPair,
  algorithm: 'ECDSA' | 'ECDH'
): Promise<KeyPair> {
  const usages: KeyUsage[] = algorithm === 'ECDSA' 
    ? ['sign', 'verify'] 
    : ['deriveKey', 'deriveBits'];
  
  const [publicKey, privateKey] = await Promise.all([
    importPublicKey(serializedKeyPair.publicKey, algorithm, algorithm === 'ECDSA' ? ['verify'] : []),
    importPrivateKey(serializedKeyPair.privateKey, algorithm, algorithm === 'ECDSA' ? ['sign'] : usages)
  ]);
  
  return { publicKey, privateKey };
}

// ============================================================================
// Digital Signatures
// ============================================================================

/**
 * Sign data with ECDSA private key
 */
export async function signData(
  data: ArrayBuffer,
  privateKey: CryptoKey
): Promise<Base64String> {
  try {
    const signature = await crypto.subtle.sign(
      CRYPTO_CONFIG.SIGNATURE_ALGORITHM,
      privateKey,
      data
    );
    
    return arrayBufferToBase64(signature);
  } catch (error) {
    throw new CryptoError(
      'SIGNATURE_FAILED',
      'Failed to sign data',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Verify signature with ECDSA public key
 */
export async function verifySignature(
  signature: Base64String,
  data: ArrayBuffer,
  publicKey: CryptoKey
): Promise<boolean> {
  try {
    const signatureBuffer = base64ToArrayBuffer(signature);
    
    return await crypto.subtle.verify(
      CRYPTO_CONFIG.SIGNATURE_ALGORITHM,
      publicKey,
      signatureBuffer,
      data
    );
  } catch (error) {
    throw new CryptoError(
      'SIGNATURE_VERIFICATION_FAILED',
      'Failed to verify signature',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

// ============================================================================
// Key Derivation
// ============================================================================

/**
 * Derive shared secret using ECDH
 */
export async function deriveSharedSecret(
  privateKey: CryptoKey,
  publicKey: CryptoKey
): Promise<ArrayBuffer> {
  try {
    return await crypto.subtle.deriveBits(
      {
        name: 'ECDH',
        public: publicKey
      },
      privateKey,
      256 // 256 bits = 32 bytes
    );
  } catch (error) {
    throw new CryptoError(
      'KEY_DERIVATION_FAILED',
      'Failed to derive shared secret',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Derive key using HKDF
 */
export async function deriveKey(
  sharedSecret: ArrayBuffer,
  salt: ArrayBuffer,
  info: ArrayBuffer,
  keyUsages: KeyUsage[] = ['encrypt', 'decrypt']
): Promise<CryptoKey> {
  try {
    // First import the shared secret as an HKDF key
    const baseKey = await crypto.subtle.importKey(
      'raw',
      sharedSecret,
      'HKDF',
      false,
      ['deriveKey']
    );
    
    // Then derive the AES key
    return await crypto.subtle.deriveKey(
      {
        name: 'HKDF',
        hash: 'SHA-256',
        salt,
        info
      },
      baseKey,
      CRYPTO_CONFIG.AES_ALGORITHM,
      false, // not extractable
      keyUsages
    );
  } catch (error) {
    throw new CryptoError(
      'KEY_DERIVATION_FAILED',
      'Failed to derive key with HKDF',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

// ============================================================================
// Message Encryption/Decryption
// ============================================================================

/**
 * Encrypt data using AES-GCM
 */
export async function encryptMessage(
  data: ArrayBuffer,
  key: CryptoKey,
  iv?: ArrayBuffer
): Promise<EncryptionResult> {
  try {
    const actualIv = iv || generateRandomBytes(CRYPTO_CONFIG.IV_LENGTH);

    const ciphertext = await crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: actualIv
      },
      key,
      data
    );

    return {
      ciphertext,
      iv: actualIv
    };
  } catch (error) {
    throw new CryptoError(
      'ENCRYPTION_FAILED',
      'Failed to encrypt message',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Decrypt data using AES-GCM
 */
export async function decryptMessage(params: DecryptionParams): Promise<ArrayBuffer> {
  try {
    return await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: params.iv
      },
      params.key,
      params.ciphertext
    );
  } catch (error) {
    throw new CryptoError(
      'DECRYPTION_FAILED',
      'Failed to decrypt message',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

// ============================================================================
// Utility Functions for Text Encoding
// ============================================================================

/**
 * Convert string to ArrayBuffer (UTF-8 encoding)
 */
export function stringToArrayBuffer(str: string): ArrayBuffer {
  const encoder = new TextEncoder();
  return encoder.encode(str).buffer;
}

/**
 * Convert ArrayBuffer to string (UTF-8 decoding)
 */
export function arrayBufferToString(buffer: ArrayBuffer): string {
  const decoder = new TextDecoder();
  return decoder.decode(buffer);
}

// ============================================================================
// High-Level Crypto Operations
// ============================================================================

/**
 * Generate and sign a pre-key with identity key
 */
export async function generateSignedPreKey(
  identityPrivateKey: CryptoKey,
  preKeyId: number
): Promise<{
  keyPair: PreKeyPair;
  signature: Base64String;
  publicKeySpki: Base64String;
}> {
  try {
    // Generate the pre-key pair
    const keyPair = await generatePreKeyPair();

    // Export public key to SPKI format
    const publicKeySpki = await exportPublicKey(keyPair.publicKey);

    // Sign the SPKI bytes with identity key
    const spkiBytes = base64ToArrayBuffer(publicKeySpki);
    const signature = await signData(spkiBytes, identityPrivateKey);

    return {
      keyPair,
      signature,
      publicKeySpki
    };
  } catch (error) {
    throw new CryptoError(
      'KEY_GENERATION_FAILED',
      'Failed to generate signed pre-key',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Verify a signed pre-key
 */
export async function verifySignedPreKey(
  publicKeySpki: Base64String,
  signature: Base64String,
  identityPublicKey: CryptoKey
): Promise<boolean> {
  try {
    const spkiBytes = base64ToArrayBuffer(publicKeySpki);
    return await verifySignature(signature, spkiBytes, identityPublicKey);
  } catch (error) {
    throw new CryptoError(
      'SIGNATURE_VERIFICATION_FAILED',
      'Failed to verify signed pre-key',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Check if WebCrypto is supported
 */
export function isWebCryptoSupported(): boolean {
  return typeof crypto !== 'undefined' &&
         typeof crypto.subtle !== 'undefined' &&
         typeof crypto.getRandomValues === 'function';
}

/**
 * Validate that a key has the expected algorithm
 */
export function validateKeyAlgorithm(key: CryptoKey, expectedAlgorithm: string): boolean {
  return key.algorithm.name === expectedAlgorithm;
}

/**
 * Get key algorithm info as string
 */
export function getKeyAlgorithmInfo(key: CryptoKey): string {
  const alg = key.algorithm as any;
  return `${alg.name}${alg.namedCurve ? `-${alg.namedCurve}` : ''}`;
}
