# Phase 3: End-to-End Encryption Implementation

**Duration**: 2–3 weeks  •  **Priority**: High  •  **Status**: Ready for Implementation

## Overview

This phase implements comprehensive end-to-end encryption for our messaging application, transforming it into a secure communication platform. Building upon our successful Phase 2 core messaging system, we'll add Signal protocol-based encryption that ensures only intended recipients can read message content.

**Key Goals:**
- End-to-end encryption using Signal protocol or compatible implementation
- Universal browser compatibility (Chrome, Edge, Firefox, Safari, mobile)
- Server never sees plaintext messages
- Seamless integration with existing Phase 2 messaging system
- Forward secrecy and self-healing through Double Ratchet
- Transparent user experience with automatic key management

---

## Research Findings & Architecture Decisions

### Modern E2EE Best Practices (2024)

Based on comprehensive research of current messaging security standards:

1. **Signal Protocol** remains the gold standard for messaging encryption
2. **WebCrypto API** has excellent browser support for P-256 ECDH/ECDSA and AES-GCM
3. **libsignal TypeScript package** provides official Signal implementation with WASM
4. **Dual implementation strategy** ensures maximum compatibility

### Browser Compatibility Strategy

**Primary Implementation**: `@signalapp/libsignal-client` (Official Signal TypeScript package)
- Full X3DH + Double Ratchet with Curve25519/Ed25519
- WASM-based for optimal security and performance
- Official Signal implementation with ongoing security updates

**Fallback Implementation**: WebCrypto P-256 Signal-compatible protocol
- ECDSA P-256 for identity/signing keys
- ECDH P-256 for signed pre-keys, one-time pre-keys, DH ratchets
- HKDF-SHA-256 for key derivation
- AES-GCM (256-bit) with 96-bit IVs for message encryption

**Supported Browser Matrix:**
- Chrome/Edge ≥ 79 (desktop/mobile)
- Firefox ≥ 78 (desktop/mobile)
- Safari ≥ 14 (desktop/mobile)
- iOS Safari ≥ 14.5
- Android Chrome ≥ 79
- Web Workers support for crypto operations

---

## System Architecture

### High-Level Design

**Client-Side (Frontend)**:
- Manages all cryptographic operations (key generation, encryption, decryption)
- Stores keys securely in IndexedDB with encryption
- Performs X3DH key exchange and Double Ratchet operations
- Uses Web Workers for crypto operations to maintain UI responsiveness

**Server-Side (Backend)**:
- Blind relay and storage only
- Stores public key bundles and encrypted message payloads
- Verifies signatures on uploaded key bundles
- Never receives or stores plaintext messages or private keys

### Core Components

#### 1. Key Manager (Client)
- **Identity Keys**: Long-term ECDSA P-256 signing keys for user identity
- **Signed Pre-Keys**: Medium-term ECDH P-256 keys signed by identity key
- **One-Time Pre-Keys**: Short-term ECDH P-256 keys for perfect forward secrecy
- **Key Rotation**: Automatic rotation of signed pre-keys and replenishment of one-time pre-keys

#### 2. Session Manager (Client)
- **X3DH Key Exchange**: Establishes shared secrets between conversation participants
- **Double Ratchet**: Provides forward secrecy and self-healing properties
- **Session State**: Manages root keys, chain keys, and message counters per conversation
- **Out-of-Order Handling**: Manages skipped message keys for reliable decryption

#### 3. Message Service (Client)
- **AES-GCM Encryption**: 256-bit keys with 96-bit IVs for message content
- **Associated Data**: Includes conversationId, senderId, counters to prevent context confusion
- **Message Keys**: Derived from Double Ratchet chain keys for each message

#### 4. Storage Layer (Client)
- **IndexedDB**: Encrypted storage for keys, sessions, and message keys
- **Master Key**: Derived from user session for encrypting stored data
- **Session Persistence**: Maintains encryption state across browser sessions

#### 5. Server APIs (Backend)
- **Key Bundle Management**: Upload/download public key bundles with signature verification
- **Message Routing**: Store and forward encrypted message payloads
- **Pre-Key Management**: Handle one-time pre-key consumption and replenishment

---

## Database Schema Updates

### Integration with Existing Phase 2 Models

Our encryption implementation builds upon the existing Phase 2 messaging models while adding new encryption-specific models and updating the Message model for encrypted content.

### New Encryption Models

#### UserKeyBundle Model
```python
# backend/apps/encryption/models.py
class UserKeyBundle(models.Model):
    """Stores user's public key bundle for key exchange"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='key_bundle')
    identity_public_key = models.TextField()  # ECDSA P-256 public key (SPKI base64)
    signed_prekey_id = models.IntegerField()
    signed_prekey_public = models.TextField()  # ECDH P-256 public key (SPKI base64)
    signed_prekey_signature = models.TextField()  # ECDSA signature (base64)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_key_bundles'
```

#### OneTimePreKey Model
```python
class OneTimePreKey(models.Model):
    """One-time pre-keys for perfect forward secrecy"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='one_time_prekeys')
    key_id = models.IntegerField()
    public_key = models.TextField()  # ECDH P-256 public key (SPKI base64)
    is_used = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    used_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'one_time_prekeys'
        unique_together = ['user', 'key_id']
```

#### ConversationSession Model
```python
class ConversationSession(models.Model):
    """Stores Double Ratchet session state for each participant"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey('messaging.Conversation', on_delete=models.CASCADE, related_name='sessions')
    participant = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversation_sessions')
    session_state = models.JSONField()  # Encrypted session state
    root_key = models.TextField()  # Base64 encoded, encrypted with user's key
    chain_key_send = models.TextField(null=True, blank=True)
    chain_key_receive = models.TextField(null=True, blank=True)
    message_number_send = models.IntegerField(default=0)
    message_number_receive = models.IntegerField(default=0)
    previous_chain_length = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'conversation_sessions'
        unique_together = ['conversation', 'participant']
```

### Updated Message Model

**IMPORTANT**: We're updating the existing Message model to support encryption while maintaining backward compatibility.

```python
# backend/apps/messaging/models.py - Updated Message model
class Message(models.Model):
    # ... existing fields remain unchanged ...
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')
    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='TEXT')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # UPDATED: Keep for backward compatibility, will be empty for new encrypted messages
    content = models.TextField(blank=True)

    # NEW: Encryption fields
    encrypted_content = models.TextField(blank=True)  # AES-GCM ciphertext (base64)
    iv = models.TextField(blank=True)  # 96-bit initialization vector (base64)
    sender_ratchet_key = models.TextField(blank=True)  # Current DH public key (SPKI base64)
    message_number = models.IntegerField(default=0)  # Message counter (N)
    previous_chain_length = models.IntegerField(default=0)  # Previous chain length (PN)

    class Meta:
        db_table = 'messages'
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['conversation', 'created_at']),
            models.Index(fields=['sender', 'created_at']),
        ]
```

### Migration Strategy

1. **Backward Compatibility**: Existing messages keep their plaintext content
2. **New Messages**: Use encrypted_content field, leave content empty
3. **Gradual Migration**: Users can read old plaintext and new encrypted messages
4. **Future Cleanup**: Eventually remove plaintext content field after full migration

---

## Client-Side Cryptographic Implementation

### Dual Implementation Strategy

#### Primary: libsignal Integration
```typescript
// frontend/src/crypto/libsignalClient.ts
import * as libsignal from '@signalapp/libsignal-client';

export class LibSignalCrypto {
  async initializeKeys(): Promise<KeyBundle> {
    // Use official libsignal implementation
    const identityKeyPair = libsignal.PrivateKey.generate();
    const signedPreKey = libsignal.PrivateKey.generate();
    // ... full Signal protocol implementation
  }

  async encryptMessage(content: string, session: libsignal.SessionCipher): Promise<EncryptedMessage> {
    // Official Signal encryption
  }
}
```

#### Fallback: WebCrypto P-256 Implementation
```typescript
// frontend/src/crypto/webCryptoClient.ts
export class WebCryptoCrypto {
  async generateIdentityKey(): Promise<CryptoKeyPair> {
    return await crypto.subtle.generateKey(
      { name: 'ECDSA', namedCurve: 'P-256' },
      true,
      ['sign', 'verify']
    );
  }

  async generateECDHKey(): Promise<CryptoKeyPair> {
    return await crypto.subtle.generateKey(
      { name: 'ECDH', namedCurve: 'P-256' },
      true,
      ['deriveKey', 'deriveBits']
    );
  }
}
```

### Key Management Implementation

#### Key Generation and Storage
```typescript
// frontend/src/crypto/keyManager.ts
export class KeyManager {
  private cryptoImpl: LibSignalCrypto | WebCryptoCrypto;

  constructor() {
    // Runtime detection and fallback
    this.cryptoImpl = this.detectBestCrypto();
  }

  private detectBestCrypto(): LibSignalCrypto | WebCryptoCrypto {
    try {
      // Try to initialize libsignal
      return new LibSignalCrypto();
    } catch (error) {
      console.warn('libsignal not available, using WebCrypto fallback');
      return new WebCryptoCrypto();
    }
  }
}
```

### X3DH Key Exchange Implementation

#### WebCrypto P-256 X3DH Protocol
```typescript
// frontend/src/crypto/x3dh.ts
export class X3DHKeyExchange {
  async performKeyExchange(
    localIdentityKey: CryptoKeyPair,
    remoteKeyBundle: KeyBundle
  ): Promise<SessionState> {
    // 1. Generate ephemeral key pair
    const ephemeralKey = await this.generateECDHKey();

    // 2. Verify signed pre-key signature
    const isValid = await this.verifySignature(
      remoteKeyBundle.identityKey,
      remoteKeyBundle.signedPreKey.publicKey,
      remoteKeyBundle.signedPreKey.signature
    );

    if (!isValid) {
      throw new Error('Invalid signed pre-key signature');
    }

    // 3. Perform Triple/Quadruple Diffie-Hellman
    const dh1 = await this.performECDH(localIdentityKey.privateKey, remoteKeyBundle.signedPreKey.publicKey);
    const dh2 = await this.performECDH(ephemeralKey.privateKey, remoteKeyBundle.identityKey);
    const dh3 = await this.performECDH(ephemeralKey.privateKey, remoteKeyBundle.signedPreKey.publicKey);

    let dh4: ArrayBuffer | undefined;
    if (remoteKeyBundle.oneTimePreKey) {
      dh4 = await this.performECDH(ephemeralKey.privateKey, remoteKeyBundle.oneTimePreKey.publicKey);
    }

    // 4. Combine shared secrets and derive root key
    const combinedSecret = this.concatenateSecrets([dh1, dh2, dh3, dh4].filter(Boolean));
    const rootKey = await this.deriveRootKey(combinedSecret);

    return {
      rootKey: await this.exportKey(rootKey),
      ephemeralKey: await this.exportKeyPair(ephemeralKey),
      messageNumberSend: 0,
      messageNumberReceive: 0,
      previousChainLength: 0
    };
  }

  private async deriveRootKey(sharedSecret: ArrayBuffer): Promise<CryptoKey> {
    const salt = crypto.getRandomValues(new Uint8Array(32));
    const keyMaterial = await crypto.subtle.importKey('raw', sharedSecret, 'HKDF', false, ['deriveKey']);

    return await crypto.subtle.deriveKey(
      {
        name: 'HKDF',
        hash: 'SHA-256',
        salt: salt,
        info: new TextEncoder().encode('X3DH Root Key')
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      true,
      ['encrypt', 'decrypt']
    );
  }
}
```
### Double Ratchet Implementation

#### Session State Management

```typescript
// frontend/src/crypto/doubleRatchet.ts
interface SessionState {
  rootKey: string;  // Base64 encoded raw key
  sendChainKey?: string;
  receiveChainKey?: string;
  localRatchetKey: CryptoKeyPair;
  remoteRatchetKey?: CryptoKey;
  messageNumberSend: number;
  messageNumberReceive: number;
  previousChainLength: number;
  skippedKeys: Map<string, CryptoKey>;  // (ratchetKey + msgNum) -> messageKey
}

export class DoubleRatchet {
  async encryptMessage(content: string, sessionState: SessionState): Promise<EncryptedMessage> {
    // 1. Check if DH ratchet step needed
    if (this.shouldPerformDHRatchet(sessionState)) {
      await this.performDHRatchet(sessionState);
    }

    // 2. Derive message key from send chain
    const { chainKey: newSendChainKey, messageKey } = await this.deriveMessageKey(
      sessionState.sendChainKey || sessionState.rootKey,
      sessionState.messageNumberSend
    );

    // 3. Encrypt message with AES-GCM
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const associatedData = this.createAssociatedData(
      sessionState.conversationId,
      sessionState.senderId,
      sessionState.messageNumberSend,
      sessionState.previousChainLength
    );

    const encryptedContent = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv, additionalData },
      messageKey,
      new TextEncoder().encode(content)
    );

    // 4. Update session state
    sessionState.sendChainKey = newSendChainKey;
    sessionState.messageNumberSend++;

    return {
      encryptedContent: this.arrayBufferToBase64(encryptedContent),
      iv: this.arrayBufferToBase64(iv),
      senderRatchetKey: await this.exportPublicKey(sessionState.localRatchetKey.publicKey),
      messageNumber: sessionState.messageNumberSend - 1,
      previousChainLength: sessionState.previousChainLength
    };
  }

  async decryptMessage(
    encryptedMessage: EncryptedMessage,
    sessionState: SessionState
  ): Promise<string> {
    // Handle DH ratchet if sender's ratchet key changed
    if (await this.hasRatchetKeyChanged(encryptedMessage.senderRatchetKey, sessionState)) {
      await this.handleSkippedMessages(sessionState, encryptedMessage.messageNumber);
      await this.performReceiveDHRatchet(sessionState, encryptedMessage.senderRatchetKey);
    }

    // Try to decrypt with current chain or skipped keys
    const messageKey = await this.getMessageKey(sessionState, encryptedMessage);

    const associatedData = this.createAssociatedData(
      sessionState.conversationId,
      encryptedMessage.senderId,
      encryptedMessage.messageNumber,
      encryptedMessage.previousChainLength
    );

    const decryptedContent = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: this.base64ToArrayBuffer(encryptedMessage.iv),
        additionalData
      },
      messageKey,
      this.base64ToArrayBuffer(encryptedMessage.encryptedContent)
    );

    return new TextDecoder().decode(decryptedContent);
  }
}
```
---

## Server API Implementation

### Integration with Existing Phase 2 APIs

Our encryption APIs extend the existing Phase 2 messaging system without breaking compatibility.

### New Encryption Endpoints

#### Key Bundle Management
```python
# backend/apps/encryption/views.py
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from .models import UserKeyBundle, OneTimePreKey
from .schemas import KeyBundleUpload, KeyBundleResponse

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def upload_key_bundle(request):
    """Upload user's public key bundle with signature verification"""
    try:
        # Validate input with Pydantic
        bundle_data = KeyBundleUpload(**request.data)

        # Verify signed pre-key signature
        if not verify_signature(
            bundle_data.identity_public_key,
            bundle_data.signed_prekey_public,
            bundle_data.signed_prekey_signature
        ):
            return Response(
                {'error': 'Invalid signed pre-key signature'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Replace existing key bundle atomically
        UserKeyBundle.objects.filter(user=request.user).delete()

        key_bundle = UserKeyBundle.objects.create(
            user=request.user,
            identity_public_key=bundle_data.identity_public_key,
            signed_prekey_id=bundle_data.signed_prekey_id,
            signed_prekey_public=bundle_data.signed_prekey_public,
            signed_prekey_signature=bundle_data.signed_prekey_signature
        )

        return Response({
            'key_bundle_id': str(key_bundle.id),
            'message': 'Key bundle uploaded successfully'
        }, status=status.HTTP_201_CREATED)

    except ValidationError as e:
        return Response({'errors': e.errors()}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_key_bundle(request, user_id):
    """Get another user's key bundle for key exchange"""
    try:
        key_bundle = UserKeyBundle.objects.get(user_id=user_id)

        # SECURITY FIX: Atomic one-time pre-key consumption
        with transaction.atomic():
            one_time_prekey = OneTimePreKey.objects.select_for_update().filter(
                user_id=user_id,
                is_used=False
            ).first()

            if one_time_prekey:
                one_time_prekey.is_used = True
                one_time_prekey.used_at = timezone.now()
                one_time_prekey.save()

        response_data = {
            'identity_public_key': key_bundle.identity_public_key,
            'signed_prekey': {
                'id': key_bundle.signed_prekey_id,
                'public_key': key_bundle.signed_prekey_public,
                'signature': key_bundle.signed_prekey_signature
            }
        }

        if one_time_prekey:
            response_data['one_time_prekey'] = {
                'id': one_time_prekey.key_id,
                'public_key': one_time_prekey.public_key
            }

        return Response(response_data)

    except UserKeyBundle.DoesNotExist:
        return Response(
            {'error': 'Key bundle not found for user'},
            status=status.HTTP_404_NOT_FOUND
        )
```

### Updated Message Endpoints

#### Modified Send Message Endpoint
```python
# backend/apps/messaging/views.py - Updated send_message function
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def send_message(request, conversation_id):
    """Send a message (plaintext or encrypted) to a conversation"""
    try:
        conversation = get_object_or_404(Conversation, id=conversation_id)

        # Check if user is participant
        participant = conversation.participants.filter(user=request.user).first()
        if not participant:
            return Response(
                {'error': 'Not a participant in this conversation'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Validate input - now supports both encrypted and plaintext
        message_data = MessageCreate(**request.data)

        # Create message with encryption support
        message = Message.objects.create(
            conversation=conversation,
            sender=request.user,
            message_type=message_data.message_type,
            # Handle both encrypted and plaintext content
            content=message_data.content or '',  # Backward compatibility
            encrypted_content=getattr(message_data, 'encrypted_content', ''),
            iv=getattr(message_data, 'iv', ''),
            sender_ratchet_key=getattr(message_data, 'sender_ratchet_key', ''),
            message_number=getattr(message_data, 'message_number', 0),
            previous_chain_length=getattr(message_data, 'previous_chain_length', 0)
        )

        # Update conversation timestamp
        conversation.updated_at = timezone.now()
        conversation.save(update_fields=['updated_at'])

        return Response(
            serialize_message(message, request.user).model_dump(),
            status=status.HTTP_201_CREATED
        )

    except ValidationError as e:
        return Response({'errors': e.errors()}, status=status.HTTP_400_BAD_REQUEST)
```

---

## Secure Client Storage Implementation

### IndexedDB Encrypted Storage
```typescript
// frontend/src/storage/encryptedStorage.ts
export class EncryptedStorage {
  private dbName = 'ChatAppCrypto';
  private version = 1;
  private masterKey: CryptoKey | null = null;

  async initialize(): Promise<void> {
    // Generate or retrieve master key for encrypting stored data
    this.masterKey = await this.getMasterKey();

    // Initialize IndexedDB with proper stores
    const db = await this.openDatabase();

    // Create object stores if they don't exist
    if (!db.objectStoreNames.contains('keys')) {
      const keyStore = db.createObjectStore('keys', { keyPath: 'id' });
      keyStore.createIndex('userId', 'userId', { unique: false });
    }

    if (!db.objectStoreNames.contains('sessions')) {
      const sessionStore = db.createObjectStore('sessions', { keyPath: 'id' });
      sessionStore.createIndex('conversationId', 'conversationId', { unique: false });
    }

    if (!db.objectStoreNames.contains('messageKeys')) {
      const messageKeyStore = db.createObjectStore('messageKeys', { keyPath: 'id' });
      messageKeyStore.createIndex('sessionId', 'sessionId', { unique: false });
    }
  }

  async storeKeyBundle(keyBundle: KeyBundle): Promise<void> {
    const encryptedBundle = await this.encryptData(keyBundle);
    // Store in IndexedDB
  }

  async getSessionState(conversationId: string, userId: string): Promise<SessionState | null> {
    const encryptedState = await this.getFromStore('sessions', `${conversationId}-${userId}`);
    if (!encryptedState) return null;

    return await this.decryptData(encryptedState);
  }

  private async getMasterKey(): Promise<CryptoKey> {
    // SECURITY FIX: Use non-exportable key in memory, never sessionStorage
    if (this.masterKey) {
      return this.masterKey;
    }

    // Try to get wrapped key from IndexedDB
    const wrappedKey = await this.getFromStore('masterKey', 'default');
    if (wrappedKey) {
      // Derive KEK from user passphrase (or use WebAuthn when available)
      const kek = await this.deriveKEK();
      return await this.unwrapMasterKey(wrappedKey, kek);
    }

    // Generate new non-exportable master key
    const masterKey = await crypto.subtle.generateKey(
      { name: 'AES-GCM', length: 256 },
      false, // CRITICAL: non-exportable
      ['encrypt', 'decrypt']
    );

    // Wrap and store in IndexedDB
    const kek = await this.deriveKEK();
    const wrappedMasterKey = await this.wrapMasterKey(masterKey, kek);
    await this.storeInDB('masterKey', 'default', wrappedMasterKey);

    return masterKey;
  }

  private async deriveKEK(): Promise<CryptoKey> {
    // Use WebAuthn/platform keystore when available
    // Fallback to PBKDF2 from user session context
    const userContext = this.getUserSessionContext(); // user ID + session token
    const salt = new TextEncoder().encode('ChatApp-KEK-Salt-v1');

    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(userContext),
      'PBKDF2',
      false,
      ['deriveKey']
    );

    return await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: salt,
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false, // non-exportable
      ['wrapKey', 'unwrapKey']
    );
  }
}
```

---

## Operational Security Considerations

### Performance & Security Best Practices

1. **Web Workers**: All cryptographic operations run in Web Workers to maintain UI responsiveness
2. **IV Generation**: Strictly random 96-bit IVs, never reused under the same key
3. **Key Rotation**:
   - Signed pre-keys rotated weekly
   - Maintain ~100 one-time pre-keys per user
   - Automatic replenishment when stock is low
4. **Entropy**: Rely exclusively on `crypto.getRandomValues()` for all random data
5. **Clock Independence**: No security decisions based on client clocks
6. **Error Handling**: Comprehensive error taxonomy:
   - `KeyMismatch`: Session key mismatch
   - `SignatureInvalid`: Invalid key bundle signature
   - `MessageOutOfOrder`: Out-of-sequence message
   - `DecryptionFailed`: Message decryption failure
   - `SessionNotFound`: No session established

### Integration with Existing System

1. **Backward Compatibility**: Existing plaintext messages remain readable
2. **Gradual Migration**: New conversations automatically use encryption
3. **Transparent UX**: Encryption status indicated but not intrusive
4. **Error Recovery**: Graceful fallback for encryption failures

---

## Implementation Timeline & Task Breakdown

### Week 1: Database & Backend Foundation
**Tasks:**
1. **Create encryption Django app and models** (~2 days)
   - UserKeyBundle, OneTimePreKey, ConversationSession, MessageKey models
   - Proper relationships and constraints
   - Database migrations

2. **Update Message model for encryption** (~1 day)
   - Add encrypted_content, iv, sender_ratchet_key fields
   - Maintain backward compatibility with existing content field
   - Update indexes for performance

3. **Implement encryption API endpoints** (~2 days)
   - Key bundle upload/download with signature verification
   - One-time pre-key management
   - Session management endpoints

4. **Create Pydantic schemas for encryption** (~1 day)
   - Validation schemas for key bundles and encrypted messages
   - Integration with existing messaging schemas

### Week 2: Client-Side Crypto Implementation
**Tasks:**
1. **Implement WebCrypto utilities** (~2 days)
   - ECDH, ECDSA, AES-GCM operations
   - Key generation, import/export functions
   - Browser compatibility testing

2. **Create Signal protocol implementation** (~3 days)
   - X3DH key exchange
   - Double Ratchet with proper state management
   - Out-of-order message handling

3. **Implement secure IndexedDB storage** (~1 day)
   - Encrypted storage for keys and sessions
   - Master key derivation and management

4. **Add Web Worker crypto support** (~1 day)
   - Move crypto operations off main thread
   - Message passing interface

5. **Create libsignal integration with fallback** (~1 day)
   - Runtime detection and graceful fallback
   - Feature flag support

### Week 3: Integration & Testing
**Tasks:**
1. **Update Redux store for encrypted messages** (~1 day)
   - Handle encrypted content and decryption states
   - Key exchange status management

2. **Integrate encryption with message sending** (~2 days)
   - Encrypt before sending via socket/API
   - Session establishment flow

3. **Implement message decryption on receive** (~1 day)
   - Decrypt incoming messages
   - Error handling and recovery

4. **Create key exchange UI flow** (~1 day)
   - Encryption status indicators
   - Key generation progress

5. **Update socket server for encrypted routing** (~1 day)
   - Route encrypted messages without decryption
   - Maintain existing real-time functionality

6. **Comprehensive testing** (~2 days)
   - Unit tests for crypto functions
   - Integration tests for key exchange
   - Browser compatibility testing
   - Security audit preparation

---

## Testing Strategy

### Unit Tests (Client-Side)
- **Cryptographic Functions**:
  - ECDSA sign/verify with SPKI byte round-trips
  - ECDH key derivation → HKDF → AES-GCM encrypt/decrypt
  - KDF_RK and KDF_CK function invariants
  - Skipped message keys cache management

- **Protocol Implementation**:
  - X3DH key exchange with and without one-time pre-keys
  - Double Ratchet state transitions
  - Message key derivation and rotation

### Integration Tests (Client ↔ Server)
1. **Session Establishment**:
   - A ↔ B key exchange with/without one-time pre-key
   - Session state persistence and recovery

2. **Message Flow**:
   - A→B→A→B ping-pong across multiple DH ratchets
   - Out-of-order delivery: messages 1,2,5,3,4 all decrypt correctly

3. **Key Management**:
   - Bundle rotation with new signed pre-key
   - Existing sessions remain valid after rotation

### Persistence Tests
- **Browser Reload**: Sessions restore and decrypt old messages
- **Storage Encryption**: Verify all stored data is encrypted
- **Key Recovery**: Proper handling of lost/corrupted keys

### Security Tests
- **Server Security**:
  - Server refuses invalid signatures
  - Server never stores private keys or plaintext
  - Replay protection on message IDs

- **Client Security**:
  - Private keys never leave client
  - Proper IV generation and uniqueness
  - Forward secrecy verification

### Browser Compatibility Tests
- **Target Browsers**: Chrome 79+, Edge 79+, Firefox 78+, Safari 14+
- **Mobile Support**: iOS Safari 14.5+, Android Chrome 79+
- **Feature Detection**: Graceful fallback when features unavailable

---

## Key Differences from Existing Phase 3 Documents

### Improvements Over Original Phase 3
1. **Dual Implementation Strategy**: Primary libsignal + WebCrypto fallback for maximum compatibility
2. **Enhanced Browser Support**: Comprehensive compatibility matrix with specific version requirements
3. **Better Integration**: Seamless integration with existing Phase 2 messaging system
4. **Improved Security**: Server-side signature verification and proper key rotation
5. **Real Double Ratchet**: Full implementation with skipped message handling
6. **Production Ready**: Comprehensive testing strategy and security audit requirements

### Migration from Phase 2
- **Backward Compatibility**: Existing plaintext messages remain readable
- **Gradual Rollout**: New conversations automatically encrypted, old ones migrate naturally
- **Database Changes**: Additive changes to Message model, no breaking changes
- **API Extensions**: New encryption endpoints, existing endpoints enhanced

---

## Acceptance Criteria

### Core Functionality
- [ ] **Session Establishment**: Key exchange succeeds with and without one-time pre-keys
- [ ] **Message Encryption**: All new messages encrypted end-to-end
- [ ] **Message Decryption**: Recipients can decrypt messages reliably
- [ ] **Out-of-Order Handling**: Messages decrypt correctly regardless of delivery order
- [ ] **Session Persistence**: Encryption state survives browser reloads
- [ ] **Forward Secrecy**: New DH ratchets provide forward secrecy
- [ ] **Self-Healing**: Sessions recover from message loss

### Security Requirements
- [ ] **Server Blindness**: Server cannot decrypt any message content
- [ ] **Signature Verification**: Server rejects invalid key bundle signatures
- [ ] **Key Isolation**: Private keys never leave client devices
- [ ] **IV Uniqueness**: No IV reuse under the same key
- [ ] **Replay Protection**: Message replay attacks prevented

### Compatibility & Performance
- [ ] **Browser Support**: Works on Chrome 79+, Edge 79+, Firefox 78+, Safari 14+
- [ ] **Mobile Support**: Functions on iOS Safari 14.5+ and Android Chrome 79+
- [ ] **Performance**: Crypto operations don't block UI (Web Workers)
- [ ] **Fallback**: Graceful degradation when libsignal unavailable

### Integration & UX
- [ ] **Transparent UX**: Encryption is automatic and invisible to users
- [ ] **Status Indicators**: Clear indication of encryption status
- [ ] **Error Handling**: Graceful handling of encryption failures
- [ ] **Migration**: Smooth transition from Phase 2 plaintext messages

---

## Security Considerations & Best Practices

### Cryptographic Security
1. **Key Generation**: Use only `crypto.getRandomValues()` for entropy
2. **IV Management**: Generate random 96-bit IVs, never reuse
3. **Key Rotation**: Regular rotation of signed pre-keys and one-time pre-keys
4. **Forward Secrecy**: Proper Double Ratchet implementation ensures forward secrecy
5. **Associated Data**: Include conversation context in AES-GCM to prevent attacks

### Implementation Security
1. **Private Key Protection**: Mark private keys as non-exportable when possible
2. **Storage Encryption**: Encrypt all stored cryptographic material
3. **Memory Management**: Clear sensitive data from memory when possible
4. **Error Handling**: Avoid timing attacks in error responses
5. **Audit Trail**: Log security-relevant events for monitoring

### Operational Security
1. **Key Backup**: Users must understand key loss implications
2. **Device Management**: Consider multi-device key synchronization in future phases
3. **Monitoring**: Alert on unusual encryption failure rates
4. **Updates**: Plan for cryptographic algorithm updates
5. **Incident Response**: Procedures for handling security incidents

---

## Deliverables

### Client-Side Components
- **E2EE Client SDK**: Complete encryption/decryption interface
- **Key Manager**: Identity and pre-key management
- **Session Manager**: X3DH and Double Ratchet implementation
- **Secure Storage**: IndexedDB encryption layer
- **Web Worker**: Crypto operations worker

### Server-Side Components
- **Encryption Models**: Django models for key storage
- **API Endpoints**: Key bundle and pre-key management
- **Signature Verification**: Server-side key bundle validation
- **Message Routing**: Encrypted message handling

### Testing & Documentation
- **Unit Tests**: Comprehensive crypto function testing
- **Integration Tests**: End-to-end encryption flow testing
- **Browser Tests**: Cross-browser compatibility validation
- **Security Audit**: Professional security review
- **Documentation**: Implementation guide and API documentation

This comprehensive Phase 3 implementation will transform our messaging application into a secure, encrypted communication platform while maintaining full compatibility with our existing Phase 2 foundation.
