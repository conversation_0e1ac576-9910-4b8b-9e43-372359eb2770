# backend/media/utils.py - E2EE Utility Functions
import os
import tempfile
import subprocess
import json
import base64
import secrets
import hashlib
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile

def generate_download_token():
    """Generate secure download token"""
    return secrets.token_urlsafe(32)

def get_client_ip(request):
    """Get client IP address"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

def scan_file_for_virus(file_path):
    """Scan file for viruses using ClamAV or similar"""
    try:
        # Example using ClamAV (install clamav-daemon)
        result = subprocess.run(
            ['clamscan', '--no-summary', file_path],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            return {'infected': False, 'clean': True}
        elif result.returncode == 1:
            # Virus found
            threat = result.stdout.split(':')[-1].strip() if ':' in result.stdout else 'Unknown threat'
            return {'infected': True, 'threat': threat}
        else:
            # Error scanning
            return {'infected': False, 'error': result.stderr}
            
    except subprocess.TimeoutExpired:
        return {'infected': False, 'error': 'Scan timeout'}
    except FileNotFoundError:
        # ClamAV not installed - skip scanning (log warning)
        import logging
        logging.warning("ClamAV not found - virus scanning disabled")
        return {'infected': False, 'skipped': True}
    except Exception as e:
        return {'infected': False, 'error': str(e)}

def decrypt_file_for_processing(media_file, conversation_key):
    """Temporarily decrypt file for processing (thumbnail, metadata)"""
    try:
        # For now, return a placeholder - this requires implementing conversation key retrieval
        # This would need to integrate with the encryption system from Phase 3/4
        
        # Placeholder implementation
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        temp_file.write(b"placeholder encrypted content")
        temp_file.close()
        
        # Return temp file path and a dummy file key
        return temp_file.name, b"dummy_file_key_32_bytes_long_here"
        
    except Exception as e:
        raise Exception(f"Failed to decrypt file for processing: {str(e)}")

def encrypt_data_with_key(data, file_key):
    """Encrypt data with file key"""
    # Placeholder implementation - would use AES-GCM
    nonce = os.urandom(12)
    # In real implementation: use AES-GCM to encrypt data with file_key
    encrypted_data = data  # Placeholder
    return encrypted_data, base64.b64encode(nonce).decode('utf-8')

def generate_thumbnail_encrypted(media_file):
    """Generate encrypted thumbnail for image/video"""
    try:
        # For now, return a placeholder result
        # In full implementation, this would:
        # 1. Get conversation key
        # 2. Decrypt file temporarily
        # 3. Generate thumbnail using PIL/ffmpeg
        # 4. Encrypt thumbnail with same file key
        # 5. Save encrypted thumbnail
        
        return {
            'thumbnail_generated': False, 
            'reason': 'Placeholder implementation - requires full E2EE integration'
        }
        
    except Exception as e:
        return {'thumbnail_generated': False, 'error': str(e)}

def extract_metadata_encrypted(media_file):
    """Extract and encrypt metadata from media file"""
    try:
        # For now, return basic metadata without decryption
        metadata = {
            'file_size': media_file.file_size,
            'mime_type': media_file.mime_type,
            'file_type': media_file.file_type,
            'original_filename': media_file.original_filename
        }
        
        # In full implementation, this would:
        # 1. Decrypt file temporarily
        # 2. Extract detailed metadata (dimensions, duration, etc.)
        # 3. Encrypt metadata with file key
        # 4. Store encrypted metadata
        
        # For now, store basic metadata as JSON
        metadata_json = json.dumps(metadata)
        # Placeholder encryption
        encrypted_metadata = base64.b64encode(metadata_json.encode('utf-8')).decode('utf-8')
        
        # Update media file
        media_file.encrypted_metadata = encrypted_metadata
        media_file.save()
        
        return {'metadata_extracted': True, 'fields': len(metadata)}
        
    except Exception as e:
        return {'metadata_extracted': False, 'error': str(e)}

def get_conversation_key(conversation):
    """Get conversation key for E2EE operations"""
    # Placeholder - this would integrate with the encryption system
    # from Phase 3/4 to retrieve the conversation's encryption key
    return b"placeholder_conversation_key_32_bytes"

# File type validation helpers
def validate_file_size(file_size, file_type):
    """Validate file size based on type"""
    size_limits = {
        'image': 10 * 1024 * 1024,    # 10MB
        'document': 25 * 1024 * 1024, # 25MB
        'audio': 25 * 1024 * 1024,    # 25MB
        'video': 100 * 1024 * 1024,   # 100MB
        'archive': 50 * 1024 * 1024,  # 50MB
    }
    
    max_size = size_limits.get(file_type, 10 * 1024 * 1024)
    return file_size <= max_size

def get_safe_filename(filename):
    """Generate safe filename for storage"""
    import uuid
    file_extension = os.path.splitext(filename)[1]
    return f"{uuid.uuid4()}{file_extension}.enc"

def calculate_file_hash(file_path):
    """Calculate SHA256 hash of file"""
    hash_sha256 = hashlib.sha256()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_sha256.update(chunk)
    return hash_sha256.hexdigest()
