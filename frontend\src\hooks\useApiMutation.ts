// frontend/src/hooks/useApiMutation.ts
import { useCallback } from 'react';
import { useApiError } from './useApiError';

interface UseApiMutationOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
  showSuccessMessage?: boolean;
  showErrorMessage?: boolean;
}

export const useApiMutation = <TData, TVariables>(
  mutationResult: [
    (variables: TVariables) => Promise<{ data?: TData; error?: any }>,
    {
      data?: TData;
      error?: any;
      isLoading: boolean;
      isError: boolean;
      isSuccess: boolean;
      reset: () => void;
    }
  ],
  options: UseApiMutationOptions = {}
) => {
  const [mutate, result] = mutationResult;
  const { formatError } = useApiError();
  const { onSuccess, onError, showSuccessMessage = false, showErrorMessage = true } = options;

  const execute = useCallback(async (variables: TVariables) => {
    try {
      const response = await mutate(variables);
      
      if (response.error) {
        const formattedError = formatError(response.error);
        if (onError) {
          onError(formattedError);
        }
        if (showErrorMessage) {
          // You could integrate with a toast library here
          console.error('Mutation error:', formattedError.message);
        }
        return { success: false, error: formattedError };
      }
      
      if (response.data) {
        if (onSuccess) {
          onSuccess(response.data);
        }
        if (showSuccessMessage) {
          // You could integrate with a toast library here
          console.log('Mutation successful');
        }
        return { success: true, data: response.data };
      }
      
      return { success: false, error: { message: 'Unknown error occurred' } };
    } catch (error) {
      const formattedError = formatError(error);
      if (onError) {
        onError(formattedError);
      }
      if (showErrorMessage) {
        console.error('Mutation error:', formattedError.message);
      }
      return { success: false, error: formattedError };
    }
  }, [mutate, formatError, onSuccess, onError, showSuccessMessage, showErrorMessage]);

  return {
    execute,
    data: result.data,
    error: result.error ? formatError(result.error) : null,
    isLoading: result.isLoading,
    isError: result.isError,
    isSuccess: result.isSuccess,
    reset: result.reset,
  };
};
