# backend/authentication/tests.py
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from users.models import User
import json

class AuthenticationTestCase(APITestCase):
    def setUp(self):
        self.register_url = reverse('register')
        self.login_url = reverse('login')
        self.test_user_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpassword123'
        }

    def test_user_registration_success(self):
        """Test successful user registration."""
        response = self.client.post(self.register_url, self.test_user_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('data', response.data)
        self.assertIn('user', response.data['data'])
        self.assertIn('tokens', response.data['data'])
        self.assertEqual(response.data['data']['user']['email'], self.test_user_data['email'])
        self.assertEqual(response.data['data']['user']['username'], self.test_user_data['username'])

    def test_user_registration_duplicate_email(self):
        """Test registration with duplicate email."""
        # Create first user
        self.client.post(self.register_url, self.test_user_data, format='json')

        # Try to create second user with same email
        duplicate_data = self.test_user_data.copy()
        duplicate_data['username'] = 'differentuser'
        response = self.client.post(self.register_url, duplicate_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)

    def test_user_registration_invalid_data(self):
        """Test registration with invalid data."""
        invalid_data = {
            'email': 'invalid-email',
            'username': 'ab',  # Too short
            'first_name': '',  # Empty
            'last_name': 'User',
            'password': '123'  # Too short
        }

        response = self.client.post(self.register_url, invalid_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_user_login_success(self):
        """Test successful user login."""
        # First register a user
        self.client.post(self.register_url, self.test_user_data, format='json')

        # Then login
        login_data = {
            'email': self.test_user_data['email'],
            'password': self.test_user_data['password']
        }
        response = self.client.post(self.login_url, login_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertIn('user', response.data['data'])
        self.assertIn('tokens', response.data['data'])
        self.assertEqual(response.data['data']['user']['email'], self.test_user_data['email'])

    def test_user_login_invalid_credentials(self):
        """Test login with invalid credentials."""
        # Register a user first
        self.client.post(self.register_url, self.test_user_data, format='json')

        # Try login with wrong password
        login_data = {
            'email': self.test_user_data['email'],
            'password': 'wrongpassword'
        }
        response = self.client.post(self.login_url, login_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertIn('error', response.data)

    def test_user_login_nonexistent_user(self):
        """Test login with non-existent user."""
        login_data = {
            'email': '<EMAIL>',
            'password': 'somepassword'
        }
        response = self.client.post(self.login_url, login_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertIn('error', response.data)

class UserModelTestCase(TestCase):
    def test_user_creation(self):
        """Test user model creation."""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            first_name='Test',
            last_name='User',
            password='testpassword123'
        )

        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.first_name, 'Test')
        self.assertEqual(user.last_name, 'User')
        self.assertTrue(user.check_password('testpassword123'))
        self.assertFalse(user.is_verified)

    def test_user_string_representation(self):
        """Test user string representation."""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            first_name='Test',
            last_name='User',
            password='testpassword123'
        )

        self.assertEqual(str(user), '<EMAIL>')
