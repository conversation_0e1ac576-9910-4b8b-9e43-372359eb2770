# backend/media/views.py - Media Upload API with E2EE
import os
import hashlib
import tempfile
import uuid

# Optional import for file type detection
try:
    import magic
    HAS_MAGIC = True
except ImportError:
    HAS_MAGIC = False
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.http import HttpResponse, Http404, StreamingHttpResponse
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON>PartPars<PERSON>, FormParser
from rest_framework.response import Response
from datetime import timedelta
import json
import base64

from .models import MediaFile, MediaDownload, MediaChunk
from messaging.models import Message

# CHUNK SIZE for streaming (1MB chunks)
CHUNK_SIZE = 1024 * 1024

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@parser_classes([<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>])
def upload_media_chunked(request):
    """Upload media file with chunked streaming and client-side encryption"""

    # Get upload parameters
    conversation_id = request.data.get('conversation_id')
    upload_session = request.data.get('upload_session')
    chunk_number = int(request.data.get('chunk_number', 0))
    total_chunks = int(request.data.get('total_chunks', 1))
    file_hash = request.data.get('file_hash')  # SHA256 of original file

    # File metadata (only on first chunk)
    original_filename = request.data.get('original_filename')
    file_size = request.data.get('file_size')
    mime_type = request.data.get('mime_type')
    wrapped_file_key = request.data.get('wrapped_file_key')  # Encrypted with conversation key
    file_nonce = request.data.get('file_nonce')  # AES-GCM nonce

    if not all([conversation_id, upload_session]):
        return Response(
            {'error': 'Missing required parameters: conversation_id, upload_session'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Verify conversation exists and user has access
    try:
        from messaging.models import Conversation
        conversation = Conversation.objects.get(id=conversation_id)
        if not conversation.participants.filter(
            user=request.user, is_active=True
        ).exists():
            return Response(
                {'error': 'Access denied to conversation'},
                status=status.HTTP_403_FORBIDDEN
            )
    except Conversation.DoesNotExist:
        return Response(
            {'error': 'Conversation not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    try:
        # Get uploaded chunk
        if 'chunk' not in request.FILES:
            return Response(
                {'error': 'No chunk data provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

        chunk_file = request.FILES['chunk']
        chunk_data = chunk_file.read()

        # Verify chunk integrity
        chunk_hash = hashlib.sha256(chunk_data).hexdigest()
        expected_hash = request.data.get('chunk_hash')
        if expected_hash and chunk_hash != expected_hash:
            return Response(
                {'error': 'Chunk integrity check failed'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Store chunk
        MediaChunk.objects.create(
            upload_session=upload_session,
            chunk_number=chunk_number,
            total_chunks=total_chunks,
            chunk_data=chunk_data,
            chunk_hash=chunk_hash
        )

        # Check if all chunks received
        received_chunks = MediaChunk.objects.filter(
            upload_session=upload_session
        ).count()

        if received_chunks < total_chunks:
            return Response({
                'status': 'chunk_received',
                'received_chunks': received_chunks,
                'total_chunks': total_chunks
            })

        # All chunks received - assemble file
        return assemble_chunked_file(
            request, upload_session, conversation, original_filename,
            file_size, mime_type, wrapped_file_key, file_nonce, file_hash
        )

    except Exception as e:
        # Clean up chunks on error
        MediaChunk.objects.filter(upload_session=upload_session).delete()
        return Response(
            {'error': f'Upload failed: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def assemble_chunked_file(request, upload_session, conversation, original_filename,
                         file_size, mime_type, wrapped_file_key, file_nonce, file_hash):
    """Assemble chunks into final encrypted file"""

    try:
        # Get all chunks in order
        chunks = MediaChunk.objects.filter(
            upload_session=upload_session
        ).order_by('chunk_number')

        # Create temporary file for virus scanning
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            # Assemble chunks
            for chunk in chunks:
                temp_file.write(chunk.chunk_data)
            temp_file_path = temp_file.name

        try:
            # FIXED: Virus scan BEFORE encryption (on assembled encrypted data from client)
            # Note: Client sends already encrypted data, so we scan the encrypted content
            # For better security, implement client-side scanning or scan during decryption
            virus_scan_result = scan_file_for_virus(temp_file_path)

            if virus_scan_result.get('infected', False):
                return Response(
                    {'error': f'File rejected: {virus_scan_result.get("threat", "Virus detected")}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Determine file type
            file_type = determine_file_type(mime_type)

            # Validate file
            validation_error = validate_file_metadata(
                original_filename, int(file_size), file_type
            )
            if validation_error:
                return Response(
                    {'error': validation_error},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Generate unique encrypted filename
            file_extension = os.path.splitext(original_filename)[1]
            encrypted_filename = f"{uuid.uuid4()}{file_extension}.enc"

            # Save encrypted file to storage
            file_path = f"media/{request.user.id}/{encrypted_filename}"

            with open(temp_file_path, 'rb') as assembled_file:
                saved_path = default_storage.save(
                    file_path,
                    ContentFile(assembled_file.read())
                )

            # Create temporary media record (without message)
            media_file = MediaFile.objects.create(
                message=None,  # Will be set after message creation
                uploader=request.user,
                original_filename=original_filename,
                file_type=file_type,
                mime_type=mime_type,
                file_size=int(file_size),
                encrypted_file_path=saved_path,
                wrapped_file_key=wrapped_file_key,
                file_nonce=file_nonce,
                virus_scan_status='completed',
                virus_scan_result=json.dumps(virus_scan_result),
                virus_scan_hash=file_hash,
                processing_status='temporary'  # Temporary status until message is created
            )

            # Auto-create Message with media attachment
            from messaging.models import Message
            message = Message.objects.create(
                conversation=conversation,
                sender=request.user,
                message_type='FILE',
                content='',  # Empty for media messages
                has_media=True,
                media_count=1
            )

            # Update media file with the created message
            media_file.message = message
            media_file.processing_status = 'pending'
            media_file.save()

            # Queue background processing (thumbnail, metadata)
            try:
                from .tasks import process_media_file_e2ee
                process_media_file_e2ee.delay(str(media_file.id))
            except Exception as e:
                # If Celery is not running, just log the error and continue
                import logging
                logging.warning(f"Could not queue background processing: {str(e)}")
                # Set processing status to completed for now
                media_file.processing_status = 'completed'
                media_file.save()

            # Return media file data
            # Return both message_id and media_id with complete data
            from .serializers import MediaFileSerializer
            from messaging.schemas import MessageResponse
            
            response_data = {
                'media_file': MediaFileSerializer(media_file).data,
                'message': MessageResponse.model_validate(message).model_dump(by_alias=True),
                'message_id': str(message.id),
                'media_id': str(media_file.id)
            }
            
            return Response(
                response_data,
                status=status.HTTP_201_CREATED
            )

        finally:
            # Clean up
            os.unlink(temp_file_path)
            MediaChunk.objects.filter(upload_session=upload_session).delete()

    except Exception as e:
        # Clean up on error
        MediaChunk.objects.filter(upload_session=upload_session).delete()
        raise e


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@parser_classes([MultiPartParser, FormParser])
def upload_media_simple(request):
    """Simple upload for small files (< 10MB) with client-side encryption"""

    if 'file' not in request.FILES:
        return Response(
            {'error': 'No file provided'},
            status=status.HTTP_400_BAD_REQUEST
        )

    uploaded_file = request.FILES['file']
    conversation_id = request.data.get('conversation_id')
    wrapped_file_key = request.data.get('wrapped_file_key')
    file_nonce = request.data.get('file_nonce')
    file_hash = request.data.get('file_hash')

    if not all([conversation_id, wrapped_file_key, file_nonce]):
        return Response(
            {'error': 'Missing required parameters: conversation_id, wrapped_file_key, file_nonce'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Verify conversation access
    try:
        from messaging.models import Conversation
        conversation = Conversation.objects.get(id=conversation_id)
        if not conversation.participants.filter(
            user=request.user, is_active=True
        ).exists():
            return Response(
                {'error': 'Access denied to conversation'},
                status=status.HTTP_403_FORBIDDEN
            )
    except Conversation.DoesNotExist:
        return Response(
            {'error': 'Conversation not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    # File size check
    if uploaded_file.size > 10 * 1024 * 1024:  # 10MB
        return Response(
            {'error': 'File too large for simple upload. Use chunked upload.'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Read encrypted file content (already encrypted by client)
        encrypted_content = uploaded_file.read()

        # FIXED: Virus scan on encrypted content (limited effectiveness)
        # For better security, implement client-side scanning
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(encrypted_content)
            temp_file_path = temp_file.name

        try:
            virus_scan_result = scan_file_for_virus(temp_file_path)

            if virus_scan_result.get('infected', False):
                return Response(
                    {'error': f'File rejected: {virus_scan_result.get("threat", "Virus detected")}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Determine file type from original filename
            if HAS_MAGIC:
                mime_type = magic.from_buffer(encrypted_content[:1024], mime=True)
            else:
                # Fallback to basic MIME type detection
                mime_type = 'application/octet-stream'
            file_type = determine_file_type(mime_type)

            # Validate file
            validation_error = validate_file_metadata(
                uploaded_file.name, uploaded_file.size, file_type
            )
            if validation_error:
                return Response(
                    {'error': validation_error},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Generate unique filename
            file_extension = os.path.splitext(uploaded_file.name)[1]
            encrypted_filename = f"{uuid.uuid4()}{file_extension}.enc"

            # Save encrypted file
            file_path = f"media/{request.user.id}/{encrypted_filename}"
            saved_path = default_storage.save(
                file_path,
                ContentFile(encrypted_content)
            )

            # Create temporary media record (without message)
            media_file = MediaFile.objects.create(
                message=None,  # Will be set after message creation
                uploader=request.user,
                original_filename=uploaded_file.name,
                file_type=file_type,
                mime_type=mime_type,
                file_size=uploaded_file.size,
                encrypted_file_path=saved_path,
                wrapped_file_key=wrapped_file_key,
                file_nonce=file_nonce,
                virus_scan_status='completed',
                virus_scan_result=json.dumps(virus_scan_result),
                virus_scan_hash=file_hash,
                processing_status='temporary'  # Temporary status until message is created
            )

            # Auto-create Message with media attachment
            from messaging.models import Message
            message = Message.objects.create(
                conversation=conversation,
                sender=request.user,
                message_type='FILE',
                content='',  # Empty for media messages
                has_media=True,
                media_count=1
            )

            # Update media file with the created message
            media_file.message = message
            media_file.processing_status = 'pending'
            media_file.save()

            # Queue background processing
            try:
                from .tasks import process_media_file_e2ee
                process_media_file_e2ee.delay(str(media_file.id))
            except Exception as e:
                # If Celery is not running, just log the error and continue
                import logging
                logging.warning(f"Could not queue background processing: {str(e)}")
                # Set processing status to completed for now
                media_file.processing_status = 'completed'
                media_file.save()
            
            # Emit socket event for media upload completion
            try:
                import requests
                import logging
                
                # Emit media_upload_completed event to socket server
                payload = {
                    'event': 'media_upload_completed',
                    'room': f'conversation_{conversation.id}',
                    'data': {
                        'conversationId': str(conversation.id),
                        'messageId': str(message.id),
                        'mediaFileId': str(media_file.id),
                        'tempId': f'temp_{message.id}'  # Generate a temp ID
                    }
                }
                
                response = requests.post(
                    'http://localhost:7000/api/emit',
                    json=payload,
                    timeout=5,
                    headers={'Content-Type': 'application/json'}
                )
                
                if response.status_code == 200:
                    logging.info(f"Emitted media_upload_completed event for message {message.id}")
                else:
                    logging.warning(f"Failed to emit socket event: {response.status_code}")
                    
            except Exception as e:
                import logging
                logging.warning(f"Could not emit socket event: {str(e)}")

            # Return both message_id and media_id with complete data
            from .serializers import MediaFileSerializer
            from messaging.schemas import MessageResponse
            
            response_data = {
                'media_file': MediaFileSerializer(media_file).data,
                'message': MessageResponse.model_validate(message).model_dump(by_alias=True),
                'message_id': str(message.id),
                'media_id': str(media_file.id)
            }
            
            return Response(
                response_data,
                status=status.HTTP_201_CREATED
            )

        finally:
            os.unlink(temp_file_path)

    except Exception as e:
        return Response(
            {'error': f'Upload failed: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def validate_file_metadata(filename, file_size, file_type):
    """Validate file metadata"""
    # Size limits
    size_limits = {
        'image': 10 * 1024 * 1024,    # 10MB
        'document': 25 * 1024 * 1024, # 25MB
        'audio': 25 * 1024 * 1024,    # 25MB
        'video': 100 * 1024 * 1024,   # 100MB
        'archive': 50 * 1024 * 1024,  # 50MB
    }

    if file_type == 'other':
        return "File type not supported"

    max_size = size_limits.get(file_type, 10 * 1024 * 1024)
    if file_size > max_size:
        return f"File too large. Maximum size for {file_type} files is {max_size // (1024*1024)}MB"

    # Check dangerous extensions
    file_extension = os.path.splitext(filename)[1].lower()
    dangerous_extensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.msi']
    if file_extension in dangerous_extensions:
        return "File type not allowed for security reasons"

    return None


def determine_file_type(mime_type):
    """Determine file type from MIME type"""
    if mime_type.startswith('image/'):
        return 'image'
    elif mime_type.startswith('video/'):
        return 'video'
    elif mime_type.startswith('audio/'):
        return 'audio'
    elif mime_type in [
        'application/pdf', 'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain', 'application/rtf'
    ]:
        return 'document'
    elif mime_type.startswith('text/'):
        return 'document'  # Treat all text files as documents
    elif mime_type in [
        'application/zip', 'application/x-rar-compressed',
        'application/x-7z-compressed'
    ]:
        return 'archive'
    elif mime_type == 'application/octet-stream':
        return 'document'  # Fallback for unknown binary files
    else:
        return 'other'


def scan_file_for_virus(file_path):
    """Basic virus scanning placeholder - implement with ClamAV or similar"""
    try:
        # For now, just return clean - implement actual virus scanning
        # Example: subprocess.run(['clamscan', '--no-summary', file_path])
        return {'infected': False, 'clean': True}
    except Exception as e:
        return {'infected': False, 'error': str(e)}


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def download_media(request, media_id):
    """Generate secure download token - FIXED: Multiple downloads allowed"""
    try:
        media_file = MediaFile.objects.get(id=media_id)

        # Check access permissions
        conversation = media_file.message.conversation
        if not conversation.participants.filter(
            user=request.user, is_active=True
        ).exists():
            return Response(
                {'error': 'Access denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Generate download token (reusable until expiry)
        download_token = generate_download_token()
        expires_at = timezone.now() + timedelta(hours=1)

        MediaDownload.objects.create(
            media_file=media_file,
            downloaded_by=request.user,
            download_token=download_token,
            expires_at=expires_at,
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        return Response({
            'download_url': f'/api/media/download/{download_token}/',
            'filename': media_file.original_filename,
            'file_size': media_file.file_size,
            'expires_at': expires_at.isoformat(),
            'wrapped_file_key': media_file.wrapped_file_key,
            'file_nonce': media_file.file_nonce
        })

    except MediaFile.DoesNotExist:
        return Response(
            {'error': 'Media file not found'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
def secure_download(request, download_token):
    """Secure download with streaming - FIXED: Multiple downloads allowed"""
    try:
        download = MediaDownload.objects.get(
            download_token=download_token,
            expires_at__gt=timezone.now()
        )

        # Check download limits
        if not download.can_download:
            raise Http404("Download limit exceeded or expired")

        media_file = download.media_file

        # Update download stats
        download.download_count += 1
        download.last_downloaded_at = timezone.now()
        download.save()

        # Stream encrypted file (client will decrypt)
        def file_iterator():
            with default_storage.open(media_file.encrypted_file_path, 'rb') as f:
                while True:
                    chunk = f.read(CHUNK_SIZE)
                    if not chunk:
                        break
                    yield chunk

        response = StreamingHttpResponse(
            file_iterator(),
            content_type='application/octet-stream'  # Client handles decryption
        )
        response['Content-Disposition'] = f'attachment; filename="{media_file.original_filename}.enc"'
        response['X-File-Size'] = str(media_file.file_size)
        response['X-Original-Filename'] = media_file.original_filename

        return response

    except MediaDownload.DoesNotExist:
        raise Http404("Download link expired or invalid")


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_thumbnail(request, media_id):
    """Get encrypted thumbnail - FIXED: Thumbnails are now encrypted"""
    try:
        media_file = MediaFile.objects.get(id=media_id)

        # Check access permissions
        conversation = media_file.message.conversation
        if not conversation.participants.filter(
            user=request.user, is_active=True
        ).exists():
            return Response(
                {'error': 'Access denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        if not media_file.encrypted_thumbnail_path:
            return Response(
                {'error': 'Thumbnail not available'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Return encrypted thumbnail data + decryption info
        with default_storage.open(media_file.encrypted_thumbnail_path, 'rb') as thumbnail_file:
            encrypted_thumbnail = thumbnail_file.read()

        return Response({
            'encrypted_thumbnail': base64.b64encode(encrypted_thumbnail).decode('utf-8'),
            'thumbnail_nonce': media_file.thumbnail_nonce,
            'wrapped_file_key': media_file.wrapped_file_key  # Same key for thumbnail
        })

    except MediaFile.DoesNotExist:
        return Response(
            {'error': 'Media file not found'},
            status=status.HTTP_404_NOT_FOUND
        )


def generate_download_token():
    """Generate secure download token"""
    import secrets
    return secrets.token_urlsafe(32)


def get_client_ip(request):
    """Get client IP address"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip
