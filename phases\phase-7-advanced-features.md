# Phase 7: Advanced Features & Polish

**Duration**: 3-4 weeks | **Priority**: Low

## Overview
This final phase implements advanced features that enhance user experience, including message reactions, threading, notifications, themes, and performance optimizations.

## Prerequisites
- All previous phases completed successfully
- Core functionality stable and tested
- Performance baseline established
- User feedback collected from earlier phases

## Advanced Features Roadmap

### Feature Categories
- **Message Enhancements**: Reactions, replies, forwarding, search
- **User Experience**: Themes, shortcuts, drafts, archives
- **Notifications**: Push notifications, email alerts, preferences
- **Performance**: Caching, optimization, offline support
- **Administration**: User blocking, reporting, moderation

## Database Schema Updates

### Step 1: Advanced Message Features

```python
# backend/apps/messaging/models.py - Add advanced message features

class MessageReaction(models.Model):
    """Message reactions/emojis"""
    REACTION_TYPES = [
        ('👍', 'Thumbs Up'),
        ('❤️', 'Heart'),
        ('😂', 'Laugh'),
        ('😮', 'Wow'),
        ('😢', 'Sad'),
        ('😡', 'Angry'),
        ('👏', 'Clap'),
        ('🔥', 'Fire'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='reactions')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='message_reactions')
    reaction = models.CharField(max_length=10, choices=REACTION_TYPES)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'message_reactions'
        unique_together = ['message', 'user', 'reaction']

class MessageThread(models.Model):
    """Message threading for replies"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    parent_message = models.OneToOneField(Message, on_delete=models.CASCADE, related_name='thread')
    reply_count = models.IntegerField(default=0)
    last_reply_at = models.DateTimeField(auto_now_add=True)
    participants = models.ManyToManyField(User, related_name='thread_participations')
    
    class Meta:
        db_table = 'message_threads'

class MessageDraft(models.Model):
    """Save message drafts"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='drafts')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='message_drafts')
    content = models.TextField()
    reply_to = models.ForeignKey(Message, on_delete=models.CASCADE, null=True, blank=True, related_name='draft_replies')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'message_drafts'
        unique_together = ['conversation', 'user']

class MessageForward(models.Model):
    """Track message forwarding"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    original_message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='forwards')
    forwarded_message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='forwarded_from')
    forwarded_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='forwarded_messages')
    forwarded_to_conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='received_forwards')
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'message_forwards'
```

### Step 2: User Preferences and Settings

```python
# backend/apps/users/models.py - Add user preferences

class UserPreferences(models.Model):
    """User application preferences"""
    THEME_CHOICES = [
        ('light', 'Light'),
        ('dark', 'Dark'),
        ('auto', 'Auto'),
    ]
    
    NOTIFICATION_FREQUENCY = [
        ('all', 'All Messages'),
        ('mentions', 'Mentions Only'),
        ('none', 'None'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='preferences')
    
    # Appearance
    theme = models.CharField(max_length=10, choices=THEME_CHOICES, default='auto')
    font_size = models.CharField(max_length=10, default='medium')
    compact_mode = models.BooleanField(default=False)
    
    # Notifications
    push_notifications = models.BooleanField(default=True)
    email_notifications = models.BooleanField(default=True)
    notification_frequency = models.CharField(max_length=10, choices=NOTIFICATION_FREQUENCY, default='all')
    quiet_hours_start = models.TimeField(null=True, blank=True)
    quiet_hours_end = models.TimeField(null=True, blank=True)
    
    # Privacy
    read_receipts = models.BooleanField(default=True)
    last_seen_visibility = models.BooleanField(default=True)
    profile_photo_visibility = models.CharField(max_length=20, default='everyone')
    
    # Chat preferences
    enter_to_send = models.BooleanField(default=True)
    auto_download_media = models.BooleanField(default=True)
    archive_old_conversations = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'user_preferences'

class UserBlock(models.Model):
    """User blocking functionality"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    blocker = models.ForeignKey(User, on_delete=models.CASCADE, related_name='blocked_users')
    blocked = models.ForeignKey(User, on_delete=models.CASCADE, related_name='blocked_by')
    reason = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'user_blocks'
        unique_together = ['blocker', 'blocked']

class ConversationArchive(models.Model):
    """Archive conversations"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='archives')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='archived_conversations')
    archived_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'conversation_archives'
        unique_together = ['conversation', 'user']
```

### Step 3: Notification System

```python
# backend/apps/notifications/models.py
class NotificationTemplate(models.Model):
    """Notification templates"""
    TEMPLATE_TYPES = [
        ('new_message', 'New Message'),
        ('mention', 'Mention'),
        ('call_missed', 'Missed Call'),
        ('group_added', 'Added to Group'),
        ('group_removed', 'Removed from Group'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES, unique=True)
    title_template = models.CharField(max_length=200)
    body_template = models.TextField()
    email_subject_template = models.CharField(max_length=200)
    email_body_template = models.TextField()
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'notification_templates'

class UserNotification(models.Model):
    """User notifications"""
    NOTIFICATION_TYPES = [
        ('push', 'Push Notification'),
        ('email', 'Email'),
        ('in_app', 'In-App'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=10, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=200)
    body = models.TextField()
    data = models.JSONField(default=dict)  # Additional notification data
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='pending')
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'user_notifications'
        ordering = ['-created_at']

class PushSubscription(models.Model):
    """Web push notification subscriptions"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='push_subscriptions')
    endpoint = models.URLField()
    p256dh_key = models.TextField()
    auth_key = models.TextField()
    user_agent = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    last_used = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'push_subscriptions'
        unique_together = ['user', 'endpoint']
```

## Advanced API Implementation

### Step 4: Message Enhancement APIs

```python
# backend/apps/messaging/views.py - Add advanced message features

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def add_message_reaction(request, message_id):
    """Add reaction to a message"""
    try:
        message = Message.objects.get(id=message_id)
        
        # Check if user has access to this message
        conversation = message.conversation
        if not conversation.participants.filter(user=request.user, is_active=True).exists():
            return Response({'error': 'Access denied'}, status=status.HTTP_403_FORBIDDEN)
        
        reaction = request.data.get('reaction')
        if not reaction:
            return Response({'error': 'Reaction required'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Add or update reaction
        message_reaction, created = MessageReaction.objects.get_or_create(
            message=message,
            user=request.user,
            defaults={'reaction': reaction}
        )
        
        if not created:
            message_reaction.reaction = reaction
            message_reaction.save()
        
        # Notify other participants
        from .socket_handlers import notify_message_reaction
        notify_message_reaction(message, request.user, reaction)
        
        return Response({
            'message': 'Reaction added',
            'reaction': MessageReactionSerializer(message_reaction).data
        })
        
    except Message.DoesNotExist:
        return Response({'error': 'Message not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def remove_message_reaction(request, message_id, reaction):
    """Remove reaction from a message"""
    try:
        message_reaction = MessageReaction.objects.get(
            message_id=message_id,
            user=request.user,
            reaction=reaction
        )
        
        message_reaction.delete()
        
        # Notify other participants
        from .socket_handlers import notify_reaction_removed
        notify_reaction_removed(message_reaction.message, request.user, reaction)
        
        return Response({'message': 'Reaction removed'})
        
    except MessageReaction.DoesNotExist:
        return Response({'error': 'Reaction not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def forward_message(request, message_id):
    """Forward a message to other conversations"""
    try:
        original_message = Message.objects.get(id=message_id)
        
        # Check access to original message
        if not original_message.conversation.participants.filter(user=request.user, is_active=True).exists():
            return Response({'error': 'Access denied'}, status=status.HTTP_403_FORBIDDEN)
        
        conversation_ids = request.data.get('conversation_ids', [])
        if not conversation_ids:
            return Response({'error': 'Conversation IDs required'}, status=status.HTTP_400_BAD_REQUEST)
        
        forwarded_messages = []
        
        for conversation_id in conversation_ids:
            try:
                target_conversation = Conversation.objects.get(id=conversation_id)
                
                # Check access to target conversation
                if not target_conversation.participants.filter(user=request.user, is_active=True).exists():
                    continue
                
                # Create forwarded message
                forwarded_message = Message.objects.create(
                    conversation=target_conversation,
                    sender=request.user,
                    content=f"Forwarded: {original_message.content}",
                    message_type=original_message.message_type
                )
                
                # Track forwarding
                MessageForward.objects.create(
                    original_message=original_message,
                    forwarded_message=forwarded_message,
                    forwarded_by=request.user,
                    forwarded_to_conversation=target_conversation
                )
                
                forwarded_messages.append(forwarded_message)
                
                # Notify participants
                from .socket_handlers import notify_new_message
                notify_new_message(forwarded_message)
                
            except Conversation.DoesNotExist:
                continue
        
        return Response({
            'message': f'Message forwarded to {len(forwarded_messages)} conversations',
            'forwarded_count': len(forwarded_messages)
        })
        
    except Message.DoesNotExist:
        return Response({'error': 'Message not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def search_messages(request):
    """Search messages across conversations"""
    query = request.GET.get('q', '').strip()
    conversation_id = request.GET.get('conversation_id')
    
    if not query:
        return Response({'error': 'Search query required'}, status=status.HTTP_400_BAD_REQUEST)
    
    # Base queryset - only messages user has access to
    messages = Message.objects.filter(
        conversation__participants__user=request.user,
        conversation__participants__is_active=True,
        is_deleted=False
    ).distinct()
    
    # Filter by conversation if specified
    if conversation_id:
        messages = messages.filter(conversation_id=conversation_id)
    
    # Search in content (this would be more complex with encrypted messages)
    messages = messages.filter(content__icontains=query)
    
    # Order by relevance (simplified)
    messages = messages.order_by('-created_at')[:50]
    
    return Response({
        'results': MessageSerializer(messages, many=True).data,
        'total_count': messages.count()
    })

@api_view(['POST', 'PUT'])
@permission_classes([permissions.IsAuthenticated])
def save_message_draft(request, conversation_id):
    """Save or update message draft"""
    try:
        conversation = Conversation.objects.get(id=conversation_id)
        
        # Check access
        if not conversation.participants.filter(user=request.user, is_active=True).exists():
            return Response({'error': 'Access denied'}, status=status.HTTP_403_FORBIDDEN)
        
        content = request.data.get('content', '').strip()
        reply_to_id = request.data.get('reply_to_id')
        
        if not content:
            # Delete draft if content is empty
            MessageDraft.objects.filter(conversation=conversation, user=request.user).delete()
            return Response({'message': 'Draft deleted'})
        
        # Save or update draft
        draft, created = MessageDraft.objects.update_or_create(
            conversation=conversation,
            user=request.user,
            defaults={
                'content': content,
                'reply_to_id': reply_to_id
            }
        )
        
        return Response({
            'message': 'Draft saved',
            'draft': MessageDraftSerializer(draft).data
        })
        
    except Conversation.DoesNotExist:
        return Response({'error': 'Conversation not found'}, status=status.HTTP_404_NOT_FOUND)
```

### Step 5: User Preferences API

```python
# backend/apps/users/views.py - Add user preferences

@api_view(['GET', 'PUT'])
@permission_classes([permissions.IsAuthenticated])
def user_preferences(request):
    """Get or update user preferences"""
    preferences, created = UserPreferences.objects.get_or_create(user=request.user)
    
    if request.method == 'GET':
        return Response(UserPreferencesSerializer(preferences).data)
    
    elif request.method == 'PUT':
        serializer = UserPreferencesSerializer(preferences, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def block_user(request, user_id):
    """Block a user"""
    try:
        user_to_block = User.objects.get(id=user_id)
        
        if user_to_block == request.user:
            return Response({'error': 'Cannot block yourself'}, status=status.HTTP_400_BAD_REQUEST)
        
        block, created = UserBlock.objects.get_or_create(
            blocker=request.user,
            blocked=user_to_block,
            defaults={'reason': request.data.get('reason', '')}
        )
        
        if created:
            # Remove from active conversations
            # This is a simplified implementation
            return Response({'message': 'User blocked successfully'})
        else:
            return Response({'message': 'User already blocked'})
            
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def unblock_user(request, user_id):
    """Unblock a user"""
    try:
        UserBlock.objects.get(blocker=request.user, blocked_id=user_id).delete()
        return Response({'message': 'User unblocked successfully'})
    except UserBlock.DoesNotExist:
        return Response({'error': 'User not blocked'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def archive_conversation(request, conversation_id):
    """Archive a conversation"""
    try:
        conversation = Conversation.objects.get(id=conversation_id)
        
        # Check access
        if not conversation.participants.filter(user=request.user, is_active=True).exists():
            return Response({'error': 'Access denied'}, status=status.HTTP_403_FORBIDDEN)
        
        archive, created = ConversationArchive.objects.get_or_create(
            conversation=conversation,
            user=request.user
        )
        
        return Response({'message': 'Conversation archived'})
        
    except Conversation.DoesNotExist:
        return Response({'error': 'Conversation not found'}, status=status.HTTP_404_NOT_FOUND)
```

## Frontend Advanced Features

### Step 6: Theme System

```typescript
// frontend/src/contexts/ThemeContext.tsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { createTheme, ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';

interface ThemeContextType {
  theme: 'light' | 'dark' | 'auto';
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
  effectiveTheme: 'light' | 'dark';
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, setTheme] = useState<'light' | 'dark' | 'auto'>('auto');
  const [effectiveTheme, setEffectiveTheme] = useState<'light' | 'dark'>('light');

  useEffect(() => {
    // Load theme from localStorage
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | 'auto' | null;
    if (savedTheme) {
      setTheme(savedTheme);
    }
  }, []);

  useEffect(() => {
    // Determine effective theme
    if (theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      setEffectiveTheme(mediaQuery.matches ? 'dark' : 'light');
      
      const handleChange = (e: MediaQueryListEvent) => {
        setEffectiveTheme(e.matches ? 'dark' : 'light');
      };
      
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    } else {
      setEffectiveTheme(theme);
    }
  }, [theme]);

  const handleSetTheme = (newTheme: 'light' | 'dark' | 'auto') => {
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
  };

  const muiTheme = createTheme({
    palette: {
      mode: effectiveTheme,
      primary: {
        main: effectiveTheme === 'dark' ? '#90caf9' : '#1976d2',
      },
      background: {
        default: effectiveTheme === 'dark' ? '#121212' : '#ffffff',
        paper: effectiveTheme === 'dark' ? '#1e1e1e' : '#ffffff',
      },
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            scrollbarColor: effectiveTheme === 'dark' ? '#6b6b6b #2b2b2b' : '#c1c1c1 #f1f1f1',
            '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
              backgroundColor: effectiveTheme === 'dark' ? '#2b2b2b' : '#f1f1f1',
            },
            '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
              borderRadius: 8,
              backgroundColor: effectiveTheme === 'dark' ? '#6b6b6b' : '#c1c1c1',
              minHeight: 24,
            },
          },
        },
      },
    },
  });

  return (
    <ThemeContext.Provider value={{ theme, setTheme: handleSetTheme, effectiveTheme }}>
      <MuiThemeProvider theme={muiTheme}>
        <CssBaseline />
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
```

### Step 7: Notification System

```typescript
// frontend/src/utils/notifications.ts
export class NotificationManager {
  private static instance: NotificationManager;
  private registration: ServiceWorkerRegistration | null = null;

  static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  async initialize(): Promise<void> {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      try {
        this.registration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered');
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
  }

  async requestPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.log('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }

    return false;
  }

  async subscribeToPush(): Promise<PushSubscription | null> {
    if (!this.registration) {
      console.error('Service Worker not registered');
      return null;
    }

    try {
      const subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(process.env.REACT_APP_VAPID_PUBLIC_KEY || ''),
      });

      // Send subscription to server
      await this.sendSubscriptionToServer(subscription);
      
      return subscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      return null;
    }
  }

  private async sendSubscriptionToServer(subscription: PushSubscription): Promise<void> {
    const response = await fetch('/api/notifications/subscribe/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify({
        endpoint: subscription.endpoint,
        keys: {
          p256dh: btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('p256dh')!))),
          auth: btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('auth')!))),
        },
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to send subscription to server');
    }
  }

  showNotification(title: string, options: NotificationOptions = {}): void {
    if (Notification.permission === 'granted') {
      new Notification(title, {
        icon: '/icon-192x192.png',
        badge: '/badge-72x72.png',
        ...options,
      });
    }
  }

  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }
}

// Service Worker for push notifications
// public/sw.js
self.addEventListener('push', function(event) {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/icon-192x192.png',
      badge: '/badge-72x72.png',
      data: data.data,
      actions: [
        {
          action: 'reply',
          title: 'Reply',
          icon: '/reply-icon.png'
        },
        {
          action: 'view',
          title: 'View',
          icon: '/view-icon.png'
        }
      ]
    };

    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

self.addEventListener('notificationclick', function(event) {
  event.notification.close();

  if (event.action === 'reply') {
    // Handle reply action
    event.waitUntil(
      clients.openWindow('/chat?reply=' + event.notification.data.conversationId)
    );
  } else if (event.action === 'view') {
    // Handle view action
    event.waitUntil(
      clients.openWindow('/chat/' + event.notification.data.conversationId)
    );
  } else {
    // Default action
    event.waitUntil(
      clients.openWindow('/chat')
    );
  }
});
```

## Performance Optimizations

### Step 8: Caching and Optimization

```typescript
// frontend/src/utils/cache.ts
export class MessageCache {
  private cache = new Map<string, any>();
  private maxSize = 1000;

  set(key: string, value: any): void {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  get(key: string): any {
    return this.cache.get(key);
  }

  has(key: string): boolean {
    return this.cache.has(key);
  }

  clear(): void {
    this.cache.clear();
  }
}

// Implement virtual scrolling for message lists
// frontend/src/components/Chat/VirtualMessageList.tsx
import { FixedSizeList as List } from 'react-window';

const VirtualMessageList: React.FC<{
  messages: Message[];
  height: number;
}> = ({ messages, height }) => {
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style}>
      <MessageItem message={messages[index]} />
    </div>
  );

  return (
    <List
      height={height}
      itemCount={messages.length}
      itemSize={80} // Approximate message height
      overscanCount={5}
    >
      {Row}
    </List>
  );
};
```

## Integration Points

### All Systems Integration
- Advanced features work with existing core functionality
- Performance optimizations don't break existing features
- Notification system integrates with all message types
- Theme system applies consistently across all components

## Acceptance Criteria

### Phase 7 Completion Checklist
- [ ] Message reactions working
- [ ] Message threading implemented
- [ ] Message forwarding functional
- [ ] Search functionality working
- [ ] User preferences system complete
- [ ] Theme system implemented
- [ ] Push notifications working
- [ ] Performance optimizations applied
- [ ] User blocking functionality
- [ ] Conversation archiving

### Testing Requirements
- [ ] Advanced feature integration tests
- [ ] Performance benchmark tests
- [ ] Notification delivery tests
- [ ] Theme switching tests
- [ ] Cross-browser compatibility tests
- [ ] Mobile responsiveness tests

## Common Issues & Troubleshooting

### Performance Issues
- Monitor memory usage with large message lists
- Implement proper cleanup for event listeners
- Use React.memo and useMemo for expensive operations

### Notification Problems
- Check browser notification permissions
- Verify service worker registration
- Test push notification delivery

### Theme Issues
- Ensure consistent color schemes
- Test theme switching performance
- Verify accessibility compliance

## Final Deployment Checklist

### Production Readiness
- [ ] All features tested and stable
- [ ] Performance benchmarks met
- [ ] Security audit completed
- [ ] Documentation updated
- [ ] Error monitoring configured
- [ ] Analytics implemented
- [ ] Backup systems in place

### Launch Preparation
- [ ] User onboarding flow complete
- [ ] Help documentation created
- [ ] Support system ready
- [ ] Monitoring dashboards configured
- [ ] Rollback plan prepared

This final phase completes the chat application with advanced features and production-ready polish. The application should now be fully functional with enterprise-grade features and performance.
