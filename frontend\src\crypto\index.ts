// frontend/src/crypto/index.ts
/**
 * Main export file for Phase 3 encryption utilities.
 * Provides a clean API for all crypto operations.
 */

// WebCrypto utilities
export {
  CRYPTO_CONFIG,
  CryptoError,
  arrayBufferToBase64,
  base64ToArrayBuffer,
  generateRandomBytes,
  generateIdentityKeyPair,
  generatePreKeyPair,
  generateAESKey,
  exportPublicKey,
  exportPrivateKey,
  exportKeyPair,
  importPublicKey,
  importPrivateK<PERSON>,
  importKeyPair,
  signData,
  verifySignature,
  deriveSharedSecret,
  deriveKey,
  encryptMessage,
  decryptMessage,
  stringToArrayBuffer,
  arrayBufferToString,
  generateSignedPreKey,
  verifySignedPreKey,
  isWebCryptoSupported,
  validateKeyAlgorithm,
  getKeyAlgorithmInfo
} from './webCrypto';

// Key storage utilities
export {
  initializeDatabase,
  storeIdentityKeys,
  getIdentityKeys,
  deleteIdentityKeys,
  storeSignedPreKey,
  getCurrentSignedPreKey,
  getSignedPreKey,
  deleteSignedPreKey,
  storeOneTimePreKey,
  storeOneTimePreKeys,
  getAllOneTimePreKeys,
  getUnusedOneTimePreKeys,
  getUnuploadedOneTimePreKeys,
  markOneTimePreKeyAsUploaded,
  markOneTimePreKeyAsUsed,
  deleteOneTimePreKey,
  storeSession,
  getSession,
  getAllSessions,
  updateSession,
  deleteSession,
  storeMetadata,
  getMetadata,
  deleteMetadata,
  clearAllEncryptionData,
  getStorageStats,
  isEncryptionInitialized,
  cleanupOldSessions,
  cleanupUsedPreKeys
} from './keyStorage';

// Key management utilities
export {
  initializeEncryption,
  needsInitialization,
  generateKeyBundle,
  generateOneTimePreKeyBatch,
  getUnuploadedPreKeyBatch,
  markPreKeysAsUploaded,
  needsSignedPreKeyRotation,
  rotateSignedPreKeyIfNeeded,
  needsMoreOneTimePreKeys,
  generateMoreOneTimePreKeysIfNeeded
} from './keyManager';

// Re-export types for convenience
export type {
  KeyPair,
  SerializedKeyPair,
  IdentityKeyPair,
  PreKeyPair,
  EncryptionResult,
  DecryptionParams,
  EncryptionError,
  EncryptionErrorCode,
  Base64String,
  KeyBundleUpload,
  OneTimePreKeyUpload,
  OneTimePreKeyBatch,
  KeyBundleResponse,
  PreKeyCountResponse,
  KeyExchangeRequest,
  KeyExchangeResponse,
  EncryptionStatusRequest,
  EncryptionStatusResponse,
  EncryptedMessagePayload,
  EncryptedMessage,
  StoredIdentityKeys,
  StoredSignedPreKey,
  StoredOneTimePreKey,
  ConversationSession,
  CryptoConfig,
  EncryptionState
} from '../types/encryption';
