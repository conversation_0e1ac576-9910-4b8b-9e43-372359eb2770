// frontend/src/crypto/keyStorage.ts
/**
 * Secure local storage utilities for encryption keys using IndexedDB.
 * Provides encrypted storage for identity keys, pre-keys, and session data.
 */

import type {
  StoredIdentityKeys,
  StoredSignedPreKey,
  StoredOneTimePreKey,
  ConversationSession,
  EncryptionError,
  EncryptionErrorCode
} from '../types/encryption';
import { CryptoError } from './webCrypto';

// ============================================================================
// IndexedDB Configuration
// ============================================================================

const DB_NAME = 'ChatAppEncryption';
const DB_VERSION = 1;

// Object store names
const STORES = {
  IDENTITY_KEYS: 'identityKeys',
  SIGNED_PREKEYS: 'signedPreKeys',
  ONE_TIME_PREKEYS: 'oneTimePreKeys',
  SESSIONS: 'sessions',
  METADATA: 'metadata'
} as const;

// ============================================================================
// Database Initialization
// ============================================================================

let dbInstance: IDBDatabase | null = null;

/**
 * Initialize IndexedDB database
 */
export async function initializeDatabase(): Promise<IDBDatabase> {
  if (dbInstance) {
    return dbInstance;
  }

  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = () => {
      reject(new CryptoError(
        'STORAGE_FAILED',
        'Failed to open IndexedDB database',
        { error: request.error }
      ));
    };

    request.onsuccess = () => {
      dbInstance = request.result;
      resolve(dbInstance);
    };

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;

      // Identity keys store
      if (!db.objectStoreNames.contains(STORES.IDENTITY_KEYS)) {
        const identityStore = db.createObjectStore(STORES.IDENTITY_KEYS, { keyPath: 'id' });
        identityStore.createIndex('createdAt', 'createdAt');
      }

      // Signed pre-keys store
      if (!db.objectStoreNames.contains(STORES.SIGNED_PREKEYS)) {
        const signedPreKeyStore = db.createObjectStore(STORES.SIGNED_PREKEYS, { keyPath: 'id' });
        signedPreKeyStore.createIndex('createdAt', 'createdAt');
      }

      // One-time pre-keys store
      if (!db.objectStoreNames.contains(STORES.ONE_TIME_PREKEYS)) {
        const oneTimePreKeyStore = db.createObjectStore(STORES.ONE_TIME_PREKEYS, { keyPath: 'id' });
        oneTimePreKeyStore.createIndex('uploaded', 'uploaded');
        oneTimePreKeyStore.createIndex('used', 'used');
      }

      // Sessions store
      if (!db.objectStoreNames.contains(STORES.SESSIONS)) {
        const sessionsStore = db.createObjectStore(STORES.SESSIONS, { keyPath: 'conversationId' });
        sessionsStore.createIndex('participantId', 'participantId');
        sessionsStore.createIndex('updatedAt', 'updatedAt');
      }

      // Metadata store
      if (!db.objectStoreNames.contains(STORES.METADATA)) {
        db.createObjectStore(STORES.METADATA, { keyPath: 'key' });
      }
    };
  });
}

/**
 * Get database instance
 */
async function getDatabase(): Promise<IDBDatabase> {
  if (!dbInstance) {
    return await initializeDatabase();
  }
  return dbInstance;
}

// ============================================================================
// Generic Storage Operations
// ============================================================================

/**
 * Generic function to store data in IndexedDB
 */
async function storeData<T>(storeName: string, data: T): Promise<void> {
  const db = await getDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([storeName], 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.put(data);

    request.onerror = () => {
      reject(new CryptoError(
        'STORAGE_FAILED',
        `Failed to store data in ${storeName}`,
        { error: request.error }
      ));
    };

    request.onsuccess = () => resolve();
  });
}

/**
 * Generic function to retrieve data from IndexedDB
 */
async function getData<T>(storeName: string, key: string | number): Promise<T | null> {
  const db = await getDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([storeName], 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.get(key);

    request.onerror = () => {
      reject(new CryptoError(
        'STORAGE_FAILED',
        `Failed to retrieve data from ${storeName}`,
        { error: request.error }
      ));
    };

    request.onsuccess = () => {
      resolve(request.result || null);
    };
  });
}

/**
 * Generic function to get all data from a store
 */
async function getAllData<T>(storeName: string): Promise<T[]> {
  const db = await getDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([storeName], 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.getAll();

    request.onerror = () => {
      reject(new CryptoError(
        'STORAGE_FAILED',
        `Failed to retrieve all data from ${storeName}`,
        { error: request.error }
      ));
    };

    request.onsuccess = () => {
      resolve(request.result || []);
    };
  });
}

/**
 * Generic function to delete data from IndexedDB
 */
async function deleteData(storeName: string, key: string | number): Promise<void> {
  const db = await getDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([storeName], 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.delete(key);

    request.onerror = () => {
      reject(new CryptoError(
        'STORAGE_FAILED',
        `Failed to delete data from ${storeName}`,
        { error: request.error }
      ));
    };

    request.onsuccess = () => resolve();
  });
}

// ============================================================================
// Identity Keys Storage
// ============================================================================

/**
 * Store identity keys
 */
export async function storeIdentityKeys(keys: StoredIdentityKeys): Promise<void> {
  const data = {
    id: 'current',
    ...keys
  };
  await storeData(STORES.IDENTITY_KEYS, data);
}

/**
 * Retrieve identity keys
 */
export async function getIdentityKeys(): Promise<StoredIdentityKeys | null> {
  const data = await getData<StoredIdentityKeys & { id: string }>(STORES.IDENTITY_KEYS, 'current');
  if (!data) return null;
  
  const { id, ...keys } = data;
  return keys;
}

/**
 * Delete identity keys
 */
export async function deleteIdentityKeys(): Promise<void> {
  await deleteData(STORES.IDENTITY_KEYS, 'current');
}

// ============================================================================
// Signed Pre-Key Storage
// ============================================================================

/**
 * Store signed pre-key
 */
export async function storeSignedPreKey(preKey: StoredSignedPreKey): Promise<void> {
  await storeData(STORES.SIGNED_PREKEYS, preKey);
}

/**
 * Get current signed pre-key
 */
export async function getCurrentSignedPreKey(): Promise<StoredSignedPreKey | null> {
  const allPreKeys = await getAllData<StoredSignedPreKey>(STORES.SIGNED_PREKEYS);
  if (allPreKeys.length === 0) return null;
  
  // Return the most recent one
  return allPreKeys.sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )[0];
}

/**
 * Get signed pre-key by ID
 */
export async function getSignedPreKey(id: number): Promise<StoredSignedPreKey | null> {
  return await getData<StoredSignedPreKey>(STORES.SIGNED_PREKEYS, id);
}

/**
 * Delete signed pre-key
 */
export async function deleteSignedPreKey(id: number): Promise<void> {
  await deleteData(STORES.SIGNED_PREKEYS, id);
}

// ============================================================================
// One-Time Pre-Keys Storage
// ============================================================================

/**
 * Store one-time pre-key
 */
export async function storeOneTimePreKey(preKey: StoredOneTimePreKey): Promise<void> {
  await storeData(STORES.ONE_TIME_PREKEYS, preKey);
}

/**
 * Store multiple one-time pre-keys
 */
export async function storeOneTimePreKeys(preKeys: StoredOneTimePreKey[]): Promise<void> {
  const db = await getDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORES.ONE_TIME_PREKEYS], 'readwrite');
    const store = transaction.objectStore(STORES.ONE_TIME_PREKEYS);
    
    let completed = 0;
    const total = preKeys.length;
    
    if (total === 0) {
      resolve();
      return;
    }
    
    preKeys.forEach(preKey => {
      const request = store.put(preKey);
      
      request.onerror = () => {
        reject(new CryptoError(
          'STORAGE_FAILED',
          'Failed to store one-time pre-keys',
          { error: request.error }
        ));
      };
      
      request.onsuccess = () => {
        completed++;
        if (completed === total) {
          resolve();
        }
      };
    });
  });
}

/**
 * Get all one-time pre-keys
 */
export async function getAllOneTimePreKeys(): Promise<StoredOneTimePreKey[]> {
  return await getAllData<StoredOneTimePreKey>(STORES.ONE_TIME_PREKEYS);
}

/**
 * Get unused one-time pre-keys
 */
export async function getUnusedOneTimePreKeys(): Promise<StoredOneTimePreKey[]> {
  const allPreKeys = await getAllOneTimePreKeys();
  return allPreKeys.filter(preKey => !preKey.used);
}

/**
 * Get unuploaded one-time pre-keys
 */
export async function getUnuploadedOneTimePreKeys(): Promise<StoredOneTimePreKey[]> {
  const allPreKeys = await getAllOneTimePreKeys();
  return allPreKeys.filter(preKey => !preKey.uploaded);
}

/**
 * Mark one-time pre-key as uploaded
 */
export async function markOneTimePreKeyAsUploaded(id: number): Promise<void> {
  const preKey = await getData<StoredOneTimePreKey>(STORES.ONE_TIME_PREKEYS, id);
  if (preKey) {
    preKey.uploaded = true;
    await storeData(STORES.ONE_TIME_PREKEYS, preKey);
  }
}

/**
 * Mark one-time pre-key as used
 */
export async function markOneTimePreKeyAsUsed(id: number): Promise<void> {
  const preKey = await getData<StoredOneTimePreKey>(STORES.ONE_TIME_PREKEYS, id);
  if (preKey) {
    preKey.used = true;
    await storeData(STORES.ONE_TIME_PREKEYS, preKey);
  }
}

/**
 * Delete one-time pre-key
 */
export async function deleteOneTimePreKey(id: number): Promise<void> {
  await deleteData(STORES.ONE_TIME_PREKEYS, id);
}

// ============================================================================
// Session Storage
// ============================================================================

/**
 * Store conversation session
 */
export async function storeSession(session: ConversationSession): Promise<void> {
  await storeData(STORES.SESSIONS, session);
}

/**
 * Get conversation session
 */
export async function getSession(conversationId: string): Promise<ConversationSession | null> {
  return await getData<ConversationSession>(STORES.SESSIONS, conversationId);
}

/**
 * Get all sessions
 */
export async function getAllSessions(): Promise<ConversationSession[]> {
  return await getAllData<ConversationSession>(STORES.SESSIONS);
}

/**
 * Update session
 */
export async function updateSession(
  conversationId: string,
  updates: Partial<ConversationSession>
): Promise<void> {
  const session = await getSession(conversationId);
  if (session) {
    const updatedSession = {
      ...session,
      ...updates,
      updatedAt: new Date().toISOString()
    };
    await storeData(STORES.SESSIONS, updatedSession);
  }
}

/**
 * Delete session
 */
export async function deleteSession(conversationId: string): Promise<void> {
  await deleteData(STORES.SESSIONS, conversationId);
}

// ============================================================================
// Metadata Storage
// ============================================================================

/**
 * Store metadata
 */
export async function storeMetadata(key: string, value: any): Promise<void> {
  const data = { key, value, updatedAt: new Date().toISOString() };
  await storeData(STORES.METADATA, data);
}

/**
 * Get metadata
 */
export async function getMetadata(key: string): Promise<any> {
  const data = await getData<{ key: string; value: any; updatedAt: string }>(STORES.METADATA, key);
  return data?.value || null;
}

/**
 * Delete metadata
 */
export async function deleteMetadata(key: string): Promise<void> {
  await deleteData(STORES.METADATA, key);
}

// ============================================================================
// Cleanup and Maintenance
// ============================================================================

/**
 * Clear all encryption data (for logout or reset)
 */
export async function clearAllEncryptionData(): Promise<void> {
  const db = await getDatabase();

  const storeNames = [
    STORES.IDENTITY_KEYS,
    STORES.SIGNED_PREKEYS,
    STORES.ONE_TIME_PREKEYS,
    STORES.SESSIONS,
    STORES.METADATA
  ];

  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeNames, 'readwrite');

    let completed = 0;
    const total = storeNames.length;

    storeNames.forEach(storeName => {
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onerror = () => {
        reject(new CryptoError(
          'STORAGE_FAILED',
          `Failed to clear ${storeName}`,
          { error: request.error }
        ));
      };

      request.onsuccess = () => {
        completed++;
        if (completed === total) {
          resolve();
        }
      };
    });
  });
}

/**
 * Get storage statistics
 */
export async function getStorageStats(): Promise<{
  identityKeys: number;
  signedPreKeys: number;
  oneTimePreKeys: number;
  sessions: number;
  metadata: number;
}> {
  const [identityKeys, signedPreKeys, oneTimePreKeys, sessions, metadata] = await Promise.all([
    getAllData(STORES.IDENTITY_KEYS),
    getAllData(STORES.SIGNED_PREKEYS),
    getAllData(STORES.ONE_TIME_PREKEYS),
    getAllData(STORES.SESSIONS),
    getAllData(STORES.METADATA)
  ]);

  return {
    identityKeys: identityKeys.length,
    signedPreKeys: signedPreKeys.length,
    oneTimePreKeys: oneTimePreKeys.length,
    sessions: sessions.length,
    metadata: metadata.length
  };
}

/**
 * Check if encryption is initialized (has identity keys)
 */
export async function isEncryptionInitialized(): Promise<boolean> {
  try {
    const identityKeys = await getIdentityKeys();
    return identityKeys !== null;
  } catch (error) {
    console.error('Error checking encryption initialization:', error);
    return false;
  }
}

/**
 * Cleanup old sessions (older than 30 days)
 */
export async function cleanupOldSessions(): Promise<number> {
  const sessions = await getAllSessions();
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const oldSessions = sessions.filter(session =>
    new Date(session.updatedAt) < thirtyDaysAgo
  );

  for (const session of oldSessions) {
    await deleteSession(session.conversationId);
  }

  return oldSessions.length;
}

/**
 * Cleanup used one-time pre-keys (older than 7 days)
 */
export async function cleanupUsedPreKeys(): Promise<number> {
  const preKeys = await getAllOneTimePreKeys();
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

  const oldUsedPreKeys = preKeys.filter(preKey =>
    preKey.used && new Date(preKey.createdAt) < sevenDaysAgo
  );

  for (const preKey of oldUsedPreKeys) {
    await deleteOneTimePreKey(preKey.id);
  }

  return oldUsedPreKeys.length;
}
