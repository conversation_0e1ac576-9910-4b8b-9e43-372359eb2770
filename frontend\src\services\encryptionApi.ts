// frontend/src/services/encryptionApi.ts
/**
 * RTK Query API service for Phase 3 encryption endpoints.
 * Extends the main API service with encryption-specific endpoints.
 */

import { api } from './api';
import type {
  KeyBundleUpload,
  KeyBundleResponse,
  OneTimePreKeyBatch,
  PreKeyCountResponse,
  EncryptionApiResponse
} from '../types/encryption';

// ============================================================================
// Encryption API Endpoints
// ============================================================================

export const encryptionApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Upload user's key bundle
    uploadKeyBundle: builder.mutation<
      EncryptionApiResponse<{ message: string }>,
      KeyBundleUpload
    >({
      query: (keyBundle) => ({
        url: '/encryption/bundles/',
        method: 'POST',
        body: keyBundle,
      }),
      invalidatesTags: [{ type: 'User', id: 'CURRENT' }],
      transformResponse: (response: any) => {
        // Handle both wrapped and direct response formats
        return response.data ? response : { data: response };
      },
      transformErrorResponse: (response: any) => {
        return {
          status: response.status,
          data: response.data || { error: 'Upload failed' }
        };
      },
    }),

    // Get another user's key bundle for key exchange
    getKeyBundle: builder.query<
      EncryptionApiResponse<KeyBundleResponse>,
      string // user_id
    >({
      query: (userId) => `/encryption/bundles/${userId}/`,
      providesTags: (result, error, userId) => [
        { type: 'User', id: userId }
      ],
      transformResponse: (response: any) => {
        // Handle both wrapped and direct response formats
        return response.data ? response : { data: response };
      },
      transformErrorResponse: (response: any) => {
        return {
          status: response.status,
          data: response.data || { error: 'Failed to get key bundle' }
        };
      },
    }),

    // Upload batch of one-time pre-keys
    uploadOneTimePreKeys: builder.mutation<
      EncryptionApiResponse<{ 
        message: string;
        uploaded_count: number;
        skipped_count: number;
      }>,
      OneTimePreKeyBatch
    >({
      query: (preKeyBatch) => ({
        url: '/encryption/prekeys/',
        method: 'POST',
        body: preKeyBatch,
      }),
      invalidatesTags: [{ type: 'User', id: 'CURRENT' }],
      transformResponse: (response: any) => {
        // Handle both wrapped and direct response formats
        return response.data ? response : { data: response };
      },
      transformErrorResponse: (response: any) => {
        return {
          status: response.status,
          data: response.data || { error: 'Upload failed' }
        };
      },
    }),

    // Get count of available one-time pre-keys
    getPreKeyCount: builder.query<
      EncryptionApiResponse<PreKeyCountResponse>,
      void
    >({
      query: () => '/encryption/prekeys/count/',
      providesTags: [{ type: 'User', id: 'CURRENT' }],
      transformResponse: (response: any) => {
        // Handle both wrapped and direct response formats
        return response.data ? response : { data: response };
      },
      transformErrorResponse: (response: any) => {
        return {
          status: response.status,
          data: response.data || { error: 'Failed to get pre-key count' }
        };
      },
    }),
  }),
  overrideExisting: false,
});

// ============================================================================
// Export Generated Hooks
// ============================================================================

export const {
  useUploadKeyBundleMutation,
  useGetKeyBundleQuery,
  useLazyGetKeyBundleQuery,
  useUploadOneTimePreKeysMutation,
  useGetPreKeyCountQuery,
  useLazyGetPreKeyCountQuery,
} = encryptionApi;

// ============================================================================
// Manual Cache Management Utilities
// ============================================================================

/**
 * Manually update the pre-key count cache after operations
 */
export const updatePreKeyCountCache = (
  dispatch: any,
  newCount: Partial<PreKeyCountResponse>
) => {
  dispatch(
    encryptionApi.util.updateQueryData(
      'getPreKeyCount',
      undefined,
      (draft) => {
        if (draft.data) {
          Object.assign(draft.data, newCount);
        }
      }
    )
  );
};

/**
 * Invalidate key bundle cache for a specific user
 */
export const invalidateKeyBundleCache = (dispatch: any, userId: string) => {
  dispatch(
    encryptionApi.util.invalidateTags([{ type: 'User', id: userId }])
  );
};

/**
 * Prefetch a user's key bundle
 */
export const prefetchKeyBundle = (dispatch: any, userId: string) => {
  dispatch(
    encryptionApi.util.prefetch('getKeyBundle', userId, { force: false })
  );
};

// ============================================================================
// Error Handling Utilities
// ============================================================================

/**
 * Check if an error is a rate limit error
 */
export const isRateLimitError = (error: any): boolean => {
  return error?.status === 429 || 
         error?.data?.error?.includes('rate limit') ||
         error?.data?.error?.includes('throttle');
};

/**
 * Check if an error is a validation error
 */
export const isValidationError = (error: any): boolean => {
  return error?.status === 400 && 
         error?.data?.details &&
         Array.isArray(error.data.details);
};

/**
 * Check if an error is an authentication error
 */
export const isAuthError = (error: any): boolean => {
  return error?.status === 401;
};

/**
 * Extract error message from API response
 */
export const getErrorMessage = (error: any): string => {
  if (typeof error === 'string') return error;
  
  if (error?.data?.error) return error.data.error;
  if (error?.data?.message) return error.data.message;
  if (error?.message) return error.message;
  
  // Handle validation errors
  if (isValidationError(error) && error.data.details?.length > 0) {
    return error.data.details.map((detail: any) => 
      `${detail.field}: ${detail.message}`
    ).join(', ');
  }
  
  // Default messages based on status
  switch (error?.status) {
    case 400: return 'Invalid request data';
    case 401: return 'Authentication required';
    case 403: return 'Access denied';
    case 404: return 'Resource not found';
    case 429: return 'Rate limit exceeded. Please try again later.';
    case 500: return 'Server error. Please try again.';
    default: return 'An unexpected error occurred';
  }
};

// ============================================================================
// Retry Logic Configuration
// ============================================================================

/**
 * Determine if a request should be retried
 */
export const shouldRetry = (error: any, attempt: number): boolean => {
  // Don't retry client errors (4xx) except for rate limits
  if (error?.status >= 400 && error?.status < 500 && !isRateLimitError(error)) {
    return false;
  }
  
  // Retry server errors (5xx) and rate limits up to 3 times
  return attempt < 3;
};

/**
 * Calculate retry delay (exponential backoff)
 */
export const getRetryDelay = (attempt: number): number => {
  return Math.min(1000 * Math.pow(2, attempt), 10000); // Max 10 seconds
};

// ============================================================================
// Type Guards
// ============================================================================

/**
 * Type guard for successful API response
 */
export const isSuccessResponse = <T>(
  response: any
): response is EncryptionApiResponse<T> => {
  return response && 
         typeof response === 'object' && 
         response.success !== false &&
         'data' in response;
};

/**
 * Type guard for error response
 */
export const isErrorResponse = (response: any): response is { error: string } => {
  return response && 
         typeof response === 'object' && 
         (response.success === false || 'error' in response);
};
