# backend/encryption/group_utils.py
"""
Group encryption utilities for secure group messaging.
Implements ECIES (X25519 + AES-256-GCM) for group key distribution
and message signatures for authentication.
"""
import os
import base64
import hashlib
import json
import time
import secrets
from typing import Optional, Dict, Any
import logging

from django.conf import settings
from django.utils import timezone
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.primitives.kdf.hkdf import HKDF
from cryptography.exceptions import InvalidSignature

from .models import (
    GroupSession, GroupMemberKey, GroupKeyRotation, 
    GroupMessageSignature, UserKeyBundle
)
from .crypto_utils import decode_base64_key, load_ecdsa_public_key, CryptoError

logger = logging.getLogger(__name__)


class GroupEncryptionError(CryptoError):
    """Raised when group encryption operations fail"""
    pass


def initialize_group_encryption(conversation):
    """Initialize encryption for a new group with proper ECIES"""
    try:
        # Generate group session
        session_id = f"group_{conversation.id}_{int(time.time())}"
        
        # Generate AES-256-GCM group key
        group_key = AESGCM.generate_key(bit_length=256)
        
        # Encrypt group key with server master key for persistence
        server_key = _get_server_master_key()
        iv = os.urandom(12)  # 96-bit IV for GCM
        aesgcm = AESGCM(server_key)
        encrypted_group_key = aesgcm.encrypt(iv, group_key, None)
        
        group_session = GroupSession.objects.create(
            conversation=conversation,
            session_id=session_id,
            current_epoch=0,
            encrypted_group_key=base64.b64encode(encrypted_group_key).decode('utf-8'),
            group_key_iv=base64.b64encode(iv).decode('utf-8')
        )
        
        # Encrypt group key for each member using ECIES
        for participant in conversation.participants.filter(is_active=True):
            member_key_bundle = getattr(participant.user, 'key_bundle', None)
            if member_key_bundle:
                try:
                    encrypted_key_data = _encrypt_for_member_ecies(
                        group_key, 
                        member_key_bundle.identity_public_key
                    )
                    
                    GroupMemberKey.objects.create(
                        group_session=group_session,
                        member=participant.user,
                        ephemeral_public_key=encrypted_key_data['ephemeral_public_key'],
                        encrypted_group_key=encrypted_key_data['encrypted_data'],
                        auth_tag=encrypted_key_data['auth_tag'],
                        iv=encrypted_key_data['iv'],
                        epoch=0,
                        is_claimed=False
                    )
                except Exception as e:
                    logger.error(f"Failed to encrypt group key for user {participant.user.id}: {e}")
                    
        logger.info(f"Initialized group encryption for conversation {conversation.id}")
        
    except Exception as e:
        logger.error(f"Failed to initialize group encryption: {e}")
        raise GroupEncryptionError(f"Group encryption initialization failed: {e}")


def add_member_to_group_encryption(conversation, new_member):
    """Add a new member to group encryption with current key"""
    try:
        group_session = conversation.group_session
        current_epoch = group_session.current_epoch
        
        # Get current group key
        group_key = _get_current_group_key(group_session)
        
        # Encrypt group key for new member using ECIES
        member_key_bundle = getattr(new_member, 'key_bundle', None)
        if member_key_bundle:
            encrypted_key_data = _encrypt_for_member_ecies(
                group_key, 
                member_key_bundle.identity_public_key
            )
            
            GroupMemberKey.objects.create(
                group_session=group_session,
                member=new_member,
                ephemeral_public_key=encrypted_key_data['ephemeral_public_key'],
                encrypted_group_key=encrypted_key_data['encrypted_data'],
                auth_tag=encrypted_key_data['auth_tag'],
                iv=encrypted_key_data['iv'],
                epoch=current_epoch,
                is_claimed=False
            )
            
        logger.info(f"Added member {new_member.id} to group encryption")
        
    except Exception as e:
        logger.error(f"Failed to add member to group encryption: {e}")
        raise GroupEncryptionError(f"Failed to add member to group encryption: {e}")


def rotate_group_keys(conversation, reason='scheduled', rotated_by=None):
    """Rotate group encryption keys with forward secrecy"""
    try:
        group_session = conversation.group_session
        old_epoch = group_session.current_epoch
        new_epoch = old_epoch + 1
        
        # Get old key hash for audit trail
        old_group_key = _get_current_group_key(group_session)
        old_key_hash = hashlib.sha256(old_group_key).hexdigest()
        
        # Generate new AES-256-GCM group key
        new_group_key = AESGCM.generate_key(bit_length=256)
        
        # Encrypt new group key with server master key
        server_key = _get_server_master_key()
        iv = os.urandom(12)
        aesgcm = AESGCM(server_key)
        encrypted_group_key = aesgcm.encrypt(iv, new_group_key, None)
        
        # Update group session with new key
        group_session.encrypted_group_key = base64.b64encode(encrypted_group_key).decode('utf-8')
        group_session.group_key_iv = base64.b64encode(iv).decode('utf-8')
        group_session.current_epoch = new_epoch
        group_session.save()
        
        # Encrypt new key for all active members
        for participant in conversation.participants.filter(is_active=True):
            member_key_bundle = getattr(participant.user, 'key_bundle', None)
            if member_key_bundle:
                try:
                    encrypted_key_data = _encrypt_for_member_ecies(
                        new_group_key, 
                        member_key_bundle.identity_public_key
                    )
                    
                    GroupMemberKey.objects.create(
                        group_session=group_session,
                        member=participant.user,
                        ephemeral_public_key=encrypted_key_data['ephemeral_public_key'],
                        encrypted_group_key=encrypted_key_data['encrypted_data'],
                        auth_tag=encrypted_key_data['auth_tag'],
                        iv=encrypted_key_data['iv'],
                        epoch=new_epoch,
                        is_claimed=False
                    )
                except Exception as e:
                    logger.error(f"Failed to encrypt new group key for user {participant.user.id}: {e}")
        
        # Record key rotation
        GroupKeyRotation.objects.create(
            group_session=group_session,
            old_epoch=old_epoch,
            new_epoch=new_epoch,
            rotated_by=rotated_by,
            reason=reason,
            old_key_hash=old_key_hash
        )
        
        logger.info(f"Rotated group keys from epoch {old_epoch} to {new_epoch}")
        
    except Exception as e:
        logger.error(f"Failed to rotate group keys: {e}")
        raise GroupEncryptionError(f"Group key rotation failed: {e}")


def create_group_message_signature(message, sender_private_key_b64: str) -> str:
    """Create ECDSA signature for group message authentication"""
    try:
        # Prepare signature data
        signature_data = {
            'content_hash': hashlib.sha256(message.content.encode('utf-8')).hexdigest(),
            'sender_id': str(message.sender.id),
            'timestamp': int(message.created_at.timestamp()),
            'conversation_id': str(message.conversation.id),
            'epoch': getattr(message.conversation, 'group_session', None) and message.conversation.group_session.current_epoch or 0
        }
        
        # Create canonical representation for signing
        canonical_data = json.dumps(signature_data, sort_keys=True, separators=(',', ':'))
        data_to_sign = canonical_data.encode('utf-8')
        
        # Load private key and sign
        private_key_bytes = base64.b64decode(sender_private_key_b64)
        private_key = serialization.load_der_private_key(private_key_bytes, password=None)
        
        signature = private_key.sign(data_to_sign, ec.ECDSA(hashes.SHA256()))
        signature_b64 = base64.b64encode(signature).decode('utf-8')
        
        # Store signature
        GroupMessageSignature.objects.create(
            message=message,
            sender_signature=signature_b64,
            signature_data=signature_data
        )
        
        return signature_b64
        
    except Exception as e:
        logger.error(f"Failed to create group message signature: {e}")
        raise GroupEncryptionError(f"Message signature creation failed: {e}")


def verify_group_message_signature(message) -> bool:
    """Verify ECDSA signature for group message authentication"""
    try:
        if not hasattr(message, 'group_signature'):
            return False
            
        signature_obj = message.group_signature
        sender_key_bundle = getattr(message.sender, 'key_bundle', None)
        
        if not sender_key_bundle:
            return False
        
        # Recreate canonical data
        canonical_data = json.dumps(signature_obj.signature_data, sort_keys=True, separators=(',', ':'))
        data_to_verify = canonical_data.encode('utf-8')
        
        # Load public key and verify
        public_key_bytes = decode_base64_key(sender_key_bundle.identity_public_key)
        public_key = load_ecdsa_public_key(public_key_bytes)
        
        signature_bytes = base64.b64decode(signature_obj.sender_signature)
        
        public_key.verify(signature_bytes, data_to_verify, ec.ECDSA(hashes.SHA256()))
        return True
        
    except Exception as e:
        logger.error(f"Group message signature verification failed: {e}")
        return False


def get_unclaimed_group_keys(user, conversation) -> Optional[Dict[str, Any]]:
    """Get unclaimed group keys for offline member"""
    try:
        group_session = conversation.group_session
        unclaimed_keys = GroupMemberKey.objects.filter(
            group_session=group_session,
            member=user,
            is_claimed=False
        ).order_by('-epoch')
        
        if not unclaimed_keys.exists():
            return None
            
        # Return the latest unclaimed key
        latest_key = unclaimed_keys.first()
        
        return {
            'ephemeral_public_key': latest_key.ephemeral_public_key,
            'encrypted_group_key': latest_key.encrypted_group_key,
            'auth_tag': latest_key.auth_tag,
            'iv': latest_key.iv,
            'epoch': latest_key.epoch,
            'key_id': str(latest_key.id)
        }
        
    except Exception as e:
        logger.error(f"Failed to get unclaimed group keys: {e}")
        return None


def claim_group_key(user, key_id: str) -> bool:
    """Mark a group key as claimed by the user"""
    try:
        key = GroupMemberKey.objects.get(
            id=key_id,
            member=user,
            is_claimed=False
        )
        
        key.is_claimed = True
        key.claimed_at = timezone.now()
        key.save()
        
        return True
        
    except GroupMemberKey.DoesNotExist:
        return False
    except Exception as e:
        logger.error(f"Failed to claim group key: {e}")
        return False


# Private helper functions

def _encrypt_for_member_ecies(data: bytes, member_public_key_b64: str) -> Dict[str, str]:
    """Encrypt data using ECIES (X25519 + AES-256-GCM)"""
    try:
        # Generate ephemeral X25519 key pair
        ephemeral_private_key = ec.generate_private_key(ec.SECP256R1())
        ephemeral_public_key = ephemeral_private_key.public_key()
        
        # Load member's public key
        member_public_key_bytes = decode_base64_key(member_public_key_b64)
        member_public_key = serialization.load_der_public_key(member_public_key_bytes)
        
        # Perform ECDH
        shared_key = ephemeral_private_key.exchange(ec.ECDH(), member_public_key)
        
        # Derive AES key using HKDF
        derived_key = HKDF(
            algorithm=hashes.SHA256(),
            length=32,
            salt=None,
            info=b'group_key_encryption'
        ).derive(shared_key)
        
        # Encrypt with AES-256-GCM
        iv = os.urandom(12)
        aesgcm = AESGCM(derived_key)
        ciphertext = aesgcm.encrypt(iv, data, None)
        
        # Split ciphertext and auth tag
        encrypted_data = ciphertext[:-16]
        auth_tag = ciphertext[-16:]
        
        # Serialize ephemeral public key
        ephemeral_public_key_bytes = ephemeral_public_key.public_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        return {
            'ephemeral_public_key': base64.b64encode(ephemeral_public_key_bytes).decode('utf-8'),
            'encrypted_data': base64.b64encode(encrypted_data).decode('utf-8'),
            'auth_tag': base64.b64encode(auth_tag).decode('utf-8'),
            'iv': base64.b64encode(iv).decode('utf-8')
        }
        
    except Exception as e:
        raise GroupEncryptionError(f"ECIES encryption failed: {e}")


def _get_current_group_key(group_session: GroupSession) -> bytes:
    """Decrypt and return the current group key"""
    try:
        server_key = _get_server_master_key()
        encrypted_key = base64.b64decode(group_session.encrypted_group_key)
        iv = base64.b64decode(group_session.group_key_iv)
        
        aesgcm = AESGCM(server_key)
        group_key = aesgcm.decrypt(iv, encrypted_key, None)
        
        return group_key
        
    except Exception as e:
        raise GroupEncryptionError(f"Failed to decrypt group key: {e}")


def _get_server_master_key() -> bytes:
    """Get server master key for group key encryption"""
    # In production, this should be loaded from secure key management
    master_key_b64 = getattr(settings, 'GROUP_ENCRYPTION_MASTER_KEY', None)
    if not master_key_b64:
        # Generate a temporary key for development
        master_key = AESGCM.generate_key(bit_length=256)
        master_key_b64 = base64.b64encode(master_key).decode('utf-8')
        logger.warning("Using temporary master key for development. Configure GROUP_ENCRYPTION_MASTER_KEY in production.")
        return master_key
    
    return base64.b64decode(master_key_b64)
