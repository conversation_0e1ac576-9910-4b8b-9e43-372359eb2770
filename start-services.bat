@echo off
REM Chat Application Services Starter
REM Simple batch file to start all services

echo 🚀 Starting Chat Application Services
echo ====================================
echo.
echo Services will be available at:
echo   - Django Backend:  http://localhost:6000
echo   - React Frontend:  http://localhost:5000
echo   - Socket Server:   http://localhost:7000
echo.
echo Starting services in separate windows...
echo Press Ctrl+C in each window to stop services
echo.

REM Start Django Backend
echo Starting Django backend...
start "Django Backend" cmd /k "cd backend && .\venv\Scripts\activate && python manage.py runserver 6000"
timeout /t 2 /nobreak >nul

REM Start React Frontend
echo Starting React frontend...
start "React Frontend" cmd /k "cd frontend && pnpm run dev"
timeout /t 2 /nobreak >nul

REM Start Socket Server
echo Starting Socket server...
start "Socket Server" cmd /k "cd socket-server && pnpm run dev"

echo.
echo ✅ All services started in separate command windows!
echo Check each window for service status and logs
echo.
pause