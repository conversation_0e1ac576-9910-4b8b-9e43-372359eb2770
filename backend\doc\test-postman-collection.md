# Postman Collection Testing Guide

## Overview
This guide provides instructions for testing the updated Postman collection with enhanced authentication and token management.

## Prerequisites
1. Backend server running on `http://localhost:8000`
2. Postman application installed
3. Collection imported: `postman-collection.json`

## Collection Variables Setup
Ensure these collection variables are configured:
- `base_url`: `http://localhost:8000/api`
- `socket_url`: `ws://localhost:8000/ws`
- `jwt_token`: (will be set automatically)
- `user_id`: (will be set automatically)
- `conversation_id`: (set manually for testing)
- `target_user_id`: (set manually for testing)
- `media_id`: (will be set automatically)
- `upload_id`: (will be set automatically)
- `download_token`: (will be set automatically)

## Testing Workflow

### 1. Authentication Flow Testing

#### Test 1: User Registration
1. Run "Register User" request
2. **Expected Results:**
   - Status: 201 Created
   - `jwt_token` variable automatically set
   - `user_id` variable automatically set
   - Console log: Token and user ID stored

#### Test 2: User Login
1. Run "Login User" request with valid credentials
2. **Expected Results:**
   - Status: 200 OK
   - `jwt_token` variable updated
   - `user_id` variable updated
   - Console log: Successful login

#### Test 3: Authentication Failure Handling
1. Run "Login User" with invalid credentials
2. **Expected Results:**
   - Status: 401 Unauthorized
   - `jwt_token` variable cleared
   - `user_id` variable cleared
   - Console log: Authentication failed, tokens cleared

### 2. Token Management Testing

#### Test 4: Automatic Token Injection
1. Ensure `jwt_token` is set (from registration/login)
2. Run any protected endpoint (e.g., "Get User Profile")
3. **Expected Results:**
   - Authorization header automatically added
   - Console log: "🔐 Authorization header set for: [Request Name]"
   - Successful response (200/201)

#### Test 5: Missing Token Handling
1. Clear `jwt_token` variable manually
2. Run a protected endpoint
3. **Expected Results:**
   - Console log: "⚠️ No JWT token available for: [Request Name]"
   - Status: 401 Unauthorized
   - Tokens automatically cleared by global script

### 3. API Endpoints Testing

#### Authentication Endpoints
- ✅ Register User
- ✅ Login User
- ✅ Get User Profile

#### Messaging Endpoints
- ✅ List Conversations
- ✅ Create Direct Conversation
- ✅ Create Group Conversation
- ✅ Get Conversation Messages
- ✅ Send Plaintext Message
- ✅ Send Encrypted Message
- ✅ Get Conversation Encryption Status
- ✅ Search Users
- ✅ Get User Profile

#### Group Management Endpoints
- ✅ Create Group
- ✅ Add Group Member
- ✅ Remove Group Member
- ✅ Update Group
- ✅ Leave Group
- ✅ Send Group Message
- ✅ Verify Message Signature

#### Group Encryption Endpoints
- ✅ Get Group Key
- ✅ Claim Group Key
- ✅ Rotate Group Key

#### Encryption Endpoints
- ✅ Upload Key Bundle
- ✅ Get Key Bundle
- ✅ Upload One-Time Pre-Keys
- ✅ Get Pre-Key Count

#### Media Endpoints
- ✅ Upload Media (Simple)
- ✅ Upload Media (Chunked)
- ✅ Download Media by ID
- ✅ Download Media by Token
- ✅ Get Media Thumbnail

### 4. Global Script Validation

#### Pre-request Script Features
- ✅ Automatic token injection for protected endpoints
- ✅ Skip authentication for login/register
- ✅ Console logging for debugging
- ✅ Request name detection

#### Test Script Features
- ✅ Global authentication error handling
- ✅ Automatic token cleanup on 401/403
- ✅ Server error detection
- ✅ Response logging
- ✅ Conditional testing based on endpoint type

## Expected Console Output Examples

### Successful Authentication Flow
```
🔓 Skipping auth for: Register User
📊 Register User: 201 Created
✅ Registration successful - tokens stored

🔓 Skipping auth for: Login User
📊 Login User: 200 OK
✅ Login successful - tokens updated

🔐 Authorization header set for: Get User Profile
📊 Get User Profile: 200 OK
```

### Authentication Failure Flow
```
🔓 Skipping auth for: Login User
📊 Login User: 401 Unauthorized
🚫 Authentication failed for: Login User
🧹 Clearing invalid tokens...

⚠️ No JWT token available for: Get User Profile
📊 Get User Profile: 401 Unauthorized
🚫 Authentication failed for: Get User Profile
🧹 Clearing invalid tokens...
```

## Troubleshooting

### Common Issues
1. **Token not being set**: Check if registration/login requests are successful
2. **401 errors on protected endpoints**: Verify token is present in collection variables
3. **Headers not being added**: Check pre-request script execution
4. **Variables not clearing**: Verify global test script is running

### Debug Steps
1. Open Postman Console (View → Show Postman Console)
2. Monitor console logs during request execution
3. Check collection variables after each request
4. Verify request headers in the request preview

## Security Features Implemented

1. **Automatic Token Management**: Tokens are automatically injected and managed
2. **Secure Token Storage**: Tokens stored in collection variables (not environment)
3. **Automatic Cleanup**: Invalid tokens are immediately cleared
4. **Request Logging**: All requests logged for audit purposes
5. **Error Handling**: Comprehensive error detection and handling
6. **Conditional Authentication**: Smart authentication skipping for public endpoints

## Collection Maintenance

### Adding New Endpoints
1. Follow existing request structure
2. Add appropriate test scripts
3. Use collection variables for dynamic data
4. Include proper error handling

### Updating Authentication Logic
1. Modify global pre-request script for new auth patterns
2. Update skipAuthEndpoints array for public endpoints
3. Enhance global test script for new error scenarios

This comprehensive testing approach ensures the Postman collection provides a robust and secure API testing environment with seamless authentication flow management.