// socket-server/src/schemas/encryptionSchemas.ts
/**
 * Zod validation schemas for Phase 3 encryption models and socket events.
 * These schemas validate encrypted message payloads and key exchange data.
 */

import { z } from 'zod';

// Base64 validation regex
const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;

// UUID validation
const uuidSchema = z.string().uuid();

// Base64 string validation
const base64Schema = z.string().regex(base64Regex, 'Invalid base64 encoding');

// SPKI public key validation (base64 encoded, minimum reasonable length)
const spkiKeySchema = z.string()
  .regex(base64Regex, 'Invalid base64 encoding')
  .min(50, 'Key too short for SPKI format');

// User Key Bundle Schemas
export const UserKeyBundleSchema = z.object({
  id: uuidSchema,
  userId: uuidSchema,
  identityPublicKey: spkiKeySchema,
  signedPrekeyId: z.number().int().min(0),
  signedPrekeyPublic: spkiKeySchema,
  signedPrekeySignature: base64Schema,
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const KeyBundleUploadSchema = z.object({
  identityPublicKey: spkiKeySchema,
  signedPrekeyId: z.number().int().min(0),
  signedPrekeyPublic: spkiKeySchema,
  signedPrekeySignature: base64Schema,
});

// One-Time Pre-Key Schemas
export const OneTimePreKeySchema = z.object({
  id: uuidSchema,
  userId: uuidSchema,
  keyId: z.number().int().min(0),
  publicKey: spkiKeySchema,
  isUsed: z.boolean(),
  createdAt: z.date(),
  usedAt: z.date().nullable(),
});

export const OneTimePreKeyUploadSchema = z.object({
  keyId: z.number().int().min(0),
  publicKey: spkiKeySchema,
});

export const OneTimePreKeyBatchSchema = z.object({
  prekeys: z.array(OneTimePreKeyUploadSchema).min(1).max(100),
});

// Key Bundle Response Schema
export const KeyBundleResponseSchema = z.object({
  identityPublicKey: spkiKeySchema,
  signedPrekey: z.object({
    id: z.number().int(),
    publicKey: spkiKeySchema,
    signature: base64Schema,
  }),
  oneTimePrekey: z.object({
    id: z.number().int(),
    publicKey: spkiKeySchema,
  }).optional(),
});

// Encrypted Message Schemas
export const EncryptedMessageCreateSchema = z.object({
  conversationId: uuidSchema,
  encryptedContent: base64Schema,
  iv: base64Schema.refine(
    (val) => {
      try {
        const decoded = Buffer.from(val, 'base64');
        return decoded.length === 12; // 96 bits = 12 bytes
      } catch {
        return false;
      }
    },
    'IV must be exactly 96 bits (12 bytes)'
  ),
  senderRatchetKey: spkiKeySchema,
  messageNumber: z.number().int().min(0),
  previousChainLength: z.number().int().min(0),
  messageType: z.enum(['TEXT', 'IMAGE', 'FILE', 'SYSTEM']).default('TEXT'),
});

export const EncryptedMessageResponseSchema = z.object({
  id: uuidSchema,
  conversationId: uuidSchema,
  senderId: uuidSchema,
  encryptedContent: base64Schema,
  iv: base64Schema,
  senderRatchetKey: spkiKeySchema,
  messageNumber: z.number().int(),
  previousChainLength: z.number().int(),
  messageType: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Socket Event Schemas
export const SocketAuthSchema = z.object({
  token: z.string().min(1, 'JWT token required'),
});

export const JoinConversationSchema = z.object({
  conversationId: uuidSchema,
});

export const LeaveConversationSchema = z.object({
  conversationId: uuidSchema,
});

export const SendMessageSchema = z.object({
  conversationId: uuidSchema,
  content: z.string().optional(), // For backward compatibility
  messageType: z.enum(['TEXT', 'IMAGE', 'FILE', 'SYSTEM']).default('TEXT'),
  // Encryption fields (optional for backward compatibility)
  encryptedContent: base64Schema.optional(),
  iv: base64Schema.optional(),
  senderRatchetKey: spkiKeySchema.optional(),
  messageNumber: z.number().int().min(0).optional(),
  previousChainLength: z.number().int().min(0).optional(),
});

export const TypingIndicatorSchema = z.object({
  conversationId: uuidSchema,
  isTyping: z.boolean(),
});

export const KeyExchangeRequestSchema = z.object({
  targetUserId: uuidSchema,
  conversationId: uuidSchema,
  ephemeralPublicKey: spkiKeySchema,
});

export const KeyExchangeResponseSchema = z.object({
  success: z.boolean(),
  keyBundle: KeyBundleResponseSchema.optional(),
  error: z.string().optional(),
});

// Session Management Schemas
export const ConversationSessionSchema = z.object({
  id: uuidSchema,
  conversationId: uuidSchema,
  participantId: uuidSchema,
  sessionState: z.record(z.string(), z.any()), // JSON object
  rootKey: base64Schema,
  chainKeySend: base64Schema.nullable(),
  chainKeyReceive: base64Schema.nullable(),
  messageNumberSend: z.number().int().min(0),
  messageNumberReceive: z.number().int().min(0),
  previousChainLength: z.number().int().min(0),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Message Key Schema
export const MessageKeySchema = z.object({
  id: uuidSchema,
  sessionId: uuidSchema,
  messageNumber: z.number().int(),
  messageKey: base64Schema,
  senderRatchetKey: spkiKeySchema,
  createdAt: z.date(),
});

// Error Response Schema
export const SocketErrorSchema = z.object({
  error: z.string(),
  code: z.string().optional(),
  details: z.record(z.string(), z.any()).optional(),
});

// Socket Event Types
export type UserKeyBundle = z.infer<typeof UserKeyBundleSchema>;
export type KeyBundleUpload = z.infer<typeof KeyBundleUploadSchema>;
export type OneTimePreKey = z.infer<typeof OneTimePreKeySchema>;
export type OneTimePreKeyUpload = z.infer<typeof OneTimePreKeyUploadSchema>;
export type OneTimePreKeyBatch = z.infer<typeof OneTimePreKeyBatchSchema>;
export type KeyBundleResponse = z.infer<typeof KeyBundleResponseSchema>;
export type EncryptedMessageCreate = z.infer<typeof EncryptedMessageCreateSchema>;
export type EncryptedMessageResponse = z.infer<typeof EncryptedMessageResponseSchema>;
export type SocketAuth = z.infer<typeof SocketAuthSchema>;
export type JoinConversation = z.infer<typeof JoinConversationSchema>;
export type LeaveConversation = z.infer<typeof LeaveConversationSchema>;
export type SendMessage = z.infer<typeof SendMessageSchema>;
export type TypingIndicator = z.infer<typeof TypingIndicatorSchema>;
export type KeyExchangeRequest = z.infer<typeof KeyExchangeRequestSchema>;
export type KeyExchangeResponse = z.infer<typeof KeyExchangeResponseSchema>;
export type ConversationSession = z.infer<typeof ConversationSessionSchema>;
export type MessageKey = z.infer<typeof MessageKeySchema>;
export type SocketError = z.infer<typeof SocketErrorSchema>;

// Validation helper functions
export const validateBase64 = (value: string): boolean => {
  try {
    Buffer.from(value, 'base64');
    return base64Regex.test(value);
  } catch {
    return false;
  }
};

export const validateUUID = (value: string): boolean => {
  return uuidSchema.safeParse(value).success;
};

export const validateSPKIKey = (value: string): boolean => {
  return spkiKeySchema.safeParse(value).success;
};

// Message type guards
export const isEncryptedMessage = (message: any): boolean => {
  return !!(message.encryptedContent && message.iv && message.senderRatchetKey);
};

export const isPlaintextMessage = (message: any): boolean => {
  return !!(message.content && !message.encryptedContent);
};

// Socket event validation helpers
export const validateSocketEvent = <T>(schema: z.ZodSchema<T>, data: unknown): T => {
  const result = schema.safeParse(data);
  if (!result.success) {
    throw new Error(`Validation failed: ${result.error.message}`);
  }
  return result.data;
};

export const createSocketError = (error: string, code?: string, details?: Record<string, any>): SocketError => {
  return { error, code, details };
};
