# backend/conftest.py
import os
import django
from django.conf import settings

# Configure Django settings before importing anything else
if not settings.configured:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatapp.settings_test')
    django.setup()

import pytest
import uuid
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from messaging.models import Conversation, ConversationParticipant, Message
from faker import Faker

User = get_user_model()
fake = Faker()

@pytest.fixture
def api_client():
    """Provide an API client for testing."""
    return APIClient()

@pytest.fixture
def user_factory():
    """Factory for creating test users."""
    def _create_user(**kwargs):
        defaults = {
            'email': fake.email(),
            'username': fake.user_name(),
            'first_name': fake.first_name(),
            'last_name': fake.last_name(),
            'password': 'testpass123'
        }
        defaults.update(kwargs)
        
        user = User.objects.create_user(**defaults)
        return user
    return _create_user

@pytest.fixture
def user(user_factory):
    """Create a single test user."""
    return user_factory()

@pytest.fixture
def users(user_factory):
    """Create multiple test users."""
    return [user_factory() for _ in range(3)]

@pytest.fixture
def authenticated_client(api_client, user):
    """Provide an authenticated API client."""
    refresh = RefreshToken.for_user(user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    return api_client

@pytest.fixture
def conversation_factory():
    """Factory for creating test conversations."""
    def _create_conversation(creator, participants=None, **kwargs):
        defaults = {
            'type': 'DIRECT',
            'name': None
        }
        defaults.update(kwargs)
        
        conversation = Conversation.objects.create(**defaults)
        
        # Add creator as participant
        ConversationParticipant.objects.create(
            conversation=conversation,
            user=creator,
            role='ADMIN' if defaults['type'] == 'GROUP' else 'MEMBER'
        )
        
        # Add other participants
        if participants:
            for participant in participants:
                ConversationParticipant.objects.create(
                    conversation=conversation,
                    user=participant,
                    role='MEMBER'
                )
        
        return conversation
    return _create_conversation

@pytest.fixture
def conversation(user, users, conversation_factory):
    """Create a test conversation."""
    return conversation_factory(creator=user, participants=[users[0]])

@pytest.fixture
def group_conversation(user, users, conversation_factory):
    """Create a test group conversation."""
    return conversation_factory(
        creator=user, 
        participants=users, 
        type='GROUP',
        name='Test Group'
    )

@pytest.fixture
def message_factory():
    """Factory for creating test messages."""
    def _create_message(conversation, sender, **kwargs):
        defaults = {
            'content': fake.text(max_nb_chars=200),
            'message_type': 'TEXT'
        }
        defaults.update(kwargs)
        
        return Message.objects.create(
            conversation=conversation,
            sender=sender,
            **defaults
        )
    return _create_message

@pytest.fixture
def messages(conversation, user, users, message_factory):
    """Create test messages."""
    messages = []
    # Create messages from different users
    for i in range(5):
        sender = user if i % 2 == 0 else users[0]
        messages.append(message_factory(conversation, sender))
    return messages

@pytest.fixture
def jwt_tokens(user):
    """Generate JWT tokens for a user."""
    refresh = RefreshToken.for_user(user)
    return {
        'access': str(refresh.access_token),
        'refresh': str(refresh)
    }
