// Encryption Cycle Test - Run this in browser console to test encryption/decryption
// This tests the complete encryption/decryption cycle to ensure it works correctly

console.log('🧪 [ENCRYPTION TEST] Starting encryption cycle test...');

// Test function to verify encryption/decryption works
async function testEncryptionCycle() {
  try {
    console.log('🧪 [ENCRYPTION TEST] Testing encryption/decryption cycle...');
    
    // Check if EncryptionContext is available
    if (!window.React || !window.React.useContext) {
      console.error('❌ React context not available');
      return false;
    }
    
    // Test data
    const testMessage = 'Hello, this is a test message for encryption!';
    const conversationId = 'test-conversation-123';
    const tempId = 'test-temp-id-' + Date.now();
    
    console.log('🧪 [ENCRYPTION TEST] Test message:', testMessage);
    
    // Try to access the encryption context through the app
    // This is a bit hacky but necessary for testing
    const appElement = document.querySelector('#root');
    if (!appElement) {
      console.error('❌ App root element not found');
      return false;
    }
    
    // Alternative: Test the crypto functions directly
    console.log('🧪 [ENCRYPTION TEST] Testing crypto functions directly...');
    
    // Import crypto functions (if available globally)
    if (window.crypto && window.crypto.subtle) {
      console.log('✅ Web Crypto API available');
      
      // Test basic encryption/decryption
      const testKey = await window.crypto.subtle.generateKey(
        {
          name: 'AES-GCM',
          length: 256
        },
        false,
        ['encrypt', 'decrypt']
      );
      
      const iv = window.crypto.getRandomValues(new Uint8Array(12));
      const encoder = new TextEncoder();
      const decoder = new TextDecoder();
      
      // Encrypt
      const encrypted = await window.crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        testKey,
        encoder.encode(testMessage)
      );
      
      console.log('🔐 [ENCRYPTION TEST] Message encrypted successfully');
      
      // Decrypt
      const decrypted = await window.crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        testKey,
        encrypted
      );
      
      const decryptedText = decoder.decode(decrypted);
      console.log('🔓 [ENCRYPTION TEST] Message decrypted:', decryptedText);
      
      // Verify
      if (decryptedText === testMessage) {
        console.log('✅ [ENCRYPTION TEST] Basic encryption/decryption cycle PASSED');
        return true;
      } else {
        console.error('❌ [ENCRYPTION TEST] Decrypted text does not match original');
        console.error('Original:', testMessage);
        console.error('Decrypted:', decryptedText);
        return false;
      }
    } else {
      console.error('❌ Web Crypto API not available');
      return false;
    }
    
  } catch (error) {
    console.error('❌ [ENCRYPTION TEST] Test failed:', error);
    return false;
  }
}

// Test the EncryptionContext functions if available
async function testEncryptionContext() {
  try {
    console.log('🧪 [CONTEXT TEST] Testing EncryptionContext functions...');
    
    // This would need to be called from within a React component
    // For now, we'll test if the functions are accessible
    
    // Check if the app has the encryption context
    const hasEncryption = window.store && window.store.getState().encryption;
    console.log('🧪 [CONTEXT TEST] Encryption state available:', !!hasEncryption);
    
    if (hasEncryption) {
      const encryptionState = window.store.getState().encryption;
      console.log('🧪 [CONTEXT TEST] Encryption state:', encryptionState);
    }
    
    return true;
  } catch (error) {
    console.error('❌ [CONTEXT TEST] Context test failed:', error);
    return false;
  }
}

// Test message sending with encryption
async function testMessageSending() {
  try {
    console.log('🧪 [MESSAGE TEST] Testing message sending with encryption...');
    
    // Find message input
    const messageInput = document.querySelector('[data-testid="message-input"]');
    const sendButton = document.querySelector('[data-testid="send-button"]');
    
    if (!messageInput || !sendButton) {
      console.log('⚠️ [MESSAGE TEST] Message input/button not found - not on chat page');
      return false;
    }
    
    // Monitor console for encryption logs
    let encryptionLogs = [];
    const originalConsoleLog = console.log;
    console.log = function(...args) {
      const message = args.join(' ');
      if (message.includes('[ENCRYPTION]') || message.includes('[DECRYPTION]')) {
        encryptionLogs.push(message);
      }
      return originalConsoleLog.apply(console, args);
    };
    
    // Send test message
    const testMessage = 'Encryption test message - ' + Date.now();
    messageInput.value = testMessage;
    messageInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    setTimeout(() => {
      sendButton.click();
      console.log('📤 [MESSAGE TEST] Test message sent');
      
      // Wait for encryption logs
      setTimeout(() => {
        console.log = originalConsoleLog; // Restore console.log
        console.log('🧪 [MESSAGE TEST] Encryption logs captured:', encryptionLogs);
        
        // Analyze logs
        const hasEncryptionLog = encryptionLogs.some(log => log.includes('Message encrypted successfully'));
        const hasDecryptionLog = encryptionLogs.some(log => log.includes('Message decrypted successfully'));
        
        if (hasEncryptionLog) {
          console.log('✅ [MESSAGE TEST] Encryption detected in logs');
        } else {
          console.log('⚠️ [MESSAGE TEST] No encryption success log found');
        }
        
        if (hasDecryptionLog) {
          console.log('✅ [MESSAGE TEST] Decryption detected in logs');
        } else {
          console.log('⚠️ [MESSAGE TEST] No decryption success log found');
        }
      }, 3000);
    }, 100);
    
    return true;
  } catch (error) {
    console.error('❌ [MESSAGE TEST] Message test failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 [ENCRYPTION TEST] Running all encryption tests...');
  console.log('=====================================');
  
  const results = {
    basicCrypto: await testEncryptionCycle(),
    contextTest: await testEncryptionContext(),
    messageTest: await testMessageSending()
  };
  
  console.log('\n📊 [ENCRYPTION TEST] Test Results:');
  console.log('=====================================');
  console.log('Basic Crypto API:', results.basicCrypto ? '✅ PASSED' : '❌ FAILED');
  console.log('Context Test:', results.contextTest ? '✅ PASSED' : '❌ FAILED');
  console.log('Message Test:', results.messageTest ? '✅ PASSED' : '❌ FAILED');
  
  const passedCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`\n🎯 [ENCRYPTION TEST] Overall: ${passedCount}/${totalCount} tests passed`);
  
  if (passedCount === totalCount) {
    console.log('🎉 [ENCRYPTION TEST] All tests PASSED!');
  } else {
    console.log('⚠️ [ENCRYPTION TEST] Some tests failed. Check the logs above.');
  }
  
  return results;
}

// Expose functions globally
window.encryptionTest = {
  runAll: runAllTests,
  testCrypto: testEncryptionCycle,
  testContext: testEncryptionContext,
  testMessage: testMessageSending
};

console.log('✅ [ENCRYPTION TEST] Test functions loaded!');
console.log('📋 [ENCRYPTION TEST] Available commands:');
console.log('  encryptionTest.runAll() - Run all tests');
console.log('  encryptionTest.testCrypto() - Test basic crypto functions');
console.log('  encryptionTest.testContext() - Test encryption context');
console.log('  encryptionTest.testMessage() - Test message encryption');
console.log('\n🚀 [ENCRYPTION TEST] Run encryptionTest.runAll() to start testing!');
