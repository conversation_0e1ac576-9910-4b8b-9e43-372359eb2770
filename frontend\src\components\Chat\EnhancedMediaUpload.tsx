// frontend/src/components/Chat/EnhancedMediaUpload.tsx
import React, { useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Plus } from 'lucide-react';
import MediaUploadDialog from './MediaUploadDialog';
import MediaPreviewModal from './MediaPreviewModal';
import { mediaApi, type UploadProgress } from '../../services/mediaApi';
import { useSocket } from '../../contexts/SocketContext';
import { useMediaEncryption } from '../../hooks/useMediaEncryption';
import {
  showUploadDialog,
  hideUploadDialog,
  showPreview,
  hidePreview,
  createUploadSession,
  updateSessionMessageId,
  removeFileFromSession,
  updateFileStatus,
  updateSessionStatus,
  setFilePreview,
  clearSession,
  retryFailedUploads,
  selectActiveSession,
  selectShowUploadDialog,
  selectShowPreview,
  type MediaUploadFile,
} from '../../store/slices/mediaUploadSlice';
import { fileManager } from '../../utils/fileManager';
import type { RootState } from '../../store';

interface EnhancedMediaUploadProps {
  conversationId: string;
  messageId?: string;
  onUploadComplete?: (mediaFiles: any[]) => void;
  onUploadError?: (error: string) => void;
  disabled?: boolean;
}

const EnhancedMediaUpload: React.FC<EnhancedMediaUploadProps> = ({
  conversationId,
  messageId,
  onUploadComplete,
  onUploadError,
  disabled = false,
}) => {
  const dispatch = useDispatch();
  const { socket } = useSocket();
  const { encryptFile, isReady: encryptionReady } = useMediaEncryption(conversationId);
  
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  
  const showDialog = useSelector(selectShowUploadDialog);
  const showPreviewModal = useSelector(selectShowPreview);
  const activeSession = useSelector(selectActiveSession);

  const handleOpenDialog = () => {
    if (!disabled) {
      dispatch(showUploadDialog());
    }
  };

  const handleCloseDialog = () => {
    dispatch(hideUploadDialog());
  };

  const handleFileSelect = useCallback(async (files: File[], source: 'file' | 'image' | 'camera') => {
    console.log('🔍 File selection started:', files.length, 'files');
    if (files.length === 0) return;

    try {
      // Create upload session
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      console.log('📝 Creating session:', sessionId);

      // Store files in fileManager and get their IDs
      const fileIds = fileManager.storeFiles(files);
      console.log('💾 Files stored with IDs:', fileIds);

      // Create file metadata for Redux
      const fileMetadata = files.map((file, index) => ({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
        fileId: fileIds[index],
      }));

      dispatch(createUploadSession({
        sessionId,
        conversationId,
        fileMetadata,
      }));

      setCurrentSessionId(sessionId);
      console.log('🏪 Session created in Redux');

      // Generate previews for media files
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileId = fileIds[i];
        const uploadFileId = `${sessionId}-${i}`;

        if (file.type.startsWith('image/') || file.type.startsWith('video/')) {
          try {
            const preview = fileManager.createPreview(fileId);
            if (preview) {
              dispatch(setFilePreview({ sessionId, fileId: uploadFileId, preview }));
              console.log('🖼️ Preview created for:', file.name);
            }
          } catch (error) {
            console.warn('Failed to create preview for file:', file.name, error);
          }
        }
      }

      // Show preview modal
      console.log('🎭 Showing preview modal');
      dispatch(showPreview());
    } catch (error) {
      console.error('❌ Error in file selection:', error);
      onUploadError?.('Failed to process selected files');
    }
  }, [dispatch, conversationId, onUploadError]);

  const handleClosePreview = () => {
    dispatch(hidePreview());
    // Clean up session after a delay to allow for animations
    setTimeout(() => {
      if (activeSession) {
        dispatch(clearSession(activeSession.id));
      }
    }, 300);
  };

  const handleRemoveFile = (fileId: string) => {
    if (activeSession) {
      dispatch(removeFileFromSession({ sessionId: activeSession.id, fileId }));
    }
  };

  const handleSendFiles = async () => {
    if (!activeSession || activeSession.files.length === 0) return;

    console.log('🚀 Starting upload for session:', activeSession.id);

    try {
      // Update session status to uploading
      dispatch(updateSessionStatus({ 
        sessionId: activeSession.id, 
        status: 'uploading' 
      }));

      // Create message if not provided
      let currentMessageId = messageId;
      if (!currentMessageId) {
        // This would typically be handled by creating a message through the socket
        currentMessageId = `temp_${Date.now()}`;
        dispatch(updateSessionMessageId({ 
          sessionId: activeSession.id, 
          messageId: currentMessageId 
        }));
      }

      // Upload each file using fileManager
      const uploadPromises = activeSession.files.map(async (uploadFile) => {
        const file = fileManager.getFile(uploadFile.fileId);
        if (!file) {
          console.error('❌ File not found in fileManager:', uploadFile.fileId);
          throw new Error(`File not found: ${uploadFile.name}`);
        }

        const fileId = uploadFile.id;
        try {
          dispatch(updateFileStatus({
            sessionId: activeSession.id,
            fileId: fileId,
            status: 'encrypting',
            progress: 0,
          }));

          // Encrypt file using proper encryption system
          const encryptionResult = await encryptFile(file, (progress) => {
            // Update progress during encryption (0-50% of total progress)
            const encryptionProgress = Math.round(progress.progress * 0.5);
            dispatch(updateFileStatus({
              sessionId: activeSession.id,
              fileId: fileId,
              status: 'encrypting',
              progress: encryptionProgress,
            }));
          });

          const { encryptedData, nonce: fileNonce, wrappedFileKey, fileHash } = encryptionResult;

          dispatch(updateFileStatus({
            sessionId: activeSession.id,
            fileId: fileId,
            status: 'uploading',
            progress: 0,
          }));

          const progressHandler = (progress: UploadProgress) => {
            dispatch(updateFileStatus({
              sessionId: activeSession.id,
              fileId: fileId,
              status: 'uploading',
              progress: progress.percentage,
            }));

            // Notify socket of progress
            socket?.emit('media_upload_progress', {
              conversationId,
              messageId: currentMessageId,
              progress: progress.percentage,
              tempId: fileId
            });
          };

          // Create encrypted file blob for upload
          const encryptedFile = new File(
            [encryptedData],
            `${file.name}.enc`,
            { type: 'application/octet-stream' }
          );

          // Choose upload method based on original file size
          let result;
          if (mediaApi.shouldUseChunkedUpload(file)) {
            result = await mediaApi.uploadChunked(
              currentMessageId,
              encryptedFile,
              wrappedFileKey,
              fileNonce,
              fileHash,
              (progress) => {
                // Upload progress is 50-100% of total progress
                const uploadProgress = Math.round(50 + (progress.percentage * 0.5));
                progressHandler({ ...progress, percentage: uploadProgress });
              }
            );
          } else {
            result = await mediaApi.uploadSimple(
              currentMessageId,
              encryptedFile,
              wrappedFileKey,
              fileNonce,
              fileHash,
              (progress) => {
                // Upload progress is 50-100% of total progress
                const uploadProgress = Math.round(50 + (progress.percentage * 0.5));
                progressHandler({ ...progress, percentage: uploadProgress });
              }
            );
          }

          dispatch(updateFileStatus({
            sessionId: activeSession.id,
            fileId: fileId,
            status: 'completed',
            progress: 100,
            mediaFileId: result.id,
          }));

          // Notify socket of completion
          socket?.emit('media_upload_completed', {
            conversationId,
            messageId: currentMessageId,
            mediaFileId: result.id,
            tempId: fileId
          });

          return result;

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Upload failed';
          
          dispatch(updateFileStatus({
            sessionId: activeSession.id,
            fileId: fileId,
            status: 'error',
            error: errorMessage,
          }));

          // Notify socket of error
          socket?.emit('media_upload_error', {
            conversationId,
            messageId: currentMessageId,
            error: errorMessage,
            tempId: fileId
          });

          throw error;
        }
      });

      // Wait for all uploads to complete
      const results = await Promise.allSettled(uploadPromises);
      const successful = results.filter(r => r.status === 'fulfilled').map(r => (r as PromiseFulfilledResult<any>).value);
      const failed = results.filter(r => r.status === 'rejected');

      if (successful.length > 0) {
        onUploadComplete?.(successful);
      }

      if (failed.length > 0) {
        const errorMessage = `${failed.length} file(s) failed to upload`;
        dispatch(updateSessionStatus({ 
          sessionId: activeSession.id, 
          status: 'error',
          error: errorMessage 
        }));
        onUploadError?.(errorMessage);
      } else {
        dispatch(updateSessionStatus({ 
          sessionId: activeSession.id, 
          status: 'completed' 
        }));
        
        // Close preview after successful upload
        setTimeout(() => {
          dispatch(hidePreview());
          dispatch(clearSession(activeSession.id));
        }, 1000);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      dispatch(updateSessionStatus({ 
        sessionId: activeSession.id, 
        status: 'error',
        error: errorMessage 
      }));
      onUploadError?.(errorMessage);
    }
  };

  const handleRetry = () => {
    if (activeSession) {
      dispatch(retryFailedUploads(activeSession.id));
      // Restart the upload process
      handleSendFiles();
    }
  };

  // The MediaPreviewModal expects MediaUploadFile[] directly
  const previewFiles = activeSession?.files || [];

  return (
    <>
      {/* Plus button */}
      <button
        onClick={handleOpenDialog}
        disabled={disabled}
        className={`p-2 rounded-lg transition-colors ${
          disabled 
            ? 'text-gray-400 cursor-not-allowed' 
            : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
        }`}
        title="Share media"
        data-testid="media-upload-button"
      >
        <Plus className="w-5 h-5" />
      </button>

      {/* Upload Dialog */}
      <MediaUploadDialog
        isOpen={showDialog}
        onClose={handleCloseDialog}
        onFileSelect={handleFileSelect}
      />

      {/* Preview Modal */}
      <MediaPreviewModal
        isOpen={showPreviewModal}
        files={previewFiles}
        onClose={handleClosePreview}
        onSend={handleSendFiles}
        onRemoveFile={handleRemoveFile}
        uploading={activeSession?.status === 'uploading'}
        uploadProgress={activeSession?.totalProgress || 0}
        uploadError={activeSession?.error}
        onRetry={handleRetry}
      />
    </>
  );
};

export default EnhancedMediaUpload;
