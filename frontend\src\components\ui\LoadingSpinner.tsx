// frontend/src/components/ui/LoadingSpinner.tsx
import React from 'react';
import { Icon } from './Icon';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
  centered?: boolean;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  text,
  className = '',
  centered = false,
}) => {
  const sizeClasses = {
    sm: { icon: 16, text: 'text-sm' },
    md: { icon: 20, text: 'text-base' },
    lg: { icon: 24, text: 'text-lg' },
  };

  const styles = sizeClasses[size];
  
  const content = (
    <div className={`flex items-center ${text ? 'space-x-2' : ''} ${className}`}>
      <Icon 
        name="loader" 
        size={styles.icon} 
        className="animate-spin text-gray-400" 
      />
      {text && (
        <span className={`text-gray-600 ${styles.text}`}>
          {text}
        </span>
      )}
    </div>
  );

  if (centered) {
    return (
      <div className="flex items-center justify-center p-4">
        {content}
      </div>
    );
  }

  return content;
};
