// socket-server/src/test/setup.ts
import { beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { mockDeep, mockReset, DeepMockProxy } from 'vitest-mock-extended'

// Mock Prisma Client
export const prismaMock = mockDeep<PrismaClient>()

vi.mock('@prisma/client', () => ({
  PrismaClient: vi.fn().mockImplementation(() => prismaMock)
}))

// Mock Redis
export const redisMock = {
  get: vi.fn(),
  set: vi.fn(),
  del: vi.fn(),
  exists: vi.fn(),
  expire: vi.fn(),
  connect: vi.fn(),
  disconnect: vi.fn(),
  on: vi.fn(),
  quit: vi.fn()
}

vi.mock('redis', () => ({
  createClient: vi.fn().mockImplementation(() => redisMock)
}))

// Mock JWT
vi.mock('jsonwebtoken', () => ({
  default: {
    verify: vi.fn(),
    sign: vi.fn(),
    decode: vi.fn()
  }
}))

// Mock environment variables
process.env.JWT_SECRET = 'test-jwt-secret'
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test'
process.env.REDIS_URL = 'redis://localhost:6379'

beforeEach(() => {
  mockReset(prismaMock)
  vi.clearAllMocks()
})

afterEach(() => {
  vi.restoreAllMocks()
})
