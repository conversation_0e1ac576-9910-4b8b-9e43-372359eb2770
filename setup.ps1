# Chat Application Setup Script
# PowerShell equivalent of Makefile for Windows

# Parameters must be at the top of the script
param(
    [Parameter(Position=0)]
    [string]$Command = "help"
)

# Color functions for better output
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }

# Help function
function Show-Help {
    Write-Host "Chat Application Setup Script" -ForegroundColor Yellow
    Write-Host "============================" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Available commands:" -ForegroundColor White
    Write-Host "  .\setup.ps1 start        - Start all services in parallel" -ForegroundColor Green
    Write-Host "  .\setup.ps1 stop         - Stop all services" -ForegroundColor Green
    Write-Host "  .\setup.ps1 backend      - Start Django backend only" -ForegroundColor Green
    Write-Host "  .\setup.ps1 frontend     - Start React frontend only" -ForegroundColor Green
    Write-Host "  .\setup.ps1 socket       - Start Socket server only" -ForegroundColor Green
    Write-Host "  .\setup.ps1 install      - Install all dependencies" -ForegroundColor Green
    Write-Host "  .\setup.ps1 check        - Check dependencies" -ForegroundColor Green
    Write-Host "  .\setup.ps1 clean        - Clean up logs and temp files" -ForegroundColor Green
    Write-Host "  .\setup.ps1 help         - Show this help" -ForegroundColor Green
    Write-Host ""
    Write-Host "Services will run on:" -ForegroundColor White
    Write-Host "  - Django Backend:  http://localhost:6000" -ForegroundColor Cyan
    Write-Host "  - React Frontend:  http://localhost:5000" -ForegroundColor Cyan
    Write-Host "  - Socket Server:   http://localhost:7000" -ForegroundColor Cyan
}

# Check dependencies function
function Test-Dependencies {
    Write-Info "Checking dependencies..."
    
    # Check Python virtual environment
    Write-Info "Checking Python virtual environment..."
    if (-not (Test-Path "backend\venv\Scripts\Activate.ps1")) {
        Write-Error "❌ Python virtual environment not found in backend/venv/"
        Write-Warning "Run: cd backend && python -m venv venv"
        return $false
    }
    Write-Success "✅ Python virtual environment found"
    
    # Check pnpm
    Write-Info "Checking pnpm installation..."
    try {
        $null = Get-Command pnpm -ErrorAction Stop
        Write-Success "✅ pnpm is installed"
    } catch {
        Write-Error "❌ pnpm is not installed"
        Write-Warning "Install pnpm: npm install -g pnpm"
        return $false
    }
    
    # Check frontend dependencies
    Write-Info "Checking frontend dependencies..."
    if (-not (Test-Path "frontend\package.json")) {
        Write-Error "❌ frontend/package.json not found"
        return $false
    }
    if (-not (Test-Path "frontend\node_modules")) {
        Write-Warning "⚠️ Frontend dependencies not installed"
        Write-Info "Run: cd frontend && pnpm install"
    } else {
        Write-Success "✅ Frontend dependencies found"
    }
    
    # Check socket-server dependencies
    Write-Info "Checking socket-server dependencies..."
    if (-not (Test-Path "socket-server\package.json")) {
        Write-Error "❌ socket-server/package.json not found"
        return $false
    }
    if (-not (Test-Path "socket-server\node_modules")) {
        Write-Warning "⚠️ Socket-server dependencies not installed"
        Write-Info "Run: cd socket-server && pnpm install"
    } else {
        Write-Success "✅ Socket-server dependencies found"
    }
    
    Write-Success "✅ All dependencies checked"
    return $true
}

# Install dependencies function
function Install-Dependencies {
    Write-Info "Installing dependencies..."
    
    # Setup Python virtual environment
    Write-Info "Setting up Python virtual environment..."
    if (-not (Test-Path "backend\venv")) {
        Set-Location backend
        python -m venv venv
        Set-Location ..
        Write-Success "✅ Python virtual environment created"
    }
    
    # Install Python dependencies
    Write-Info "Installing Python dependencies..."
    Set-Location backend
    .\venv\Scripts\Activate.ps1
    if (Test-Path "requirements.txt") {
        pip install -r requirements.txt
        Write-Success "✅ Python dependencies installed"
    } else {
        Write-Warning "⚠️ No requirements.txt found in backend/"
    }
    deactivate
    Set-Location ..
    
    # Install frontend dependencies
    Write-Info "Installing frontend dependencies..."
    if (Test-Path "frontend\package.json") {
        Set-Location frontend
        pnpm install
        Set-Location ..
        Write-Success "✅ Frontend dependencies installed"
    }
    
    # Install socket-server dependencies
    Write-Info "Installing socket-server dependencies..."
    if (Test-Path "socket-server\package.json") {
        Set-Location socket-server
        pnpm install
        Set-Location ..
        Write-Success "✅ Socket-server dependencies installed"
    }
    
    Write-Success "✅ All dependencies installed successfully"
}

# Start individual services
function Start-Backend {
    Write-Info "Starting Django backend on port 6000..."
    Start-Process -FilePath "cmd" -ArgumentList "/k", "cd backend && .\venv\Scripts\activate && python manage.py runserver 6000" -WindowStyle Normal
}

function Start-Frontend {
    Write-Info "Starting React frontend on port 5000..."
    Start-Process -FilePath "cmd" -ArgumentList "/k", "cd frontend && pnpm run dev" -WindowStyle Normal
}

function Start-SocketServer {
    Write-Info "Starting Socket server on port 7000..."
    Start-Process -FilePath "cmd" -ArgumentList "/k", "cd socket-server && pnpm run dev" -WindowStyle Normal
}

# Start all services
function Start-AllServices {
    if (-not (Test-Dependencies)) {
        Write-Error "❌ Dependency check failed. Please fix the issues above."
        return
    }
    
    Write-Host "🚀 Starting Chat Application Services" -ForegroundColor Yellow
    Write-Host "====================================" -ForegroundColor Yellow
    Write-Host "Starting all services in parallel..." -ForegroundColor White
    Write-Host "Services will be available at:" -ForegroundColor White
    Write-Host "  - Django Backend:  http://localhost:6000" -ForegroundColor Cyan
    Write-Host "  - React Frontend:  http://localhost:5000" -ForegroundColor Cyan
    Write-Host "  - Socket Server:   http://localhost:7000" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Starting services in separate windows..." -ForegroundColor White
    Write-Host "Press Ctrl+C in each window to stop services" -ForegroundColor Yellow
    Write-Host ""
    
    Start-Backend
    Start-Sleep -Seconds 2
    Start-Frontend
    Start-Sleep -Seconds 2
    Start-SocketServer
    
    Write-Success "✅ All services started in separate command windows!"
    Write-Info "Check each window for service status and logs"
}

function Start-AllServices {
    if (-not (Test-Dependencies)) {
        Write-Error "❌ Dependency check failed. Please fix the issues above."
        return
    }

    Write-Host "🚀 Starting all services in ONE window" -ForegroundColor Yellow

    Start-Backend
    Start-Frontend
    Start-SocketServer

    # Keep console alive and stream logs
    Write-Host "`nPress Ctrl+C to stop all services" -ForegroundColor Yellow
    Wait-Job *
}


# Stop services
function Stop-AllServices {
    Write-Info "Stopping all services..."
    Write-Warning "Please close the service windows manually or use Task Manager"
    Write-Info "to stop the following processes:"
    Write-Info "  - Django Backend (python.exe)"
    Write-Info "  - React Frontend (node.exe)"
    Write-Info "  - Socket Server (node.exe)"
    
    Write-Host ""
    Write-Info "Current Python and Node processes:"
    Get-Process | Where-Object {$_.ProcessName -eq 'python' -or $_.ProcessName -eq 'node'} | Select-Object Id,ProcessName,CommandLine | Format-Table -AutoSize
}

# Clean up function
function Clear-TempFiles {
    Write-Info "Cleaning up..."
    Remove-Item -Path "*.log" -Force -ErrorAction SilentlyContinue
    Remove-Item -Path "*.pid" -Force -ErrorAction SilentlyContinue
    Write-Success "✅ Cleanup completed"
}

# Main script logic
switch ($Command.ToLower()) {
    "start" { Start-AllServices }
    "stop" { Stop-AllServices }
    "backend" { Start-Backend }
    "frontend" { Start-Frontend }
    "socket" { Start-SocketServer }
    "install" { Install-Dependencies }
    "check" { Test-Dependencies }
    "clean" { Clear-TempFiles }
    "help" { Show-Help }
    default { 
        Write-Error "Unknown command: $Command"
        Show-Help 
    }
}