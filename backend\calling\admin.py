# backend/calling/admin.py
from django.contrib import admin
from .models import Call, CallParticipant, CallEvent, CallQualityMetric


@admin.register(Call)
class CallAdmin(admin.ModelAdmin):
    list_display = ['id', 'caller', 'callee', 'call_type', 'status', 'initiated_at', 'duration']
    list_filter = ['call_type', 'status', 'initiated_at']
    search_fields = ['caller__username', 'callee__username', 'session_id']
    readonly_fields = ['id', 'session_id', 'initiated_at', 'duration']
    ordering = ['-initiated_at']


@admin.register(CallParticipant)
class CallParticipantAdmin(admin.ModelAdmin):
    list_display = ['id', 'call', 'user', 'status', 'joined_at', 'left_at']
    list_filter = ['status', 'audio_enabled', 'video_enabled', 'screen_sharing']
    search_fields = ['call__id', 'user__username']
    readonly_fields = ['id', 'joined_at', 'left_at']


@admin.register(CallEvent)
class CallEventAdmin(admin.ModelAdmin):
    list_display = ['id', 'call', 'event_type', 'user', 'timestamp']
    list_filter = ['event_type', 'timestamp']
    search_fields = ['call__id', 'user__username']
    readonly_fields = ['id', 'timestamp']
    ordering = ['-timestamp']


@admin.register(CallQualityMetric)
class CallQualityMetricAdmin(admin.ModelAdmin):
    list_display = ['id', 'call', 'user', 'packet_loss', 'jitter', 'round_trip_time', 'recorded_at']
    list_filter = ['recorded_at']
    search_fields = ['call__id', 'user__username']
    readonly_fields = ['id', 'recorded_at']
    ordering = ['-recorded_at']
