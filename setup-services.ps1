# Comprehensive Service Setup Script for Chat Application
# This script starts Django backend, React frontend, and Node.js socket server in parallel

# Color functions for better output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

# Service configuration
$services = @(
    @{
        Name = "Django Backend"
        Directory = "backend"
        Port = 6000
        Command = "python manage.py runserver 6000"
        VenvPath = "venv\Scripts\Activate.ps1"
        Type = "Django"
    },
    @{
        Name = "React Frontend"
        Directory = "frontend"
        Port = 5000
        Command = "pnpm run dev"
        VenvPath = $null
        Type = "Node"
    },
    @{
        Name = "Socket Server"
        Directory = "socket-server"
        Port = 7000
        Command = "pnpm run dev"
        VenvPath = $null
        Type = "Node"
    }
)

# Global variables for process tracking
$global:runningJobs = @()
$global:serviceStatus = @{}

# Function to check if port is available
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $false  # Port is in use
    } catch {
        return $true   # Port is available
    }
}

# Function to start a service
function Start-Service {
    param($ServiceConfig)
    
    $serviceName = $ServiceConfig.Name
    $serviceDir = $ServiceConfig.Directory
    $port = $ServiceConfig.Port
    $command = $ServiceConfig.Command
    $venvPath = $ServiceConfig.VenvPath
    $serviceType = $ServiceConfig.Type
    
    Write-ColorOutput Yellow "Starting $serviceName on port $port..."
    
    # Check if port is available
    if (-not (Test-Port -Port $port)) {
        Write-ColorOutput Red "❌ Port $port is already in use. Cannot start $serviceName."
        $global:serviceStatus[$serviceName] = "Failed - Port in use"
        return $null
    }
    
    # Check if directory exists
    if (-not (Test-Path $serviceDir)) {
        Write-ColorOutput Red "❌ Directory '$serviceDir' not found. Cannot start $serviceName."
        $global:serviceStatus[$serviceName] = "Failed - Directory not found"
        return $null
    }
    
    # Prepare the command based on service type
    $fullCommand = ""
    
    if ($serviceType -eq "Django") {
        # Check if virtual environment exists
        $venvFullPath = Join-Path $serviceDir $venvPath
        if (-not (Test-Path $venvFullPath)) {
            Write-ColorOutput Red "❌ Virtual environment not found at '$venvFullPath'. Cannot start $serviceName."
            $global:serviceStatus[$serviceName] = "Failed - Virtual environment not found"
            return $null
        }
        
        # Django service with virtual environment
        $fullCommand = "cd '$serviceDir'; & '$venvPath'; $command"
    } else {
        # Node.js services
        # Check if package.json exists
        $packageJsonPath = Join-Path $serviceDir "package.json"
        if (-not (Test-Path $packageJsonPath)) {
            Write-ColorOutput Red "❌ package.json not found in '$serviceDir'. Cannot start $serviceName."
            $global:serviceStatus[$serviceName] = "Failed - package.json not found"
            return $null
        }
        
        # Check if pnpm is installed
        try {
            $null = Get-Command pnpm -ErrorAction Stop
        } catch {
            Write-ColorOutput Red "❌ pnpm is not installed or not in PATH. Cannot start $serviceName."
            $global:serviceStatus[$serviceName] = "Failed - pnpm not found"
            return $null
        }
        
        $fullCommand = "cd '$serviceDir'; $command"
    }
    
    try {
        # Start the service as a background job
        $job = Start-Job -ScriptBlock {
            param($cmd, $serviceName)
            try {
                Invoke-Expression $cmd
            } catch {
                Write-Error "Error starting $serviceName`: $($_.Exception.Message)"
            }
        } -ArgumentList $fullCommand, $serviceName
        
        # Wait a moment to check if the job started successfully
        Start-Sleep -Seconds 2
        
        if ($job.State -eq "Running") {
            Write-ColorOutput Green "✅ $serviceName started successfully (Job ID: $($job.Id))"
            $global:serviceStatus[$serviceName] = "Running"
            return $job
        } else {
            Write-ColorOutput Red "❌ Failed to start $serviceName"
            $global:serviceStatus[$serviceName] = "Failed to start"
            return $null
        }
    } catch {
        Write-ColorOutput Red "❌ Error starting $serviceName`: $($_.Exception.Message)"
        $global:serviceStatus[$serviceName] = "Error: $($_.Exception.Message)"
        return $null
    }
}

# Function to display service status
function Show-ServiceStatus {
    Write-ColorOutput Cyan "`n=== Service Status ==="
    foreach ($service in $services) {
        $status = $global:serviceStatus[$service.Name]
        $statusColor = if ($status -eq "Running") { "Green" } else { "Red" }
        Write-ColorOutput $statusColor "$($service.Name) (Port $($service.Port)): $status"
    }
    Write-ColorOutput Cyan "=====================`n"
}

# Function to cleanup jobs
function Stop-AllServices {
    Write-ColorOutput Yellow "`nStopping all services..."
    
    foreach ($job in $global:runningJobs) {
        if ($job -and $job.State -eq "Running") {
            try {
                Stop-Job -Job $job -Force
                Remove-Job -Job $job -Force
                Write-ColorOutput Green "✅ Stopped job $($job.Id)"
            } catch {
                Write-ColorOutput Red "❌ Error stopping job $($job.Id): $($_.Exception.Message)"
            }
        }
    }
    
    Write-ColorOutput Green "All services stopped."
}

# Trap Ctrl+C to gracefully shutdown
$null = Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action {
    Stop-AllServices
}

# Handle Ctrl+C
trap {
    Write-ColorOutput Yellow "`nReceived interrupt signal. Shutting down services..."
    Stop-AllServices
    exit 0
}

# Main execution
Write-ColorOutput Cyan "🚀 Chat Application Service Setup"
Write-ColorOutput Cyan "================================="

# Initialize service status
foreach ($service in $services) {
    $global:serviceStatus[$service.Name] = "Not started"
}

# Start all services
foreach ($service in $services) {
    $job = Start-Service -ServiceConfig $service
    if ($job) {
        $global:runningJobs += $job
    }
    Start-Sleep -Seconds 1  # Brief pause between service starts
}

# Display initial status
Show-ServiceStatus

# Check if any services are running
$runningServices = ($global:serviceStatus.Values | Where-Object { $_ -eq "Running" }).Count

if ($runningServices -eq 0) {
    Write-ColorOutput Red "❌ No services started successfully. Exiting..."
    exit 1
}

Write-ColorOutput Green "✅ $runningServices out of $($services.Count) services started successfully."
Write-ColorOutput Cyan "Press Ctrl+C to stop all services and exit."
Write-ColorOutput Cyan "Services will continue running in the background..."`n
# Keep the script running and monitor services
try {
    while ($true) {
        Start-Sleep -Seconds 10
        
        # Check if any jobs have failed
        $activeJobs = $global:runningJobs | Where-Object { $_.State -eq "Running" }
        
        if ($activeJobs.Count -lt $runningServices) {
            Write-ColorOutput Yellow "⚠️  Some services may have stopped. Current status:"
            Show-ServiceStatus
        }
        
        # Update service status based on job state
        foreach ($job in $global:runningJobs) {
            if ($job.State -ne "Running") {
                $serviceName = ($services | Where-Object { $global:serviceStatus[$_.Name] -eq "Running" })[0].Name
                if ($serviceName) {
                    $global:serviceStatus[$serviceName] = "Stopped"
                }
            }
        }
    }
} catch {
    Write-ColorOutput Red "Error in monitoring loop: $($_.Exception.Message)"
} finally {
    Stop-AllServices
}