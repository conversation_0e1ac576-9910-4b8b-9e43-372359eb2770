// Debug JWT token structure
const jwt = require('jsonwebtoken');

// Real JWT token from Django login
const sampleToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzU3MzExNzg0LCJpYXQiOjE3NTczMDgxODQsImp0aSI6ImRiMDk2OGM0YjliNzQ1YjY5YWY1NjRkOWZmMjZjNDExIiwidXNlcl9pZCI6ImEyZWMzNjBiLWI2ZjctNDRhMS04NmQyLWEwMDFkMGFkYmNiOCJ9.YMGcJm-V4RFo6t63ajoX3IXcV_I_x_Uk-mAvJ1LKOk0';

try {
    // Decode without verification to see the payload structure
    const decoded = jwt.decode(sampleToken);
    console.log('JWT Payload Structure:');
    console.log(JSON.stringify(decoded, null, 2));
    
    // Check what fields are available
    console.log('\nAvailable fields:');
    if (decoded) {
        Object.keys(decoded).forEach(key => {
            console.log(`- ${key}: ${decoded[key]}`);
        });
    }
    
    // Check if user_id exists
    if (decoded && decoded.user_id) {
        console.log(`\n✅ user_id found: ${decoded.user_id}`);
    } else {
        console.log('\n❌ user_id not found in JWT payload');
        console.log('Available user-related fields:');
        if (decoded) {
            Object.keys(decoded).forEach(key => {
                if (key.toLowerCase().includes('user') || key.toLowerCase().includes('id')) {
                    console.log(`- ${key}: ${decoded[key]}`);
                }
            });
        }
    }
} catch (error) {
    console.error('Error decoding JWT:', error.message);
}

// Test with JWT_SECRET from .env
const JWT_SECRET = 'your-jwt-secret-key';
console.log('\n--- Testing with JWT_SECRET ---');
try {
    const verified = jwt.verify(sampleToken, JWT_SECRET);
    console.log('Verified JWT payload:');
    console.log(JSON.stringify(verified, null, 2));
} catch (error) {
    console.log('JWT verification failed (expected with sample token):', error.message);
}