// frontend/src/components/Debug/WebRTCTestingPanel.tsx
import React, { useState, useEffect } from 'react';
import { Settings, Mic, Video, Phone, PhoneOff, Eye, EyeOff } from 'lucide-react';

interface SocketEvent {
  timestamp: string;
  type: 'incoming' | 'outgoing';
  event: string;
  data: any;
}

export const WebRTCTestingPanel: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [mockMode, setMockMode] = useState(
    localStorage.getItem('webrtc_mock_mode') === 'true' || 
    window.location.search.includes('mock=true')
  );
  const [socketEvents, setSocketEvents] = useState<SocketEvent[]>([]);

  // Override console.log to capture socket events
  useEffect(() => {
    const originalLog = console.log;
    console.log = (...args: any[]) => {
      // Capture socket events
      if (args[0] && typeof args[0] === 'string') {
        if (args[0].includes('Socket event received:') || args[0].includes('Socket event emitted:')) {
          const isOutgoing = args[0].includes('emitted');
          const eventName = args[0].split(':')[1]?.trim() || 'unknown';
          
          setSocketEvents(prev => [
            ...prev.slice(-19), // Keep last 20 events
            {
              timestamp: new Date().toLocaleTimeString(),
              type: isOutgoing ? 'outgoing' : 'incoming',
              event: eventName,
              data: args[1] || {}
            }
          ]);
        }
      }
      
      // Call original console.log
      originalLog(...args);
    };

    return () => {
      console.log = originalLog;
    };
  }, []);

  const toggleMockMode = () => {
    const newMockMode = !mockMode;
    setMockMode(newMockMode);
    
    if (newMockMode) {
      localStorage.setItem('webrtc_mock_mode', 'true');
    } else {
      localStorage.removeItem('webrtc_mock_mode');
    }
    
    // Show notification that page reload is needed
    alert(`Mock mode ${newMockMode ? 'enabled' : 'disabled'}. Please reload the page for changes to take effect.`);
  };

  const clearEvents = () => {
    setSocketEvents([]);
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-500 text-white p-2 rounded-full shadow-lg hover:bg-blue-600 z-50"
        title="Show WebRTC Testing Panel"
      >
        <Settings className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 w-96 max-h-96 overflow-hidden z-50">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold">WebRTC Testing</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
          title="Hide panel"
        >
          <EyeOff className="w-5 h-5" />
        </button>
      </div>

      {/* Mock Mode Toggle */}
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Mock Mode</span>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={mockMode}
              onChange={toggleMockMode}
              className="sr-only"
            />
            <div className={`w-11 h-6 rounded-full transition-colors ${
              mockMode ? 'bg-blue-500' : 'bg-gray-200'
            }`}>
              <div className={`w-5 h-5 bg-white rounded-full shadow transition-transform ${
                mockMode ? 'translate-x-5' : 'translate-x-0'
              } mt-0.5 ml-0.5`} />
            </div>
          </label>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          {mockMode ? '🎭 Mock streams enabled (no camera/mic needed)' : '📹 Real media devices required'}
        </p>
      </div>

      {/* Media Device Status */}
      <div className="mb-4">
        <h4 className="text-sm font-medium mb-2">Current Status</h4>
        <div className="flex items-center space-x-2 text-xs">
          {mockMode ? (
            <>
              <div className="flex items-center text-green-600">
                <Mic className="w-3 h-3 mr-1" />
                Mock Audio
              </div>
              <div className="flex items-center text-green-600">
                <Video className="w-3 h-3 mr-1" />
                Mock Video
              </div>
            </>
          ) : (
            <div className="text-gray-500">Real device detection needed</div>
          )}
        </div>
      </div>

      {/* Socket Events */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-sm font-medium">Socket Events</h4>
          <button
            onClick={clearEvents}
            className="text-xs text-blue-500 hover:text-blue-700"
          >
            Clear
          </button>
        </div>
        <div className="max-h-40 overflow-y-auto text-xs space-y-1">
          {socketEvents.length === 0 ? (
            <div className="text-gray-500">No events captured yet</div>
          ) : (
            socketEvents.map((event, index) => (
              <div
                key={index}
                className={`p-2 rounded text-xs ${
                  event.type === 'outgoing' 
                    ? 'bg-blue-50 border-l-2 border-blue-300' 
                    : 'bg-green-50 border-l-2 border-green-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="font-medium">
                    {event.type === 'outgoing' ? '↗️' : '↙️'} {event.event}
                  </span>
                  <span className="text-gray-500">{event.timestamp}</span>
                </div>
                {event.data && Object.keys(event.data).length > 0 && (
                  <pre className="text-xs text-gray-600 mt-1 whitespace-pre-wrap">
                    {JSON.stringify(event.data, null, 2).slice(0, 200)}
                    {JSON.stringify(event.data).length > 200 ? '...' : ''}
                  </pre>
                )}
              </div>
            ))
          )}
        </div>
      </div>

      {/* Testing Instructions */}
      <div className="text-xs text-gray-600">
        <p><strong>Instructions:</strong></p>
        <ul className="list-disc list-inside space-y-1 mt-1">
          <li>Enable Mock Mode to test without camera/mic</li>
          <li>Try making calls to test socket events</li>
          <li>Check console for detailed logs</li>
          <li>Socket events will appear above in real-time</li>
        </ul>
      </div>
    </div>
  );
};
