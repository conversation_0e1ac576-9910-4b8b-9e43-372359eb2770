# Generated by Django 5.2.4 on 2025-08-31 07:28

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('messaging', '0004_groupevent_groupinvite_conversation_avatar_url_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='message',
            name='has_media',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AddField(
            model_name='message',
            name='media_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['conversation', 'has_media', 'created_at'], name='messages_convers_b998f9_idx'),
        ),
    ]
