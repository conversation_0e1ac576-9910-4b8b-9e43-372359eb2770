// frontend/src/components/Chat/MediaUploadDialog.tsx
import React, { useRef, useState } from 'react';
import { X, Upload, Image, Camera, File, Video, Music } from 'lucide-react';

interface MediaUploadDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onFileSelect: (files: File[], source: 'file' | 'image' | 'camera') => void;
}

const MediaUploadDialog: React.FC<MediaUploadDialogProps> = ({
  isOpen,
  onClose,
  onFileSelect,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const [showCamera, setShowCamera] = useState(false);

  if (!isOpen) return null;

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleImageUpload = () => {
    imageInputRef.current?.click();
  };

  const handleCameraCapture = () => {
    setShowCamera(true);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>, source: 'file' | 'image') => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      onFileSelect(files, source);
      onClose();
    }
  };

  const uploadOptions = [
    {
      id: 'file',
      label: 'Upload file from device',
      description: 'Documents, archives, and other files',
      icon: File,
      onClick: handleFileUpload,
      accept: '.pdf,.doc,.docx,.txt,.zip,.rar,.7z',
    },
    {
      id: 'image',
      label: 'Upload image from device',
      description: 'Photos and images',
      icon: Image,
      onClick: handleImageUpload,
      accept: 'image/*',
    },
    {
      id: 'camera',
      label: 'Take photo using camera',
      description: 'Capture a new photo',
      icon: Camera,
      onClick: handleCameraCapture,
    },
    {
      id: 'video',
      label: 'Upload video',
      description: 'Video files',
      icon: Video,
      onClick: () => {
        // Create a temporary input for video files
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'video/*';
        input.onchange = (e) => {
          const files = Array.from((e.target as HTMLInputElement).files || []);
          if (files.length > 0) {
            onFileSelect(files, 'file');
            onClose();
          }
        };
        input.click();
      },
    },
    {
      id: 'audio',
      label: 'Upload audio',
      description: 'Audio files and recordings',
      icon: Music,
      onClick: () => {
        // Create a temporary input for audio files
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'audio/*';
        input.onchange = (e) => {
          const files = Array.from((e.target as HTMLInputElement).files || []);
          if (files.length > 0) {
            onFileSelect(files, 'file');
            onClose();
          }
        };
        input.click();
      },
    },
  ];

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      {/* Dialog */}
      <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Share Media</h2>
            <button
              onClick={onClose}
              className="p-1 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {/* Options */}
          <div className="p-4 space-y-2">
            {uploadOptions.map((option) => {
              const IconComponent = option.icon;
              return (
                <button
                  key={option.id}
                  onClick={option.onClick}
                  className="w-full flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors text-left"
                >
                  <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <IconComponent className="w-5 h-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{option.label}</div>
                    <div className="text-sm text-gray-500">{option.description}</div>
                  </div>
                </button>
              );
            })}
          </div>

          {/* Hidden file inputs */}
          <input
            ref={fileInputRef}
            type="file"
            multiple
            className="hidden"
            accept=".pdf,.doc,.docx,.txt,.zip,.rar,.7z"
            onChange={(e) => handleFileChange(e, 'file')}
          />
          <input
            ref={imageInputRef}
            type="file"
            multiple
            className="hidden"
            accept="image/*"
            onChange={(e) => handleFileChange(e, 'image')}
          />
        </div>
      </div>

      {/* Camera Modal */}
      {showCamera && (
        <CameraCapture
          onCapture={(file) => {
            onFileSelect([file], 'camera');
            setShowCamera(false);
            onClose();
          }}
          onClose={() => setShowCamera(false)}
        />
      )}
    </>
  );
};

// Simple Camera Capture Component
interface CameraCaptureProps {
  onCapture: (file: File) => void;
  onClose: () => void;
}

const CameraCapture: React.FC<CameraCaptureProps> = ({ onCapture, onClose }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [error, setError] = useState<string>('');

  React.useEffect(() => {
    startCamera();
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const startCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'user' } 
      });
      setStream(mediaStream);
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (err) {
      setError('Unable to access camera. Please check permissions.');
    }
  };

  const capturePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const canvas = canvasRef.current;
      const video = videoRef.current;
      
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(video, 0, 0);
        
        canvas.toBlob((blob) => {
          if (blob) {
            const file = new File([blob], `camera-${Date.now()}.jpg`, { type: 'image/jpeg' });
            onCapture(file);
          }
        }, 'image/jpeg', 0.8);
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black z-60 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-black text-white">
        <h2 className="text-lg font-semibold">Take Photo</h2>
        <button onClick={onClose} className="p-2 hover:bg-gray-800 rounded-full">
          <X className="w-5 h-5" />
        </button>
      </div>

      {/* Camera View */}
      <div className="flex-1 flex items-center justify-center bg-black">
        {error ? (
          <div className="text-white text-center">
            <p className="mb-4">{error}</p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Close
            </button>
          </div>
        ) : (
          <video
            ref={videoRef}
            autoPlay
            playsInline
            className="max-w-full max-h-full"
          />
        )}
      </div>

      {/* Controls */}
      {!error && (
        <div className="p-4 bg-black flex justify-center">
          <button
            onClick={capturePhoto}
            className="w-16 h-16 bg-white rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
          >
            <Camera className="w-8 h-8 text-gray-800" />
          </button>
        </div>
      )}

      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
};

export default MediaUploadDialog;
