# backend/encryption/crypto_utils.py
"""
Server-side cryptographic utilities for signature verification.
SECURITY: Server only handles public keys and signature verification.
Never handles private keys or performs decryption.
"""
import base64
import hashlib
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.primitives.serialization import load_der_public_key
from cryptography.exceptions import InvalidSignature
from typing import Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class CryptoError(Exception):
    """Base exception for cryptographic operations"""
    pass


class SignatureVerificationError(CryptoError):
    """Raised when signature verification fails"""
    pass


class KeyFormatError(CryptoError):
    """Raised when key format is invalid"""
    pass


def decode_base64_key(key_b64: str) -> bytes:
    """
    Decode base64 encoded key and validate format.
    
    Args:
        key_b64: Base64 encoded key
        
    Returns:
        Raw key bytes
        
    Raises:
        KeyFormatError: If key format is invalid
    """
    try:
        key_bytes = base64.b64decode(key_b64)
        if len(key_bytes) < 50:  # Minimum reasonable size for SPKI
            raise KeyFormatError("Key too short for SPKI format")
        return key_bytes
    except Exception as e:
        raise KeyFormatError(f"Invalid base64 key format: {e}")


def load_ecdsa_public_key(key_spki_bytes: bytes) -> ec.EllipticCurvePublicKey:
    """
    Load ECDSA P-256 public key from SPKI bytes.
    
    Args:
        key_spki_bytes: SPKI encoded public key bytes
        
    Returns:
        ECDSA public key object
        
    Raises:
        KeyFormatError: If key format is invalid or not P-256
    """
    try:
        public_key = load_der_public_key(key_spki_bytes)
        
        # Verify it's an ECDSA key with P-256 curve
        if not isinstance(public_key, ec.EllipticCurvePublicKey):
            raise KeyFormatError("Key is not an ECDSA public key")
        
        if not isinstance(public_key.curve, ec.SECP256R1):
            raise KeyFormatError("Key is not using P-256 curve")
        
        return public_key
        
    except Exception as e:
        raise KeyFormatError(f"Invalid ECDSA public key: {e}")


def verify_signed_prekey_signature(
    identity_key_b64: str,
    signed_prekey_b64: str, 
    signature_b64: str
) -> bool:
    """
    Verify signed pre-key signature using identity key.
    
    SECURITY CRITICAL: This verifies the signature over the raw SPKI bytes
    of the signed pre-key using the user's identity public key.
    
    Args:
        identity_key_b64: Identity public key (ECDSA P-256, SPKI, base64)
        signed_prekey_b64: Signed pre-key public key (ECDH P-256, SPKI, base64)
        signature_b64: ECDSA signature over signed pre-key SPKI bytes (base64)
        
    Returns:
        True if signature is valid, False otherwise
        
    Raises:
        SignatureVerificationError: If verification fails due to format errors
    """
    try:
        # Decode all components
        identity_key_bytes = decode_base64_key(identity_key_b64)
        signed_prekey_bytes = decode_base64_key(signed_prekey_b64)
        signature_bytes = base64.b64decode(signature_b64)
        
        # Load identity public key
        identity_public_key = load_ecdsa_public_key(identity_key_bytes)
        
        # Verify signature over the raw SPKI bytes of the signed pre-key
        identity_public_key.verify(
            signature_bytes,
            signed_prekey_bytes,  # Sign over raw SPKI bytes
            ec.ECDSA(hashes.SHA256())
        )
        
        logger.info("Signed pre-key signature verification successful")
        return True
        
    except InvalidSignature:
        logger.warning("Signed pre-key signature verification failed: Invalid signature")
        return False
    except Exception as e:
        logger.error(f"Signed pre-key signature verification error: {e}")
        raise SignatureVerificationError(f"Signature verification failed: {e}")


def validate_ecdh_public_key(key_b64: str) -> bool:
    """
    Validate ECDH P-256 public key format.
    
    Args:
        key_b64: ECDH public key (SPKI format, base64)
        
    Returns:
        True if key is valid ECDH P-256 key
    """
    try:
        key_bytes = decode_base64_key(key_b64)
        public_key = load_der_public_key(key_bytes)
        
        # Verify it's an ECDH-capable key with P-256 curve
        if not isinstance(public_key, ec.EllipticCurvePublicKey):
            return False
        
        if not isinstance(public_key.curve, ec.SECP256R1):
            return False
        
        return True
        
    except Exception as e:
        logger.debug(f"ECDH key validation failed: {e}")
        return False


def hash_associated_data(
    conversation_id: str,
    sender_id: str, 
    message_number: int,
    previous_chain_length: int
) -> str:
    """
    Create hash of associated data for AES-GCM.
    
    This creates a deterministic hash of the message context
    that can be used for additional verification.
    
    Args:
        conversation_id: Conversation UUID
        sender_id: Sender UUID
        message_number: Message counter
        previous_chain_length: Previous chain length
        
    Returns:
        SHA-256 hash of associated data (base64 encoded)
    """
    ad_string = f"{conversation_id}:{sender_id}:{message_number}:{previous_chain_length}"
    ad_hash = hashlib.sha256(ad_string.encode('utf-8')).digest()
    return base64.b64encode(ad_hash).decode('ascii')


def generate_key_id(public_key_b64: str) -> str:
    """
    Generate a deterministic key ID from public key.
    
    Args:
        public_key_b64: Public key (base64 encoded)
        
    Returns:
        Key ID (first 16 chars of SHA-256 hash)
    """
    key_hash = hashlib.sha256(public_key_b64.encode('utf-8')).hexdigest()
    return key_hash[:16]


def constant_time_compare(a: str, b: str) -> bool:
    """
    Constant-time string comparison to prevent timing attacks.
    
    Args:
        a: First string
        b: Second string
        
    Returns:
        True if strings are equal
    """
    if len(a) != len(b):
        return False
    
    result = 0
    for x, y in zip(a, b):
        result |= ord(x) ^ ord(y)
    
    return result == 0


# Rate limiting helpers
def get_client_ip(request) -> str:
    """
    Get client IP address from request, handling proxies.
    
    Args:
        request: Django request object
        
    Returns:
        Client IP address
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', '0.0.0.0')
    return ip


def is_rate_limited(user, ip_address: str, window_minutes: int = 60, max_attempts: int = 10) -> bool:
    """
    Check if user/IP is rate limited for key bundle operations.
    
    Args:
        user: Django user object
        ip_address: Client IP address
        window_minutes: Time window in minutes
        max_attempts: Maximum attempts in window
        
    Returns:
        True if rate limited
    """
    from django.utils import timezone
    from datetime import timedelta
    from .models import KeyBundleUploadLog
    
    cutoff_time = timezone.now() - timedelta(minutes=window_minutes)
    
    # Check user-based rate limiting
    user_attempts = KeyBundleUploadLog.objects.filter(
        user=user,
        created_at__gte=cutoff_time
    ).count()
    
    if user_attempts >= max_attempts:
        return True
    
    # Check IP-based rate limiting (more permissive)
    ip_attempts = KeyBundleUploadLog.objects.filter(
        ip_address=ip_address,
        created_at__gte=cutoff_time
    ).count()
    
    if ip_attempts >= max_attempts * 2:  # Allow more attempts per IP
        return True
    
    return False
