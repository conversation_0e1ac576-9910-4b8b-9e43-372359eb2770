# ChatApp Socket Server - Postman Testing Guide

This guide provides comprehensive instructions for testing the ChatApp Socket Server API using Postman's WebSocket capabilities. The API is documented using AsyncAPI 3.0.0 specification and supports real-time messaging with end-to-end encryption.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Environment Setup](#environment-setup)
4. [Authentication](#authentication)
5. [WebSocket Connection](#websocket-connection)
6. [API Endpoints](#api-endpoints)
7. [Testing Scenarios](#testing-scenarios)
8. [Troubleshooting](#troubleshooting)

## Overview

The ChatApp Socket Server provides a real-time messaging API with the following capabilities:

- **Real-time messaging** with WebSocket protocol
- **End-to-end encryption** support (Phase 3) with backward compatibility for plaintext (Phase 2)
- **JWT authentication** for secure connections
- **Conversation management** with join/leave functionality
- **Typing indicators** for enhanced user experience
- **Key exchange coordination** for encryption setup

### Server Information

- **Development Server**: `ws://localhost:3001`
- **Production Server**: `wss://api.chatapp.com`
- **Protocol**: WebSocket (ws/wss)
- **API Version**: 3.0.0

## Prerequisites

Before testing the API, ensure you have:

1. **Postman Desktop App** (WebSocket support requires desktop version)
2. **Valid JWT Token** for authentication
3. **Running Socket Server** on the target environment
4. **Test User Accounts** for conversation testing

## Environment Setup

### Postman Environment Variables

Create a new environment in Postman with the following variables:

```json
{
  "baseUrl": "ws://localhost:3001",
  "authToken": "your-jwt-token-here",
  "userId": "your-user-id",
  "conversationId": "test-conversation-id",
  "targetUserId": "target-user-id-for-testing"
}
```

### Server Environments

#### Development Environment
- **URL**: `ws://localhost:3001`
- **Protocol**: WebSocket (ws)
- **Description**: Local development server

#### Production Environment
- **URL**: `wss://api.chatapp.com`
- **Protocol**: WebSocket Secure (wss)
- **Description**: Production server with SSL

## Authentication

All WebSocket connections require JWT authentication. The token must be provided during the initial connection.

### Authentication Flow

1. **Obtain JWT Token**: Get a valid JWT token from the authentication API
2. **Connect with Token**: Include the token in the connection payload
3. **Maintain Connection**: Keep the connection alive for real-time communication

### Connection Payload

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## WebSocket Connection

### Creating a WebSocket Request in Postman

1. **New Request**: Click "New" → "WebSocket Request"
2. **Enter URL**: Use `{{baseUrl}}` from your environment
3. **Connect**: Click "Connect" to establish the connection
4. **Send Auth**: Send the authentication payload immediately after connection

### Connection Events

#### Connect Event
```json
{
  "event": "connect",
  "token": "{{authToken}}"
}
```

#### Error Handling
The server may respond with error messages:
```json
{
  "error": "Invalid message data",
  "code": "VALIDATION_ERROR",
  "details": {
    "field": "encryptedContent"
  }
}
```

## API Endpoints

### 1. Messaging Operations

#### Send Message (Plaintext)
```json
{
  "event": "send_message",
  "conversationId": "{{conversationId}}",
  "tempId": "temp-123",
  "messageType": "TEXT",
  "content": "Hello, this is a test message!"
}
```

#### Send Message (Encrypted)
```json
{
  "event": "send_message",
  "conversationId": "{{conversationId}}",
  "tempId": "temp-456",
  "messageType": "TEXT",
  "encryptedContent": "dGhpcyBpcyBmYWtlIGVuY3J5cHRlZCBjb250ZW50",
  "iv": "cmFuZG9tSVY5NmJpdA==",
  "senderRatchetKey": "ZmFrZVJhdGNoZXRLZXlTUEtJRm9ybWF0",
  "messageNumber": 1,
  "previousChainLength": 0
}
```

#### Expected Responses

**Message Sent Confirmation:**
```json
{
  "event": "message_sent",
  "tempId": "temp-123",
  "messageId": "550e8400-e29b-41d4-a716-446655440000",
  "status": "DELIVERED",
  "isEncrypted": false
}
```

**New Message Broadcast:**
```json
{
  "event": "new_message",
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "conversationId": "{{conversationId}}",
  "sender": {
    "id": "{{userId}}",
    "username": "testuser",
    "firstName": "Test",
    "lastName": "User"
  },
  "messageType": "TEXT",
  "content": "Hello, this is a test message!",
  "createdAt": "2024-01-15T10:30:00Z",
  "status": "SENT",
  "isEncrypted": false
}
```

### 2. Conversation Management

#### Join Conversation
```json
{
  "event": "join_conversation",
  "conversationId": "{{conversationId}}"
}
```

#### Leave Conversation
```json
{
  "event": "leave_conversation",
  "conversationId": "{{conversationId}}"
}
```

### 3. Typing Indicators

#### Start Typing
```json
{
  "event": "typing_start",
  "conversationId": "{{conversationId}}"
}
```

#### Stop Typing
```json
{
  "event": "typing_stop",
  "conversationId": "{{conversationId}}"
}
```

#### Typing Indicator Broadcast
```json
{
  "event": "typing_indicator",
  "conversationId": "{{conversationId}}",
  "userId": "{{targetUserId}}",
  "username": "targetuser",
  "isTyping": true
}
```

### 4. Encryption Operations

#### Key Exchange Request
```json
{
  "event": "key_exchange_request",
  "targetUserId": "{{targetUserId}}",
  "conversationId": "{{conversationId}}",
  "ephemeralPublicKey": "ZXBoZW1lcmFsS2V5RXhhbXBsZQ=="
}
```

#### Key Exchange Response
```json
{
  "event": "key_exchange_response",
  "success": true,
  "keyBundle": {
    "identityPublicKey": "aWRlbnRpdHlLZXlFeGFtcGxl",
    "signedPrekey": {
      "id": 1,
      "publicKey": "c2lnbmVkUHJla2V5RXhhbXBsZQ==",
      "signature": "c2lnbmF0dXJlRXhhbXBsZQ=="
    },
    "oneTimePrekey": {
      "id": 1,
      "publicKey": "b25lVGltZVByZWtleUV4YW1wbGU="
    }
  }
}
```

#### Encryption Status Check
```json
{
  "event": "encryption_status_check",
  "conversationId": "{{conversationId}}"
}
```

#### Encryption Status Response
```json
{
  "event": "encryption_status_response",
  "conversationId": "{{conversationId}}",
  "isEncrypted": true,
  "participants": [
    {
      "id": "{{userId}}",
      "username": "testuser",
      "hasEncryption": true
    },
    {
      "id": "{{targetUserId}}",
      "username": "targetuser",
      "hasEncryption": true
    }
  ]
}
```

## Testing Scenarios

### Scenario 1: Basic Message Flow

1. **Connect** to WebSocket server
2. **Authenticate** with JWT token
3. **Join** a conversation
4. **Send** a plaintext message
5. **Verify** message sent confirmation
6. **Receive** new message broadcast

### Scenario 2: Encrypted Message Flow

1. **Connect** and authenticate
2. **Request** key exchange with target user
3. **Receive** key bundle response
4. **Send** encrypted message
5. **Verify** encrypted message delivery

### Scenario 3: Typing Indicators

1. **Connect** and join conversation
2. **Send** typing start event
3. **Verify** typing indicator broadcast
4. **Send** typing stop event
5. **Verify** typing indicator stops

### Scenario 4: Error Handling

1. **Send** invalid message format
2. **Verify** error response
3. **Send** message to non-existent conversation
4. **Verify** appropriate error handling

## Troubleshooting

### Common Issues

#### Connection Refused
- **Cause**: Server not running or incorrect URL
- **Solution**: Verify server status and URL format

#### Authentication Failed
- **Cause**: Invalid or expired JWT token
- **Solution**: Obtain fresh token from auth API

#### Message Not Delivered
- **Cause**: Invalid conversation ID or user not in conversation
- **Solution**: Verify conversation exists and user has access

#### WebSocket Not Supported
- **Cause**: Using Postman web version
- **Solution**: Use Postman desktop application

### Debug Tips

1. **Enable Console Logging**: Check Postman console for detailed logs
2. **Verify Environment Variables**: Ensure all variables are set correctly
3. **Test Connection First**: Always test basic connection before complex operations
4. **Monitor Server Logs**: Check server-side logs for additional context

### Support

For additional support:
- **Documentation**: Refer to AsyncAPI specification
- **Issues**: Report bugs in the project repository
- **Contact**: <EMAIL>

---

*This guide is based on AsyncAPI 3.0.0 specification. For the latest updates, refer to the official documentation.*