import React, { useState } from 'react';
import { X, Search, UserPlus } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
}

interface Group {
  id: string;
  name: string;
  participants: Array<{
    user: User;
    role: string;
  }>;
  maxParticipants: number;
}

interface AddMemberDialogProps {
  open: boolean;
  onClose: () => void;
  group: Group;
  onAddMember: (userId: string, role?: string) => Promise<void>;
  onSearchUsers: (query: string) => Promise<User[]>;
}

const AddMemberDialog: React.FC<AddMemberDialogProps> = ({
  open,
  onClose,
  group,
  onAddMember,
  onSearchUsers,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [selectedRole, setSelectedRole] = useState<'member' | 'moderator'>('member');
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);

  const handleSearchUsers = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setSearchLoading(true);
    try {
      const results = await onSearchUsers(query);
      // Filter out users who are already members
      const existingMemberIds = group.participants.map(p => p.user.id);
      const filteredResults = results.filter(
        (user) => !existingMemberIds.includes(user.id)
      );
      setSearchResults(filteredResults);
    } catch (error) {
      console.error('Failed to search users:', error);
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleAddMember = async (userId: string) => {
    // Check if group is at capacity
    if (group.participants.length >= group.maxParticipants) {
      alert('Group is at maximum capacity');
      return;
    }

    setLoading(true);
    try {
      await onAddMember(userId, selectedRole);
      
      // Remove the added user from search results
      setSearchResults(searchResults.filter(user => user.id !== userId));
      setSearchQuery('');
    } catch (error) {
      console.error('Failed to add member:', error);
      alert('Failed to add member. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setSearchQuery('');
      setSearchResults([]);
      setSelectedRole('member');
      onClose();
    }
  };

  if (!open) return null;

  const remainingSlots = group.maxParticipants - group.participants.length;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <UserPlus className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Add Member</h2>
          </div>
          <button
            onClick={handleClose}
            disabled={loading}
            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Group Info */}
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">
              Adding to: <span className="font-medium text-gray-900">{group.name}</span>
            </p>
            <p className="text-sm text-gray-500">
              {group.participants.length} / {group.maxParticipants} members
              {remainingSlots > 0 && (
                <span className="text-green-600"> ({remainingSlots} slots available)</span>
              )}
            </p>
          </div>

          {remainingSlots === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">Group is at maximum capacity</p>
            </div>
          ) : (
            <>
              {/* Role Selection */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Member Role
                </label>
                <select
                  value={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value as 'member' | 'moderator')}
                  disabled={loading}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
                >
                  <option value="member">Member</option>
                  <option value="moderator">Moderator</option>
                </select>
              </div>

              {/* User Search */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Search Users
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      handleSearchUsers(e.target.value);
                    }}
                    placeholder="Search by name or username"
                    className="pl-10 w-full"
                    disabled={loading}
                  />
                </div>
              </div>

              {/* Search Results */}
              <div className="max-h-64 overflow-y-auto">
                {searchLoading && (
                  <div className="text-center py-4 text-gray-500 text-sm">
                    Searching...
                  </div>
                )}

                {!searchLoading && searchQuery && searchResults.length === 0 && (
                  <div className="text-center py-4 text-gray-500 text-sm">
                    No users found
                  </div>
                )}

                {searchResults.length > 0 && (
                  <div className="space-y-2">
                    {searchResults.map((user) => (
                      <div
                        key={user.id}
                        className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                            {user.profilePicture ? (
                              <img
                                src={user.profilePicture}
                                alt={user.firstName}
                                className="w-10 h-10 rounded-full object-cover"
                              />
                            ) : (
                              <span className="text-gray-600 font-medium">
                                {user.firstName[0]}
                              </span>
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">
                              {user.firstName} {user.lastName}
                            </p>
                            <p className="text-sm text-gray-500">@{user.username}</p>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          onClick={() => handleAddMember(user.id)}
                          disabled={loading}
                          loading={loading}
                        >
                          Add
                        </Button>
                      </div>
                    ))}
                  </div>
                )}

                {!searchQuery && (
                  <div className="text-center py-8 text-gray-500 text-sm">
                    Start typing to search for users
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Close
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AddMemberDialog;
