// frontend/src/services/mediaApi.ts
import axios from 'axios';

// Create axios instance for media uploads
const mediaHttpClient = axios.create({
  baseURL: '/api', // Use relative URL to avoid CORS and unsafe port issues
  timeout: 300000, // 5 minutes for large file uploads
});

// Add auth interceptor
mediaHttpClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export interface MediaFile {
  id: string;
  message: string;
  uploader: {
    id: string;
    username: string;
    first_name: string;
    last_name: string;
  };
  original_filename: string;
  file_type: 'image' | 'document' | 'audio' | 'video' | 'archive' | 'other';
  mime_type: string;
  file_size: number;
  wrapped_file_key: string;
  file_nonce: string;
  thumbnail_nonce?: string;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  virus_scan_status: string;
  created_at: string;
  updated_at: string;
  file_extension: string;
  is_image: boolean;
  is_video: boolean;
  has_thumbnail: boolean;
}

export interface MediaUploadResponse {
  message_id: string;
  media_id: string;
  media_file: {
    id: string;
    original_filename: string;
    file_type: string;
    file_size: number;
    processing_status: string;
    virus_scan_status: string;
    wrapped_file_key: string;
    file_nonce: string;
  };
  message: {
    id: string;
    message_type: string;
    content: string;
    created_at: string;
  };
}

export interface MediaDownloadResponse {
  download_url: string;
  filename: string;
  file_size: number;
  expires_at: string;
  wrapped_file_key: string;
  file_nonce: string;
}

export interface ThumbnailResponse {
  encrypted_thumbnail: string;
  thumbnail_nonce: string;
  wrapped_file_key: string;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export class MediaApiService {
  /**
   * Upload a small file (< 10MB) using simple upload
   */
  async uploadSimple(
    conversationId: string,
    file: File,
    wrappedFileKey: string,
    fileNonce: string,
    fileHash: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<MediaUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('conversation_id', conversationId);
    formData.append('wrapped_file_key', wrappedFileKey);
    formData.append('file_nonce', fileNonce);
    formData.append('file_hash', fileHash);

    // Add original file metadata for backend processing
    const originalFileName = file.name.replace('.enc', '');
    formData.append('original_filename', originalFileName);
    formData.append('file_size', file.size.toString());
    formData.append('mime_type', this.getMimeTypeFromFileName(originalFileName));

    const response = await mediaHttpClient.post('/media/upload/simple/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent: any) => {
        if (onProgress && progressEvent.total) {
          const progress: UploadProgress = {
            loaded: progressEvent.loaded,
            total: progressEvent.total,
            percentage: Math.round((progressEvent.loaded * 100) / progressEvent.total)
          };
          onProgress(progress);
        }
      }
    });

    return response.data;
  }

  /**
   * Upload a large file using chunked upload
   */
  async uploadChunked(
    conversationId: string,
    file: File,
    wrappedFileKey: string,
    fileNonce: string,
    fileHash: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<MediaUploadResponse> {
    const chunkSize = 1024 * 1024; // 1MB chunks
    const totalChunks = Math.ceil(file.size / chunkSize);
    const uploadSession = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    let uploadedBytes = 0;

    for (let chunkNumber = 0; chunkNumber < totalChunks; chunkNumber++) {
      const start = chunkNumber * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);

      // Calculate chunk hash
      const chunkArrayBuffer = await chunk.arrayBuffer();
      const chunkHashBuffer = await crypto.subtle.digest('SHA-256', chunkArrayBuffer);
      const chunkHash = Array.from(new Uint8Array(chunkHashBuffer))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');

      const formData = new FormData();
      formData.append('chunk', chunk);
      formData.append('conversation_id', conversationId);
      formData.append('upload_session', uploadSession);
      formData.append('chunk_number', chunkNumber.toString());
      formData.append('total_chunks', totalChunks.toString());
      formData.append('chunk_hash', chunkHash);

      // Include metadata on first chunk
      if (chunkNumber === 0) {
        const originalFileName = file.name.replace('.enc', '');
        formData.append('original_filename', originalFileName);
        formData.append('file_size', file.size.toString());
        formData.append('mime_type', this.getMimeTypeFromFileName(originalFileName));
        formData.append('wrapped_file_key', wrappedFileKey);
        formData.append('file_nonce', fileNonce);
        formData.append('file_hash', fileHash);
      }

      const response = await mediaHttpClient.post('/media/upload/chunked/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });

      uploadedBytes += chunk.size;

      if (onProgress) {
        const progress: UploadProgress = {
          loaded: uploadedBytes,
          total: file.size,
          percentage: Math.round((uploadedBytes * 100) / file.size)
        };
        onProgress(progress);
      }

      // If this is the last chunk and upload is complete
      if (response.data.status === 'completed' || response.data.id) {
        return response.data;
      }
    }

    throw new Error('Upload failed: No completion response received');
  }

  /**
   * Get download token for a media file
   */
  async getDownloadToken(mediaId: string): Promise<MediaDownloadResponse> {
    const response = await mediaHttpClient.get(`/media/download/${mediaId}/`);
    return response.data;
  }

  /**
   * Download a media file using the download token
   */
  async downloadFile(downloadToken: string): Promise<Blob> {
    const response = await mediaHttpClient.get(`/media/download/${downloadToken}/`, {
      responseType: 'blob'
    });
    return response.data;
  }

  /**
   * Get encrypted thumbnail for a media file
   */
  async getThumbnail(mediaId: string): Promise<ThumbnailResponse> {
    const response = await mediaHttpClient.get(`/media/thumbnail/${mediaId}/`);
    return response.data;
  }

  /**
   * Determine if file should use chunked upload
   */
  shouldUseChunkedUpload(file: File): boolean {
    return file.size > 10 * 1024 * 1024; // 10MB threshold
  }

  /**
   * Get MIME type from filename extension
   */
  getMimeTypeFromFileName(filename: string): string {
    const extension = filename.toLowerCase().split('.').pop();

    const mimeTypes: Record<string, string> = {
      // Images
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'svg': 'image/svg+xml',

      // Videos
      'mp4': 'video/mp4',
      'webm': 'video/webm',
      'mov': 'video/quicktime',
      'avi': 'video/x-msvideo',

      // Audio
      'mp3': 'audio/mpeg',
      'wav': 'audio/wav',
      'ogg': 'audio/ogg',

      // Documents
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'txt': 'text/plain',
      'rtf': 'application/rtf',

      // Archives
      'zip': 'application/zip',
      'rar': 'application/x-rar-compressed',
      '7z': 'application/x-7z-compressed',
    };

    return mimeTypes[extension || ''] || 'application/octet-stream';
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): { valid: boolean; error?: string } {
    // Size limits by type
    const sizeLimits = {
      'image': 10 * 1024 * 1024,    // 10MB
      'document': 25 * 1024 * 1024, // 25MB
      'audio': 25 * 1024 * 1024,    // 25MB
      'video': 100 * 1024 * 1024,   // 100MB
      'archive': 50 * 1024 * 1024,  // 50MB
    };

    // Determine file type
    const fileType = this.getFileType(file.type);
    const maxSize = sizeLimits[fileType as keyof typeof sizeLimits] || sizeLimits.document;

    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File too large. Maximum size for ${fileType} files is ${this.formatFileSize(maxSize)}`
      };
    }

    // Check dangerous extensions
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.msi'];
    const extension = file.name.toLowerCase().split('.').pop();
    if (extension && dangerousExtensions.includes(`.${extension}`)) {
      return {
        valid: false,
        error: 'File type not allowed for security reasons'
      };
    }

    return { valid: true };
  }

  /**
   * Get file type from MIME type
   */
  getFileType(mimeType: string): string {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.startsWith('text/') || 
        ['application/pdf', 'application/msword', 
         'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(mimeType)) {
      return 'document';
    }
    if (['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'].includes(mimeType)) {
      return 'archive';
    }
    return 'other';
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export const mediaApi = new MediaApiService();
