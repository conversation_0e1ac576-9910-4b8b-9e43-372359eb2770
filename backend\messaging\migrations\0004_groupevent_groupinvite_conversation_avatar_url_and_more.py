# Generated by Django 5.2.4 on 2025-08-30 08:33

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('messaging', '0003_message_encrypted_content_message_iv_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='GroupEvent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('event_type', models.CharField(choices=[('member_added', 'Member Added'), ('member_removed', 'Member Removed'), ('member_left', 'Member Left'), ('role_changed', 'Role Changed'), ('group_info_updated', 'Group Info Updated'), ('message_pinned', 'Message Pinned'), ('message_unpinned', 'Message Unpinned')], max_length=20)),
                ('event_data', models.J<PERSON><PERSON>ield(default=dict, help_text='Additional event details')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'group_events',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GroupInvite',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('invited_email', models.EmailField(blank=True, help_text='For non-users', max_length=254, null=True)),
                ('invite_code', models.CharField(max_length=50, unique=True)),
                ('expires_at', models.DateTimeField()),
                ('is_used', models.BooleanField(default=False)),
                ('used_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'group_invites',
            },
        ),
        migrations.AddField(
            model_name='conversation',
            name='avatar_url',
            field=models.URLField(blank=True, help_text='Group avatar URL', null=True),
        ),
        migrations.AddField(
            model_name='conversation',
            name='created_by',
            field=models.ForeignKey(blank=True, help_text='User who created this conversation', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_conversations', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='conversation',
            name='description',
            field=models.TextField(blank=True, help_text='Group description'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='group_settings',
            field=models.JSONField(default=dict, help_text='Group preferences and settings'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='invite_link',
            field=models.CharField(blank=True, help_text='Unique invite link for the group', max_length=100, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='conversation',
            name='is_public',
            field=models.BooleanField(default=False, help_text='Whether this is a public group'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='max_participants',
            field=models.IntegerField(default=50, help_text='Maximum number of participants'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='pinned_message',
            field=models.ForeignKey(blank=True, help_text='Pinned message in the group', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='pinned_in_conversations', to='messaging.message'),
        ),
        migrations.AddField(
            model_name='conversationparticipant',
            name='can_add_members',
            field=models.BooleanField(default=False, help_text='Can add new members to the group'),
        ),
        migrations.AddField(
            model_name='conversationparticipant',
            name='can_delete_messages',
            field=models.BooleanField(default=False, help_text='Can delete messages from others'),
        ),
        migrations.AddField(
            model_name='conversationparticipant',
            name='can_edit_group_info',
            field=models.BooleanField(default=False, help_text='Can edit group name, description, etc.'),
        ),
        migrations.AddField(
            model_name='conversationparticipant',
            name='can_pin_messages',
            field=models.BooleanField(default=False, help_text='Can pin/unpin messages'),
        ),
        migrations.AddField(
            model_name='conversationparticipant',
            name='can_remove_members',
            field=models.BooleanField(default=False, help_text='Can remove members from the group'),
        ),
        migrations.AddField(
            model_name='conversationparticipant',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Whether the participant is active in the conversation'),
        ),
        migrations.AddField(
            model_name='conversationparticipant',
            name='mention_notifications_only',
            field=models.BooleanField(default=False, help_text='Only notify when mentioned'),
        ),
        migrations.AddField(
            model_name='conversationparticipant',
            name='notifications_enabled',
            field=models.BooleanField(default=True, help_text='Receive notifications for this group'),
        ),
        migrations.AlterField(
            model_name='conversationparticipant',
            name='role',
            field=models.CharField(choices=[('admin', 'Admin'), ('moderator', 'Moderator'), ('member', 'Member'), ('restricted', 'Restricted')], default='member', max_length=15),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['type', 'created_at'], name='conversatio_type_4693ab_idx'),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['created_by', 'created_at'], name='conversatio_created_2bfc89_idx'),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['is_public'], name='conversatio_is_publ_ddc6d2_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationparticipant',
            index=models.Index(fields=['conversation', 'is_active'], name='conversatio_convers_b28779_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationparticipant',
            index=models.Index(fields=['user', 'is_active'], name='conversatio_user_id_ae9f1a_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationparticipant',
            index=models.Index(fields=['role'], name='conversatio_role_7e11b3_idx'),
        ),
        migrations.AddField(
            model_name='groupevent',
            name='actor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='group_actions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='groupevent',
            name='conversation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='events', to='messaging.conversation'),
        ),
        migrations.AddField(
            model_name='groupevent',
            name='target_user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='group_events_received', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='groupinvite',
            name='conversation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invites', to='messaging.conversation'),
        ),
        migrations.AddField(
            model_name='groupinvite',
            name='invited_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_invites', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='groupinvite',
            name='invited_user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='received_invites', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='groupevent',
            index=models.Index(fields=['conversation', 'created_at'], name='group_event_convers_6e968a_idx'),
        ),
        migrations.AddIndex(
            model_name='groupevent',
            index=models.Index(fields=['event_type', 'created_at'], name='group_event_event_t_520b89_idx'),
        ),
        migrations.AddIndex(
            model_name='groupevent',
            index=models.Index(fields=['actor', 'created_at'], name='group_event_actor_i_04ae36_idx'),
        ),
        migrations.AddIndex(
            model_name='groupinvite',
            index=models.Index(fields=['invite_code'], name='group_invit_invite__b0e1c8_idx'),
        ),
        migrations.AddIndex(
            model_name='groupinvite',
            index=models.Index(fields=['expires_at', 'is_used'], name='group_invit_expires_c92527_idx'),
        ),
        migrations.AddIndex(
            model_name='groupinvite',
            index=models.Index(fields=['conversation', 'is_used'], name='group_invit_convers_690689_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='groupinvite',
            unique_together={('conversation', 'invited_user')},
        ),
    ]
