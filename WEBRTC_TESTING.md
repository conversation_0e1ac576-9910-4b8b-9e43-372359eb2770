# WebRTC Testing Guide

This guide explains how to test WebRTC calling functionality without requiring camera or microphone devices.

## Mock Mode Testing

### Enabling Mock Mode

There are three ways to enable mock mode for testing:

1. **URL Parameter** (temporary): Add `?mock=true` to the URL
   ```
   http://localhost:5002/?mock=true
   ```

2. **Browser localStorage** (persistent): Open browser console and run:
   ```javascript
   localStorage.setItem('webrtc_mock_mode', 'true');
   // Then reload the page
   ```

3. **Environment Variable** (for development): Add to `.env` file:
   ```
   VITE_WEBRTC_MOCK_MODE=true
   ```

### Using the Testing Panel

1. **Open the app** in your browser
2. **Look for the settings button** (⚙️) in the bottom-right corner
3. **Click to open** the WebRTC Testing Panel
4. **Toggle Mock Mode** on/off as needed
5. **Monitor socket events** in real-time

### What Mock Mode Does

When mock mode is enabled:

✅ **No Real Devices Required**: Creates synthetic audio/video streams  
✅ **Socket Events Still Work**: All WebRTC signaling events are tested  
✅ **Visual Feedback**: Shows mock video stream with "Testing Mode" overlay  
✅ **Audio Simulation**: Creates silent audio tracks for testing  
✅ **Full Call Flow**: Tests complete call initiation, connection, and termination  

### Testing Scenarios

#### 1. **Basic Call Initiation**
- Enable mock mode
- Try to start an audio call
- Check console for socket events:
  - `Socket event emitted: webrtc_offer`
  - `Socket event received: webrtc_answer` (if other user accepts)
  - `Socket event emitted: webrtc_ice_candidate`

#### 2. **Socket Event Monitoring**
The testing panel captures and displays:
- **Outgoing Events** (blue): Events sent to server
- **Incoming Events** (green): Events received from server
- **Event Data**: JSON payload for debugging

#### 3. **Error Handling**
Test how the app handles:
- Missing media devices
- Permission denied scenarios  
- Connection failures
- Socket disconnections

### Console Logging

Mock mode enables extensive logging:

```javascript
🎭 WebRTC Manager running in MOCK MODE for testing
🎭 Mock mode: Media availability check - returning available
🎭 Mock mode: Creating mock media streams
🔌 Socket event emitted: webrtc_offer
📞 Socket event received: incoming_call
```

### Testing Without Mock Mode

To test real device detection:
1. Disable mock mode
2. The app will attempt to detect actual devices
3. Check call button states:
   - **Disabled**: No devices available
   - **Loading**: Checking device availability
   - **Enabled**: Devices ready

### Debugging Tips

#### Check Socket Connection
```javascript
// In browser console
window.socket?.connected  // Should be true
```

#### View Socket Events
```javascript
// Enable more detailed socket logging
localStorage.setItem('debug', 'socket.io-client:*');
```

#### Monitor WebRTC State
```javascript
// Check if WebRTC is supported
console.log('WebRTC supported:', !!window.RTCPeerConnection);

// Check media devices
navigator.mediaDevices.enumerateDevices().then(devices => {
  console.log('Available devices:', devices);
});
```

### Common Issues & Solutions

#### Issue: "Mock mode not working"
**Solution**: Ensure page is reloaded after enabling mock mode

#### Issue: "No socket events appearing"  
**Solution**: Check that socket connection is established and backend is running

#### Issue: "Call buttons still disabled"
**Solution**: Mock mode may not be properly enabled. Check console for mock mode messages

#### Issue: "Testing panel not visible"
**Solution**: Panel only shows in development. Check `import.meta.env.DEV` or use localhost

### Backend Socket Server Testing

To test the complete flow, ensure the socket server is running:

```bash
cd socket-server
npm run dev
```

Monitor server logs for incoming WebRTC events:
- Call initiation requests
- WebRTC signaling events
- Connection state changes

### Multi-User Testing

For testing real-time features:

1. **Open two browser windows**
2. **Enable mock mode in both**
3. **Login as different users**
4. **Initiate call from one window**
5. **Monitor socket events in both**

This allows testing of:
- Incoming call notifications
- Call acceptance/rejection
- WebRTC signaling between peers
- Connection state synchronization

### Production Considerations

**Important**: Mock mode is automatically disabled in production builds. It's only available during development for testing purposes.

The testing panel and mock mode features are designed to:
- Help develop and debug calling features
- Test socket communication without hardware
- Validate error handling scenarios
- Ensure proper state management

Remember to test with real devices before deploying to production!
