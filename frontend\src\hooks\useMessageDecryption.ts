// frontend/src/hooks/useMessageDecryption.ts
import { useState, useEffect } from 'react';
import { useEncryption } from '../contexts/EncryptionContext';
import type { Message as ApiMessage } from '../services/messageApi';
import type { Message as StoreMessage } from '../store/slices/messageSlice';

// Union type to handle both API and store message formats
type MessageType = ApiMessage | StoreMessage;

interface UseMessageDecryptionResult {
  decryptedContent: string;
  isDecrypting: boolean;
  decryptionError: string | null;
}

/**
 * Hook to handle message decryption for display purposes
 * Handles both WebSocket messages (already decrypted) and API messages (need decryption)
 */
export const useMessageDecryption = (message: MessageType): UseMessageDecryptionResult => {
  const [decryptedContent, setDecryptedContent] = useState<string>('');
  const [isDecrypting, setIsDecrypting] = useState<boolean>(false);
  const [decryptionError, setDecryptionError] = useState<string | null>(null);
  
  const { decryptReceivedMessage } = useEncryption();

  useEffect(() => {
    const decryptMessage = async () => {
      try {
        // Reset state
        setDecryptionError(null);
        
        // If message has content (WebSocket messages or plaintext), use it directly
        if (message.content) {
          console.log('🔓 [MESSAGE_DECRYPT] Using existing content (WebSocket or plaintext)');
          setDecryptedContent(message.content);
          return;
        }

        // If message is encrypted and has encrypted content (API messages), decrypt it
        if (message.isEncrypted && message.encryptedContent && message.iv && message.senderRatchetKey) {
          console.log('🔓 [MESSAGE_DECRYPT] Decrypting API message:', {
            messageId: message.id,
            hasEncryptedContent: !!message.encryptedContent,
            hasIv: !!message.iv,
            hasSenderRatchetKey: !!message.senderRatchetKey
          });
          
          setIsDecrypting(true);
          
          const decrypted = await decryptReceivedMessage(
            message.encryptedContent,
            message.iv,
            message.senderRatchetKey
          );
          
          console.log('🔓 [MESSAGE_DECRYPT] ✅ API message decrypted successfully');
          setDecryptedContent(decrypted);
        } else if (message.isEncrypted) {
          // Encrypted message but missing required fields
          console.warn('🔓 [MESSAGE_DECRYPT] ⚠️ Encrypted message missing required fields:', {
            messageId: message.id,
            hasEncryptedContent: !!message.encryptedContent,
            hasIv: !!message.iv,
            hasSenderRatchetKey: !!message.senderRatchetKey
          });
          setDecryptedContent('[Encrypted Message - Missing Data]');
        } else {
          // Not encrypted but no content - this shouldn't happen
          console.warn('🔓 [MESSAGE_DECRYPT] ⚠️ Message has no content and is not encrypted:', message.id);
          setDecryptedContent('[No Content]');
        }
      } catch (error) {
        console.error('🔓 [MESSAGE_DECRYPT] ❌ Failed to decrypt message:', error);
        setDecryptionError(error instanceof Error ? error.message : 'Decryption failed');
        setDecryptedContent('[Decryption Failed]');
      } finally {
        setIsDecrypting(false);
      }
    };

    decryptMessage();
  }, [message.id, message.content, message.encryptedContent, message.iv, message.senderRatchetKey, message.isEncrypted, decryptReceivedMessage]);

  return {
    decryptedContent,
    isDecrypting,
    decryptionError
  };
};
