# backend/tests/factories.py
import factory
import uuid
from django.contrib.auth import get_user_model
from messaging.models import Conversation, ConversationParticipant, Message
from faker import Faker

User = get_user_model()
fake = Faker()

class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User
        skip_postgeneration_save = True

    id = factory.LazyFunction(uuid.uuid4)
    email = factory.Sequence(lambda n: f"user{n}@example.com")
    username = factory.Sequence(lambda n: f"user{n}")
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    password = factory.PostGenerationMethodCall('set_password', 'testpass123')
    is_verified = True
    is_active = True

class ConversationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Conversation
    
    id = factory.LazyFunction(uuid.uuid4)
    type = 'DIRECT'
    name = None

class GroupConversationFactory(ConversationFactory):
    type = 'GROUP'
    name = factory.Faker('sentence', nb_words=3)

class ConversationParticipantFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ConversationParticipant
    
    id = factory.LazyFunction(uuid.uuid4)
    conversation = factory.SubFactory(ConversationFactory)
    user = factory.SubFactory(UserFactory)
    role = 'MEMBER'

class MessageFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Message
    
    id = factory.LazyFunction(uuid.uuid4)
    conversation = factory.SubFactory(ConversationFactory)
    sender = factory.SubFactory(UserFactory)
    content = factory.Faker('text', max_nb_chars=200)
    message_type = 'TEXT'
