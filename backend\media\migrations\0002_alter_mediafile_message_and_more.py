# Generated by Django 5.2.4 on 2025-09-01 05:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('media', '0001_initial'),
        ('messaging', '0005_message_has_media_message_media_count_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='mediafile',
            name='message',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='media_files', to='messaging.message'),
        ),
        migrations.AlterField(
            model_name='mediafile',
            name='processing_status',
            field=models.CharField(choices=[('temporary', 'Temporary'), ('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=15),
        ),
        migrations.AlterField(
            model_name='mediaprocessingjob',
            name='status',
            field=models.CharField(choices=[('temporary', 'Temporary'), ('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=15),
        ),
    ]
