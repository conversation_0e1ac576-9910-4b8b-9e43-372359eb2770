from django.contrib import admin
from .models import (
    Conversation,
    ConversationParticipant,
    Message,
    MessageStatus,
    GroupInvite,
    GroupEvent
)


@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    list_display = ['id', 'type', 'name', 'created_by', 'is_public', 'created_at', 'updated_at']
    list_filter = ['type', 'is_public', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at', 'invite_link']
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'type', 'name', 'description', 'created_by')
        }),
        ('Group Settings', {
            'fields': ('is_public', 'max_participants', 'invite_link', 'avatar_url', 'pinned_message', 'group_settings'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(ConversationParticipant)
class ConversationParticipantAdmin(admin.ModelAdmin):
    list_display = ['user', 'conversation', 'role', 'is_active', 'joined_at']
    list_filter = ['role', 'is_active', 'joined_at']
    search_fields = ['user__username', 'conversation__name']
    readonly_fields = ['id', 'joined_at']
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'conversation', 'user', 'role', 'is_active', 'joined_at')
        }),
        ('Permissions', {
            'fields': ('can_add_members', 'can_remove_members', 'can_edit_group_info', 'can_pin_messages', 'can_delete_messages'),
            'classes': ('collapse',)
        }),
        ('Notifications', {
            'fields': ('notifications_enabled', 'mention_notifications_only'),
            'classes': ('collapse',)
        })
    )


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ['id', 'sender', 'conversation', 'message_type', 'has_media', 'is_encrypted', 'created_at']
    list_filter = ['message_type', 'has_media', 'created_at']
    search_fields = ['sender__username', 'conversation__name', 'content']
    readonly_fields = ['id', 'created_at', 'updated_at', 'is_encrypted']
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'conversation', 'sender', 'message_type', 'created_at', 'updated_at')
        }),
        ('Content', {
            'fields': ('content', 'has_media', 'media_count')
        }),
        ('Encryption', {
            'fields': ('encrypted_content', 'iv', 'sender_ratchet_key', 'message_number', 'previous_chain_length', 'is_encrypted'),
            'classes': ('collapse',)
        })
    )

    def is_encrypted(self, obj):
        return obj.is_encrypted
    is_encrypted.boolean = True
    is_encrypted.short_description = 'Encrypted'


@admin.register(MessageStatus)
class MessageStatusAdmin(admin.ModelAdmin):
    list_display = ['message', 'user', 'status', 'created_at', 'updated_at']
    list_filter = ['status', 'created_at']
    search_fields = ['message__id', 'user__username']
    readonly_fields = ['id', 'created_at', 'updated_at']


@admin.register(GroupInvite)
class GroupInviteAdmin(admin.ModelAdmin):
    list_display = ['conversation', 'invited_by', 'invited_user', 'invited_email', 'is_used', 'expires_at', 'created_at']
    list_filter = ['is_used', 'expires_at', 'created_at']
    search_fields = ['conversation__name', 'invited_by__username', 'invited_user__username', 'invited_email', 'invite_code']
    readonly_fields = ['id', 'invite_code', 'used_at', 'created_at']
    fieldsets = (
        ('Invitation Details', {
            'fields': ('id', 'conversation', 'invited_by', 'invited_user', 'invited_email', 'invite_code')
        }),
        ('Status', {
            'fields': ('is_used', 'used_at', 'expires_at', 'created_at')
        })
    )


@admin.register(GroupEvent)
class GroupEventAdmin(admin.ModelAdmin):
    list_display = ['conversation', 'event_type', 'actor', 'target_user', 'created_at']
    list_filter = ['event_type', 'created_at']
    search_fields = ['conversation__name', 'actor__username', 'target_user__username']
    readonly_fields = ['id', 'created_at']
    fieldsets = (
        ('Event Information', {
            'fields': ('id', 'conversation', 'event_type', 'actor', 'target_user', 'created_at')
        }),
        ('Event Data', {
            'fields': ('event_data',),
            'classes': ('collapse',)
        })
    )
