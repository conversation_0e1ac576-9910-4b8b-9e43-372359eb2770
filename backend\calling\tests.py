# backend/calling/tests.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from messaging.models import Conversation, ConversationParticipant
from .models import Call, CallEvent, CallQualityMetric

User = get_user_model()


class CallModelTest(TestCase):
    def setUp(self):
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            username='user1',
            password='testpass123',
            first_name='User',
            last_name='One'
        )
        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            username='user2',
            password='testpass123',
            first_name='User',
            last_name='Two'
        )
        
        # Create conversation
        self.conversation = Conversation.objects.create(
            type='direct',
            created_by=self.user1
        )
        ConversationParticipant.objects.create(
            conversation=self.conversation,
            user=self.user1,
            role='admin'
        )
        ConversationParticipant.objects.create(
            conversation=self.conversation,
            user=self.user2,
            role='member'
        )

    def test_call_creation(self):
        call = Call.objects.create(
            conversation=self.conversation,
            caller=self.user1,
            callee=self.user2,
            call_type='audio',
            session_id='test_session_123'
        )
        
        self.assertEqual(call.caller, self.user1)
        self.assertEqual(call.callee, self.user2)
        self.assertEqual(call.call_type, 'audio')
        self.assertEqual(call.status, 'initiated')
        self.assertIsNotNone(call.initiated_at)

    def test_call_duration_calculation(self):
        from django.utils import timezone
        from datetime import timedelta
        
        call = Call.objects.create(
            conversation=self.conversation,
            caller=self.user1,
            callee=self.user2,
            call_type='video',
            session_id='test_session_456'
        )
        
        # Simulate answered and ended times
        call.answered_at = timezone.now()
        call.ended_at = call.answered_at + timedelta(minutes=5)
        call.save()
        
        self.assertIsNotNone(call.duration)
        self.assertEqual(call.duration.total_seconds(), 300)  # 5 minutes


class CallAPITest(APITestCase):
    def setUp(self):
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            username='caller',
            password='testpass123',
            first_name='Caller',
            last_name='User'
        )
        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            username='callee',
            password='testpass123',
            first_name='Callee',
            last_name='User'
        )
        
        # Create conversation
        self.conversation = Conversation.objects.create(
            type='direct',
            created_by=self.user1
        )
        ConversationParticipant.objects.create(
            conversation=self.conversation,
            user=self.user1,
            role='admin'
        )
        ConversationParticipant.objects.create(
            conversation=self.conversation,
            user=self.user2,
            role='member'
        )
        
        # Get JWT token for authentication
        refresh = RefreshToken.for_user(self.user1)
        self.access_token = str(refresh.access_token)

    def authenticate(self, user=None):
        if user is None:
            user = self.user1
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {str(refresh.access_token)}')

    def test_initiate_call(self):
        self.authenticate(self.user1)
        
        url = reverse('initiate_call')
        data = {
            'conversation_id': str(self.conversation.id),
            'call_type': 'audio'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Call.objects.count(), 1)
        
        call = Call.objects.first()
        self.assertEqual(call.caller, self.user1)
        self.assertEqual(call.callee, self.user2)
        self.assertEqual(call.call_type, 'audio')

    def test_answer_call(self):
        # Create a call first
        call = Call.objects.create(
            conversation=self.conversation,
            caller=self.user1,
            callee=self.user2,
            call_type='video',
            session_id='test_session_789',
            status='ringing'
        )
        
        self.authenticate(self.user2)  # Callee answers
        
        url = reverse('answer_call', kwargs={'call_id': call.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        call.refresh_from_db()
        self.assertEqual(call.status, 'answered')
        self.assertIsNotNone(call.answered_at)

    def test_decline_call(self):
        # Create a call first
        call = Call.objects.create(
            conversation=self.conversation,
            caller=self.user1,
            callee=self.user2,
            call_type='audio',
            session_id='test_session_decline',
            status='ringing'
        )
        
        self.authenticate(self.user2)  # Callee declines
        
        url = reverse('decline_call', kwargs={'call_id': call.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        call.refresh_from_db()
        self.assertEqual(call.status, 'declined')
        self.assertIsNotNone(call.ended_at)

    def test_end_call(self):
        # Create an active call
        call = Call.objects.create(
            conversation=self.conversation,
            caller=self.user1,
            callee=self.user2,
            call_type='video',
            session_id='test_session_end',
            status='active'
        )
        
        self.authenticate(self.user1)  # Caller ends call
        
        url = reverse('end_call', kwargs={'call_id': call.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        call.refresh_from_db()
        self.assertEqual(call.status, 'ended')
        self.assertIsNotNone(call.ended_at)

    def test_get_call_history(self):
        # Create some calls
        Call.objects.create(
            conversation=self.conversation,
            caller=self.user1,
            callee=self.user2,
            call_type='audio',
            session_id='test_session_1',
            status='ended'
        )
        Call.objects.create(
            conversation=self.conversation,
            caller=self.user2,
            callee=self.user1,
            call_type='video',
            session_id='test_session_2',
            status='ended'
        )
        
        self.authenticate(self.user1)
        
        url = reverse('get_call_history')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
