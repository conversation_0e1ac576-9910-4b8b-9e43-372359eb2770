const axios = require('axios');

async function createTestUsers() {
  const baseURL = 'http://localhost:6000/api';
  
  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'testpass123',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      username: 'charlie'
    }
  ];

  for (const user of testUsers) {
    try {
      console.log(`Creating user: ${user.email}`);
      
      const response = await axios.post(`${baseURL}/auth/register/`, {
        email: user.email,
        password: user.password,
        first_name: user.first_name,
        last_name: user.last_name,
        username: user.username
      });
      
      console.log(`✅ User ${user.email} created successfully`);
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log(`⚠️  User ${user.email} already exists`);
      } else {
        console.error(`❌ Failed to create user ${user.email}:`, error.message);
      }
    }
  }
}

createTestUsers().then(() => {
  console.log('Test user creation completed');
}).catch(error => {
  console.error('Error creating test users:', error);
});
