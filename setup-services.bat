@echo off
REM Comprehensive Service Setup Script for Chat Application (Windows Batch)
REM This script starts Django backend, React frontend, and Node.js socket server

setlocal enabledelayedexpansion

echo.
echo ========================================
echo   Chat Application Service Setup
echo ========================================
echo.

REM Check if required directories exist
if not exist "backend" (
    echo [ERROR] Backend directory not found!
    pause
    exit /b 1
)

if not exist "frontend" (
    echo [ERROR] Frontend directory not found!
    pause
    exit /b 1
)

if not exist "socket-server" (
    echo [ERROR] Socket-server directory not found!
    pause
    exit /b 1
)

REM Check if virtual environment exists for Django
if not exist "backend\venv\Scripts\activate.bat" (
    echo [ERROR] Django virtual environment not found at backend\venv\Scripts\activate.bat
    echo Please create a virtual environment first:
    echo   cd backend
    echo   python -m venv venv
    echo   venv\Scripts\activate
    echo   pip install -r requirements.txt
    pause
    exit /b 1
)

REM Check if pnpm is installed
pnpm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] pnpm is not installed or not in PATH
    echo Please install pnpm: npm install -g pnpm
    pause
    exit /b 1
)

REM Check if package.json exists in frontend and socket-server
if not exist "frontend\package.json" (
    echo [ERROR] package.json not found in frontend directory
    pause
    exit /b 1
)

if not exist "socket-server\package.json" (
    echo [ERROR] package.json not found in socket-server directory
    pause
    exit /b 1
)

echo [INFO] All prerequisites checked successfully!
echo.

REM Function to check if port is in use
REM Note: This is a simplified check for batch files
echo [INFO] Starting services...
echo.

REM Start Django Backend (Port 6000)
echo [STARTING] Django Backend on port 6000...
start "Django Backend" cmd /k "cd /d backend && venv\Scripts\activate && python manage.py runserver 6000"
if errorlevel 1 (
    echo [ERROR] Failed to start Django Backend
) else (
    echo [SUCCESS] Django Backend started
)

REM Wait a moment between starts
timeout /t 2 /nobreak >nul

REM Start React Frontend (Port 5000)
echo [STARTING] React Frontend on port 5000...
start "React Frontend" cmd /k "cd /d frontend && pnpm run dev"
if errorlevel 1 (
    echo [ERROR] Failed to start React Frontend
) else (
    echo [SUCCESS] React Frontend started
)

REM Wait a moment between starts
timeout /t 2 /nobreak >nul

REM Start Socket Server (Port 7000)
echo [STARTING] Socket Server on port 7000...
start "Socket Server" cmd /k "cd /d socket-server && pnpm run dev"
if errorlevel 1 (
    echo [ERROR] Failed to start Socket Server
) else (
    echo [SUCCESS] Socket Server started
)

echo.
echo ========================================
echo   All Services Started Successfully!
echo ========================================
echo.
echo Services running:
echo   - Django Backend:  http://localhost:6000
echo   - React Frontend:  http://localhost:5000
echo   - Socket Server:   http://localhost:7000
echo.
echo Each service is running in its own command window.
echo To stop services, close the respective command windows
echo or press Ctrl+C in each window.
echo.
echo Press any key to exit this setup script...
pause >nul