// frontend/src/components/Chat/__tests__/EnhancedMediaUpload.test.tsx
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import EnhancedMediaUpload from '../EnhancedMediaUpload';
import mediaUploadReducer from '../../../store/slices/mediaUploadSlice';
import messageReducer from '../../../store/slices/messageSlice';
import conversationReducer from '../../../store/slices/conversationSlice';
import encryptionReducer from '../../../store/slices/encryptionSlice';
import { render } from '../../../test/utils';

// Mock the SocketContext
const mockSocketContext = {
  socket: {
    emit: vi.fn(),
  },
};

vi.mock('../../../contexts/SocketContext', () => ({
  useSocket: () => mockSocketContext,
}));

// Mock the media API
vi.mock('../../../services/mediaApi', () => ({
  mediaApi: {
    shouldUseChunkedUpload: vi.fn(() => false),
    uploadSimple: vi.fn().mockResolvedValue({ id: 'media-123' }),
    uploadChunked: vi.fn().mockResolvedValue({ id: 'media-456' }),
  },
}));

// Mock the encryption hook
vi.mock('../../../hooks/useMediaEncryption', () => ({
  useMediaEncryption: () => ({
    encryptFile: vi.fn().mockResolvedValue({
      encryptedData: new ArrayBuffer(1024),
      nonce: 'mock-nonce',
      wrappedFileKey: 'mock-wrapped-key',
      fileHash: 'mock-hash',
    }),
    isReady: true,
  }),
}));

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mock-blob-url');
global.URL.revokeObjectURL = vi.fn();

// Mock crypto.getRandomValues
Object.defineProperty(global, 'crypto', {
  value: {
    getRandomValues: vi.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    }),
    subtle: {
      digest: vi.fn().mockResolvedValue(new ArrayBuffer(32)),
    },
  },
});

const createTestStore = () => {
  return configureStore({
    reducer: {
      mediaUpload: mediaUploadReducer,
      messages: messageReducer,
      conversations: conversationReducer,
      encryption: encryptionReducer,
    },
    preloadedState: {
      encryption: {
        conversationKeys: {
          'conv-1': 'mock-conversation-key',
        },
        isInitialized: true,
        loading: false,
        error: null,
      },
    },
  });
};

const renderWithStore = (component: React.ReactElement) => {
  const store = createTestStore();
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

describe('EnhancedMediaUpload', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders plus button correctly', () => {
    renderWithStore(
      <EnhancedMediaUpload
        conversationId="conv-1"
        onUploadComplete={vi.fn()}
        onUploadError={vi.fn()}
      />
    );

    expect(screen.getByTestId('media-upload-button')).toBeInTheDocument();
    expect(screen.getByTitle('Share media')).toBeInTheDocument();
  });

  it('opens upload dialog when plus button is clicked', async () => {
    const user = userEvent.setup();
    
    renderWithStore(
      <EnhancedMediaUpload
        conversationId="conv-1"
        onUploadComplete={vi.fn()}
        onUploadError={vi.fn()}
      />
    );

    const plusButton = screen.getByTestId('media-upload-button');
    await user.click(plusButton);

    await waitFor(() => {
      expect(screen.getByText('Share Media')).toBeInTheDocument();
    });
  });

  it('shows upload options in dialog', async () => {
    const user = userEvent.setup();
    
    renderWithStore(
      <EnhancedMediaUpload
        conversationId="conv-1"
        onUploadComplete={vi.fn()}
        onUploadError={vi.fn()}
      />
    );

    const plusButton = screen.getByTestId('media-upload-button');
    await user.click(plusButton);

    await waitFor(() => {
      expect(screen.getByText('Upload file from device')).toBeInTheDocument();
      expect(screen.getByText('Upload image from device')).toBeInTheDocument();
      expect(screen.getByText('Take photo using camera')).toBeInTheDocument();
      expect(screen.getByText('Upload video')).toBeInTheDocument();
      expect(screen.getByText('Upload audio')).toBeInTheDocument();
    });
  });

  it('handles file selection and shows preview', async () => {
    const user = userEvent.setup();
    const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
    
    renderWithStore(
      <EnhancedMediaUpload
        conversationId="conv-1"
        onUploadComplete={vi.fn()}
        onUploadError={vi.fn()}
      />
    );

    const plusButton = screen.getByTestId('media-upload-button');
    await user.click(plusButton);

    // Find and click the image upload option
    const imageOption = screen.getByText('Upload image from device');
    await user.click(imageOption);

    // Simulate file selection (this would normally trigger through file input)
    // We'll need to test this through the actual file input interaction
    const fileInput = document.querySelector('input[type="file"][accept="image/*"]') as HTMLInputElement;
    if (fileInput) {
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      fireEvent.change(fileInput);
    }

    await waitFor(() => {
      expect(screen.getByText('Preview Media')).toBeInTheDocument();
    });
  });

  it('disables button when disabled prop is true', () => {
    renderWithStore(
      <EnhancedMediaUpload
        conversationId="conv-1"
        onUploadComplete={vi.fn()}
        onUploadError={vi.fn()}
        disabled={true}
      />
    );

    const plusButton = screen.getByTestId('media-upload-button');
    expect(plusButton).toBeDisabled();
  });

  it('calls onUploadComplete when upload succeeds', async () => {
    const onUploadComplete = vi.fn();
    const user = userEvent.setup();
    const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
    
    renderWithStore(
      <EnhancedMediaUpload
        conversationId="conv-1"
        onUploadComplete={onUploadComplete}
        onUploadError={vi.fn()}
      />
    );

    // Simulate the full upload flow
    const plusButton = screen.getByTestId('media-upload-button');
    await user.click(plusButton);

    // This would require more complex mocking to fully test the upload flow
    // For now, we verify the component structure is correct
    expect(screen.getByText('Share Media')).toBeInTheDocument();
  });

  it('calls onUploadError when upload fails', async () => {
    const onUploadError = vi.fn();
    
    // Mock the mediaApi to throw an error
    const { mediaApi } = await import('../../../services/mediaApi');
    vi.mocked(mediaApi.uploadSimple).mockRejectedValueOnce(new Error('Upload failed'));
    
    renderWithStore(
      <EnhancedMediaUpload
        conversationId="conv-1"
        onUploadComplete={vi.fn()}
        onUploadError={onUploadError}
      />
    );

    // This would require simulating the full upload flow to test error handling
    expect(screen.getByTestId('media-upload-button')).toBeInTheDocument();
  });

  it('shows progress during upload', async () => {
    renderWithStore(
      <EnhancedMediaUpload
        conversationId="conv-1"
        onUploadComplete={vi.fn()}
        onUploadError={vi.fn()}
      />
    );

    // This test would require mocking the upload progress callbacks
    expect(screen.getByTestId('media-upload-button')).toBeInTheDocument();
  });

  it('handles camera capture', async () => {
    const user = userEvent.setup();
    
    // Mock getUserMedia
    Object.defineProperty(navigator, 'mediaDevices', {
      value: {
        getUserMedia: vi.fn().mockResolvedValue({
          getTracks: () => [{ stop: vi.fn() }],
        }),
      },
    });
    
    renderWithStore(
      <EnhancedMediaUpload
        conversationId="conv-1"
        onUploadComplete={vi.fn()}
        onUploadError={vi.fn()}
      />
    );

    const plusButton = screen.getByTestId('media-upload-button');
    await user.click(plusButton);

    const cameraOption = screen.getByText('Take photo using camera');
    await user.click(cameraOption);

    await waitFor(() => {
      expect(screen.getByText('Take Photo')).toBeInTheDocument();
    });
  });

  it('handles multiple file selection', async () => {
    const user = userEvent.setup();
    const mockFiles = [
      new File(['test content 1'], 'test1.jpg', { type: 'image/jpeg' }),
      new File(['test content 2'], 'test2.jpg', { type: 'image/jpeg' }),
    ];
    
    renderWithStore(
      <EnhancedMediaUpload
        conversationId="conv-1"
        onUploadComplete={vi.fn()}
        onUploadError={vi.fn()}
      />
    );

    const plusButton = screen.getByTestId('media-upload-button');
    await user.click(plusButton);

    // Simulate multiple file selection
    const imageOption = screen.getByText('Upload image from device');
    await user.click(imageOption);

    // This would require more complex file input simulation
    expect(screen.getByText('Share Media')).toBeInTheDocument();
  });
});
