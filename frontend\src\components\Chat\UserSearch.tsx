// frontend/src/components/Chat/UserSearch.tsx
import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { Icon } from '../ui/Icon';
import { Button } from '../ui/Button';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { ApiErrorDisplay } from '../ui/ApiErrorDisplay';
import { createDraftConversation } from '../../store/slices/conversationSlice';
import { useSearchUsersQuery } from '../../services';
import { useDebounce } from '../../hooks/useDebounce';
import type { AppDispatch } from '../../store';
import type { SearchUser } from '../../services/userApi';

interface UserSearchProps {
  onClose: () => void;
  onConversationCreated?: (conversationId: string) => void;
}

const UserSearch: React.FC<UserSearchProps> = ({ onClose, onConversationCreated }) => {
  const [query, setQuery] = useState('');
  const [creating, setCreating] = useState<string | null>(null);
  const dispatch = useDispatch<AppDispatch>();

  // Debounce the search query to avoid too many API calls
  const debouncedQuery = useDebounce(query, 300);

  // Use RTK Query for user search
  const {
    data: searchData,
    error: searchError,
    isLoading: isSearching,
    refetch: retrySearch
  } = useSearchUsersQuery(debouncedQuery, {
    skip: debouncedQuery.length < 2,
  });

  console.log(JSON.stringify(searchData, null, 2), 'sssssssssssss')

  const users = searchData?.results || [];

  const handleCreateConversation = (user: SearchUser) => {
    setCreating(user.id);

    try {
      // Create a draft conversation instead of making an API call
      dispatch(createDraftConversation({
        userId: user.id,
        username: user.username,
        first_name: user.first_name,
        last_name: user.last_name,
        profile_picture: user.profile_picture
      }));

      if (onConversationCreated) {
        onConversationCreated(`draft-${user.id}-${Date.now()}`);
      }
      onClose();
    } catch (err: any) {
      console.error('Failed to create conversation:', err);
    } finally {
      setCreating(null);
    }
  };

  const getInitials = (first_name: string, last_name: string) => {
    return `${first_name[0] || ''}${last_name[0] || ''}`.toUpperCase();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" data-testid="user-search-modal">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Start New Conversation</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <Icon name="x" size={20} />
          </button>
        </div>

        {/* Search Input */}
        <div className="p-4">
          <div className="relative">
            <Icon 
              name="search" 
              size={20} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" 
            />
            <input
              type="text"
              placeholder="Search users by name or username..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
              data-testid="user-search-input"
              autoFocus
            />
          </div>
        </div>

        {/* Results */}
        <div className="max-h-96 overflow-y-auto" data-testid="user-search-results">
          {isSearching && (
            <LoadingSpinner
              text="Searching..."
              centered
              className="py-8"
            />
          )}

          {searchError && (
            <div className="p-4">
              <ApiErrorDisplay
                error={searchError}
                onRetry={retrySearch}
                size="sm"
              />
            </div>
          )}
          {!isSearching && !searchError && query.length >= 2 && users.length === 0 && (
            <div className="p-4 text-center text-gray-500">
              <Icon name="search" size={24} className="mx-auto mb-2 text-gray-300" />
              <p>No users found matching "{query}"</p>
            </div>
          )}

          {!isSearching && !searchError && query.length < 2 && (
            <div className="p-4 text-center text-gray-500">
              <Icon name="search" size={24} className="mx-auto mb-2 text-gray-300" />
              <p>Type at least 2 characters to search</p>
            </div>
          )}

          {users.map((user) => (
            <div
              key={user.id}
              className="flex items-center justify-between p-4 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
              data-testid="user-result"
            >
              <div className="flex items-center space-x-3">
                {user.profile_picture ? (
                  <img
                    src={user.profile_picture}
                    alt={user.full_name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full bg-gray-400 flex items-center justify-center text-white font-medium">
                    {getInitials(user.first_name, user.last_name)}
                  </div>
                )}
                <div>
                  <p className="font-medium text-gray-900">{user.full_name}</p>
                  <p className="text-sm text-gray-500">@{user.username}</p>
                </div>
              </div>
              <Button
                onClick={() => handleCreateConversation(user)}
                disabled={creating === user.id}
                size="sm"
                className="flex items-center space-x-1"
                data-testid="user-action-button"
              >
                {creating === user.id ? (
                  <>
                    <Icon name="loader" size={14} className="animate-spin" />
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <Icon name="message-circle" size={14} />
                    <span>Chat</span>
                  </>
                )}
              </Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default UserSearch;
