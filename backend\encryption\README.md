# Phase 3: End-to-End Encryption Backend

This Django app implements the server-side components for Phase 3 end-to-end encryption.

## Security Features

### ✅ Critical Security Fixes Implemented

1. **Atomic One-Time Pre-Key Consumption**
   - Uses `SELECT FOR UPDATE` to prevent race conditions
   - Ensures each OPK is consumed exactly once

2. **Server-Side Signature Verification**
   - Verifies signed pre-key signatures over raw SPKI bytes
   - Uses ECDSA P-256 with SHA-256
   - Rejects invalid signatures immediately

3. **Rate Limiting & Security Monitoring**
   - Per-user and per-IP rate limiting
   - Security event logging for all operations
   - Comprehensive error handling and monitoring

4. **Non-Exportable Key Recommendations**
   - Documentation for client-side non-exportable private keys
   - Secure key storage guidelines

## Models

### UserKeyBundle
- Stores user's public identity key (ECDSA P-256) and signed pre-key (ECDH P-256)
- Includes signature verification for signed pre-key
- One-to-one relationship with User

### OneTimePreKey
- Stores one-time pre-keys for perfect forward secrecy
- Atomic consumption prevents race conditions
- Tracks usage with timestamps

### ConversationSession
- Stores encrypted Double Ratchet session state
- Per-participant session management
- Message counters and chain keys (encrypted)

### MessageKey
- Stores message keys for out-of-order message handling
- Part of Double Ratchet's skipped message key store
- Automatic cleanup to prevent memory exhaustion

### KeyBundleUploadLog
- Security monitoring and rate limiting
- Tracks all key bundle operations
- IP address and error logging

## API Endpoints

### POST /api/encryption/bundles/
Upload user's key bundle with signature verification.

**Security Features:**
- Rate limited (10/hour per user)
- Signature verification over raw SPKI bytes
- Atomic database operations
- Security event logging

### GET /api/encryption/bundles/{user_id}/
Get another user's key bundle for key exchange.

**Security Features:**
- Atomic one-time pre-key consumption
- SELECT FOR UPDATE prevents race conditions
- Returns bundle + one unused OPK (if available)

### POST /api/encryption/prekeys/
Upload batch of one-time pre-keys.

**Security Features:**
- Rate limited (50/hour per user)
- Key format validation
- Atomic batch operations
- Duplicate prevention

### GET /api/encryption/prekeys/count/
Get count of available one-time pre-keys.

## Installation

1. **Install Dependencies**
   ```bash
   pip install cryptography>=41.0.0
   ```

2. **Add to Django Settings**
   ```python
   INSTALLED_APPS = [
       # ... existing apps
       'encryption',
   ]
   ```

3. **Add URL Configuration**
   ```python
   # chatapp/urls.py
   urlpatterns = [
       # ... existing patterns
       path('api/encryption/', include('encryption.urls')),
   ]
   ```

4. **Run Migrations**
   ```bash
   python manage.py makemigrations encryption
   python manage.py makemigrations messaging  # For Message model updates
   python manage.py migrate
   ```

## Testing

Run comprehensive tests:
```bash
python manage.py test encryption
```

Tests cover:
- Cryptographic utility functions
- Model creation and constraints
- API endpoints with authentication
- Signature verification
- Rate limiting
- Atomic operations

## Security Considerations

### Server Security
- **Never stores private keys or plaintext messages**
- **Signature verification prevents key spoofing**
- **Rate limiting prevents DoS attacks**
- **Atomic operations prevent race conditions**
- **Comprehensive logging for security monitoring**

### Key Management
- **ECDSA P-256 for identity keys (signing)**
- **ECDH P-256 for pre-keys (key exchange)**
- **Raw SPKI bytes used for signature verification**
- **One-time pre-keys consumed atomically**

### Monitoring
- **All key bundle operations logged**
- **Failed signature verifications tracked**
- **Rate limiting violations recorded**
- **IP address tracking for security analysis**

## Production Deployment

### Required Environment Variables
```bash
# Django settings
SECRET_KEY=your-secret-key
DEBUG=False
ALLOWED_HOSTS=your-domain.com

# Database (recommended: PostgreSQL)
DATABASE_URL=postgresql://user:pass@host:port/dbname
```

### Security Hardening
1. **Enable HTTPS only**
2. **Configure proper CORS settings**
3. **Set up log monitoring and alerting**
4. **Regular security audits**
5. **Monitor rate limiting violations**

### Performance Considerations
1. **Database indexes on frequently queried fields**
2. **Connection pooling for high load**
3. **Caching for key bundle lookups**
4. **Regular cleanup of old message keys**

## Integration with Phase 2

This encryption app extends the existing Phase 2 messaging system:

1. **Message Model Updates**
   - Added encryption fields to existing Message model
   - Maintains backward compatibility with plaintext messages
   - Gradual migration from plaintext to encrypted

2. **API Compatibility**
   - New encryption endpoints don't break existing APIs
   - Existing message endpoints enhanced to support encrypted content
   - Client can detect encryption support via API responses

3. **Database Schema**
   - Additive changes only, no breaking modifications
   - Proper foreign key relationships with existing models
   - Indexes optimized for encryption queries

## Next Steps

After backend deployment:

1. **Client-Side Implementation**
   - Implement WebCrypto utilities
   - Create Signal protocol client
   - Integrate with existing Redux store

2. **Testing & Security Audit**
   - Cross-browser compatibility testing
   - Professional security audit
   - Performance optimization

3. **Gradual Rollout**
   - Feature flags for controlled deployment
   - Monitor encryption success rates
   - User education about enhanced security

## Support

For issues or questions about the encryption implementation:
1. Check the comprehensive test suite for examples
2. Review the Phase 3 implementation document
3. Verify all security fixes are properly implemented
4. Monitor logs for security events and errors
