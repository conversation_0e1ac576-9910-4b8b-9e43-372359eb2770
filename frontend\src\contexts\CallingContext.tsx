// frontend/src/contexts/CallingContext.tsx
import React, { createContext, useContext, useReducer, useEffect, useRef, useCallback } from 'react';
import { useSocket } from './SocketContext';
import { useAuth } from './AuthContext';
import { WebRTCManager, type CallQualityMetrics } from '../utils/webrtc';
import {
  useInitiateCallMutation,
  useAnswerCallMutation,
  useDeclineCallMutation,
  useEndCallMutation,
  useReportCallQualityMutation,
  type Call,
} from '../services/callingApi';

// Call state types
export type CallStatus = 'idle' | 'initiating' | 'ringing' | 'connecting' | 'active' | 'ending';

export interface CallParticipant {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
}

export interface ActiveCall {
  id: string;
  conversationId: string;
  type: 'audio' | 'video';
  status: CallStatus;
  isIncoming: boolean;
  caller: CallParticipant;
  callee: CallParticipant;
  startTime?: Date;
  duration?: number;
  localStream?: MediaStream;
  remoteStream?: MediaStream;
  isAudioEnabled: boolean;
  isVideoEnabled: boolean;
  connectionState: RTCPeerConnectionState;
  qualityMetrics?: CallQualityMetrics;
}

// Action types
type CallAction =
  | { type: 'INCOMING_CALL'; payload: { call: Call; caller: CallParticipant } }
  | { type: 'OUTGOING_CALL'; payload: { call: ActiveCall } }
  | { type: 'CALL_INITIATED'; payload: { callId: string } }
  | { type: 'CALL_RINGING' }
  | { type: 'CALL_ANSWERED'; payload: { calleeId: string } }
  | { type: 'CALL_CONNECTED'; payload: { localStream: MediaStream } }
  | { type: 'REMOTE_STREAM'; payload: { remoteStream: MediaStream } }
  | { type: 'CALL_ENDED'; payload: { reason?: string } }
  | { type: 'CALL_DECLINED' }
  | { type: 'CALL_FAILED'; payload: { error: string } }
  | { type: 'TOGGLE_AUDIO'; payload: { enabled: boolean } }
  | { type: 'TOGGLE_VIDEO'; payload: { enabled: boolean } }
  | { type: 'CONNECTION_STATE_CHANGE'; payload: { state: RTCPeerConnectionState } }
  | { type: 'QUALITY_UPDATE'; payload: { metrics: CallQualityMetrics } }
  | { type: 'UPDATE_DURATION'; payload: { duration: number } }
  | { type: 'CLEAR_CALL' };

// Call state
interface CallState {
  activeCall: ActiveCall | null;
  error: string | null;
}

const initialState: CallState = {
  activeCall: null,
  error: null,
};

// Reducer
function callReducer(state: CallState, action: CallAction): CallState {
  switch (action.type) {
    case 'INCOMING_CALL':
      return {
        ...state,
        activeCall: {
          id: action.payload.call.id,
          conversationId: action.payload.call.conversation,
          type: action.payload.call.call_type,
          status: 'ringing',
          isIncoming: true,
          caller: action.payload.caller,
          callee: action.payload.call.callee,
          isAudioEnabled: true,
          isVideoEnabled: action.payload.call.call_type === 'video',
          connectionState: 'new',
        },
        error: null,
      };

    case 'CALL_INITIATED':
      return {
        ...state,
        activeCall: state.activeCall ? {
          ...state.activeCall,
          status: 'initiating',
        } : null,
      };

    case 'CALL_RINGING':
      return {
        ...state,
        activeCall: state.activeCall ? {
          ...state.activeCall,
          status: 'ringing',
        } : null,
      };

    case 'CALL_ANSWERED':
      return {
        ...state,
        activeCall: state.activeCall ? {
          ...state.activeCall,
          status: 'connecting',
        } : null,
      };

    case 'CALL_CONNECTED':
      return {
        ...state,
        activeCall: state.activeCall ? {
          ...state.activeCall,
          status: 'active',
          localStream: action.payload.localStream,
          startTime: new Date(),
        } : null,
      };

    case 'REMOTE_STREAM':
      return {
        ...state,
        activeCall: state.activeCall ? {
          ...state.activeCall,
          remoteStream: action.payload.remoteStream,
        } : null,
      };

    case 'CALL_ENDED':
    case 'CALL_DECLINED':
      return {
        ...state,
        activeCall: null,
        error: null,
      };

    case 'CALL_FAILED':
      return {
        ...state,
        activeCall: null,
        error: action.payload.error,
      };

    case 'TOGGLE_AUDIO':
      return {
        ...state,
        activeCall: state.activeCall ? {
          ...state.activeCall,
          isAudioEnabled: action.payload.enabled,
        } : null,
      };

    case 'TOGGLE_VIDEO':
      return {
        ...state,
        activeCall: state.activeCall ? {
          ...state.activeCall,
          isVideoEnabled: action.payload.enabled,
        } : null,
      };

    case 'CONNECTION_STATE_CHANGE':
      return {
        ...state,
        activeCall: state.activeCall ? {
          ...state.activeCall,
          connectionState: action.payload.state,
        } : null,
      };

    case 'QUALITY_UPDATE':
      return {
        ...state,
        activeCall: state.activeCall ? {
          ...state.activeCall,
          qualityMetrics: action.payload.metrics,
        } : null,
      };

    case 'UPDATE_DURATION':
      return {
        ...state,
        activeCall: state.activeCall ? {
          ...state.activeCall,
          duration: action.payload.duration,
        } : null,
      };

    case 'CLEAR_CALL':
      return {
        ...state,
        activeCall: null,
        error: null,
      };

    default:
      return state;
  }
}

// Context type
interface CallingContextType {
  activeCall: ActiveCall | null;
  error: string | null;
  initiateCall: (conversationId: string, callType: 'audio' | 'video', callee: CallParticipant) => Promise<void>;
  answerCall: () => Promise<void>;
  declineCall: () => Promise<void>;
  endCall: () => Promise<void>;
  toggleAudio: () => void;
  toggleVideo: () => void;
  clearError: () => void;
}

const CallingContext = createContext<CallingContextType | null>(null);

// Provider component
interface CallingProviderProps {
  children: React.ReactNode;
}

export const CallingProvider: React.FC<CallingProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(callReducer, initialState);
  const { socket } = useSocket();
  const { user } = useAuth();
  const webrtcRef = useRef<WebRTCManager | null>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const callTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // API mutations
  const [initiateCallMutation] = useInitiateCallMutation();
  const [answerCallMutation] = useAnswerCallMutation();
  const [declineCallMutation] = useDeclineCallMutation();
  const [endCallMutation] = useEndCallMutation();
  const [reportQualityMutation] = useReportCallQualityMutation();

  // Initialize WebRTC manager when socket is available
  useEffect(() => {
    if (socket && !webrtcRef.current) {
      // Enable mock mode for testing (can be controlled via environment variable)
      const mockMode = import.meta.env.VITE_WEBRTC_MOCK_MODE === 'true' || 
                      window.location.search.includes('mock=true') ||
                      localStorage.getItem('webrtc_mock_mode') === 'true' ||
                      // Auto-enable mock mode in test environments
                      (typeof window !== 'undefined' && window.navigator.webdriver) ||
                      import.meta.env.MODE === 'test' ||
                      process.env.NODE_ENV === 'test';
      
      if (mockMode) {
        console.log('🎭 MOCK MODE ENABLED for WebRTC testing');
        // Set localStorage flag for consistency
        localStorage.setItem('webrtc_mock_mode', 'true');
      }
      
      webrtcRef.current = new WebRTCManager(socket, { mockMode });
      
      // Set up WebRTC event handlers
      webrtcRef.current.events = {
        onRemoteStream: (stream: MediaStream) => {
          dispatch({ type: 'REMOTE_STREAM', payload: { remoteStream: stream } });
        },
        onConnectionStateChange: (state: RTCPeerConnectionState) => {
          dispatch({ type: 'CONNECTION_STATE_CHANGE', payload: { state } });
          if (state === 'connected') {
            dispatch({ type: 'CALL_CONNECTED', payload: { localStream: webrtcRef.current!.getLocalStream()! } });
          }
        },
        onQualityUpdate: (metrics: CallQualityMetrics) => {
          dispatch({ type: 'QUALITY_UPDATE', payload: { metrics } });
          
          // Report quality metrics to backend
          if (state.activeCall) {
            reportQualityMutation({
              call_id: state.activeCall.id,
              quality_data: metrics,
            });
          }
        },
        onError: (error: Error) => {
          dispatch({ type: 'CALL_FAILED', payload: { error: error.message } });
        },
      };
    }
  }, [socket, reportQualityMutation, state.activeCall]);

  // Socket event listeners
  useEffect(() => {
    if (!socket) return;

    const handleIncomingCall = (data: {
      callId: string;
      caller: CallParticipant;
      callType: 'audio' | 'video';
      conversationId: string;
      timestamp: string;
    }) => {
      console.log('📞 Socket event received: incoming_call', data);
      const call: Call = {
        id: data.callId,
        conversation: data.conversationId,
        caller: data.caller,
        callee: {
          id: user?.id || '',
          username: user?.username || '',
          firstName: user?.firstName || '',
          lastName: user?.lastName || '',
          profilePicture: user?.profilePicture
        },
        call_type: data.callType,
        status: 'ringing',
        initiated_at: data.timestamp,
        session_id: '',
        quality_issues: [],
        metadata: {},
      };

      dispatch({ type: 'INCOMING_CALL', payload: { call, caller: data.caller } });
    };

    const handleCallAnswered = (data: { callId: string; calleeId: string }) => {
      console.log('📞 Socket event received: call_answered', data);
      dispatch({ type: 'CALL_ANSWERED', payload: { calleeId: data.calleeId } });
    };

    const handleCallDeclined = (data?: any) => {
      console.log('📞 Socket event received: call_declined', data);
      dispatch({ type: 'CALL_DECLINED' });
      cleanup();
    };

    const handleCallEnded = (data?: any) => {
      console.log('📞 Socket event received: call_ended', data);
      dispatch({ type: 'CALL_ENDED', payload: {} });
      cleanup();
    };

    const handleCallRinging = (data?: any) => {
      console.log('📞 Socket event received: call_ringing', data);
      dispatch({ type: 'CALL_RINGING' });
    };

    socket.on('incoming_call', handleIncomingCall);
    socket.on('call_answered', handleCallAnswered);
    socket.on('call_declined', handleCallDeclined);
    socket.on('call_ended', handleCallEnded);
    socket.on('call_ringing', handleCallRinging);

    return () => {
      socket.off('incoming_call', handleIncomingCall);
      socket.off('call_answered', handleCallAnswered);
      socket.off('call_declined', handleCallDeclined);
      socket.off('call_ended', handleCallEnded);
      socket.off('call_ringing', handleCallRinging);
    };
  }, [socket]);

  // Duration timer
  useEffect(() => {
    if (state.activeCall?.status === 'active' && state.activeCall.startTime) {
      durationIntervalRef.current = setInterval(() => {
        const duration = Math.floor((Date.now() - state.activeCall!.startTime!.getTime()) / 1000);
        dispatch({ type: 'UPDATE_DURATION', payload: { duration } });
      }, 1000);
    } else {
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
        durationIntervalRef.current = null;
      }
    }

    return () => {
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
    };
  }, [state.activeCall?.status, state.activeCall?.startTime]);

  // Call timeout management
  useEffect(() => {
    // Clear any existing timeout
    if (callTimeoutRef.current) {
      clearTimeout(callTimeoutRef.current);
      callTimeoutRef.current = null;
    }

    // Set timeout for calls in initiating or ringing state
    if (state.activeCall && 
        (state.activeCall.status === 'initiating' || state.activeCall.status === 'ringing')) {
      
      const timeoutDuration = state.activeCall.status === 'initiating' ? 30000 : 60000; // 30s for initiating, 60s for ringing
      
      callTimeoutRef.current = setTimeout(() => {
        console.warn(`⏰ Call timeout: ${state.activeCall?.status} state exceeded ${timeoutDuration/1000}s`);
        dispatch({ 
          type: 'CALL_FAILED', 
          payload: { 
            error: state.activeCall?.status === 'initiating' 
              ? 'Call initiation timeout - unable to reach recipient'
              : 'Call timeout - no response from recipient'
          } 
        });
        cleanup();
      }, timeoutDuration);
    }

    return () => {
      if (callTimeoutRef.current) {
        clearTimeout(callTimeoutRef.current);
        callTimeoutRef.current = null;
      }
    };
  }, [state.activeCall?.status, state.activeCall?.id]);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (webrtcRef.current) {
      webrtcRef.current.endCall();
    }
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }
    if (callTimeoutRef.current) {
      clearTimeout(callTimeoutRef.current);
      callTimeoutRef.current = null;
    }
  }, []);

  // API functions
  const initiateCall = async (conversationId: string, callType: 'audio' | 'video', callee: CallParticipant) => {
    try {
      console.log('🔥🔥🔥 INITIATE_CALL FUNCTION CALLED 🔥🔥🔥');
      console.log('🔥 Initiating call - socket available:', !!socket);
      console.log('🔌 Socket connected:', socket?.connected);
      console.log('🆔 Socket ID:', socket?.id);
      console.log('🏠 Socket rooms:', socket?.rooms);
      
      if (!socket) {
        console.error('❌ Socket not available during call initiation');
        return;
      }
      
      if (!socket.connected) {
        console.error('❌ Socket not connected during call initiation');
        return;
      }
      
      const isMockMode = webrtcRef.current?.isMockMode() || false;
      
      if (isMockMode) {
        console.log('🎭 MOCK MODE: Initiating call with mock WebRTC');
      }

      // Check media availability (mock mode will always return available)
      if (webrtcRef.current) {
        const mediaCheck = await webrtcRef.current.checkMediaAvailability(callType === 'video');
        if (!mediaCheck.audio && !isMockMode) {
          throw new Error('No audio device available');
        }
        if (callType === 'video' && !mediaCheck.video && !isMockMode) {
          console.warn('No video device available, falling back to audio-only call');
          callType = 'audio';
        }
      }

      // Create temporary call state
      const tempCall: ActiveCall = {
        id: 'temp',
        conversationId,
        type: callType,
        status: 'initiating',
        isIncoming: false,
        caller: callee, // Will be updated
        callee,
        isAudioEnabled: true,
        isVideoEnabled: callType === 'video',
        connectionState: 'new',
      };

      dispatch({ type: 'INCOMING_CALL', payload: { call: tempCall as any, caller: callee } });

      // Initialize WebRTC for outgoing call with placeholder - will be updated when call is created
      if (webrtcRef.current) {
        await webrtcRef.current.initializeCall('pending', callType === 'video');
      }
      
      console.log('[DEBUG] WebRTC initialized with placeholder ID for outgoing call');

      // Call backend API
      const result = await initiateCallMutation({
        conversationId: conversationId,
        callType: callType,
      }).unwrap();

      console.log('✅ Call initiated successfully:', result);
      
      // Update call ID
      dispatch({ type: 'CALL_INITIATED', payload: { callId: result.id } });
      
      // Emit socket event to notify callee
      console.log('🔌🔌🔌 ABOUT TO EMIT SOCKET EVENT 🔌🔌🔌');
      console.log('📞 Socket available, emitting initiate_call event');
      console.log('📞 Socket details before emit:', {
        connected: socket.connected,
        id: socket.id,
        rooms: socket.rooms
      });
      const emitResult = socket.emit('initiate_call', {
        conversationId: conversationId,
        callType: callType,
      });
      console.log('🔌🔌🔌 SOCKET EVENT EMITTED SUCCESSFULLY 🔌🔌🔌');
      console.log('📞 Socket event emitted: initiate_call', { conversationId, callType });
      console.log('📡 Emit result:', emitResult);
        
        // Re-initialize WebRTC with the actual call ID
        if (webrtcRef.current) {
          console.log(`[DEBUG] Re-initializing WebRTC with actual call ID: ${result.id}`);
          await webrtcRef.current.initializeCall(result.id, callType === 'video');
        }

      // Create WebRTC offer
      if (webrtcRef.current) {
        await webrtcRef.current.createOffer();
      }

    } catch (error) {
      console.error('Failed to initiate call:', error);
      dispatch({ type: 'CALL_FAILED', payload: { error: error instanceof Error ? error.message : 'Failed to initiate call' } });
      cleanup();
    }
  };

  const answerCall = async () => {
    if (!state.activeCall) return;

    try {
      const isMockMode = webrtcRef.current?.isMockMode() || false;
      
      if (isMockMode) {
        console.log('🎭 MOCK MODE: Answering call with mock WebRTC');
      }

      // Call backend API first
      await answerCallMutation(state.activeCall.id).unwrap();
      
      // Dispatch CALL_ANSWERED with required payload
      dispatch({ type: 'CALL_ANSWERED', payload: { calleeId: state.activeCall.callee.id } });

      // Initialize WebRTC and get local stream
      if (webrtcRef.current) {
        // initializeCall returns Promise<void>, not the stream
        await webrtcRef.current.initializeCall(state.activeCall.id, state.activeCall.type === 'video');
        
        // Get the local stream after initialization
        const localStream = webrtcRef.current.getLocalStream();
        
        // Transition to active call state
        if (localStream) {
          console.log('✅ Local stream obtained, transitioning to active call state');
          dispatch({ type: 'CALL_CONNECTED', payload: { localStream } });
        } else {
          console.warn('⚠️ No local stream available after WebRTC initialization');
          // In mock mode, we should still transition to active state
          if (isMockMode) {
            console.log('🎭 Mock mode: Transitioning to active state without real stream');
            dispatch({ type: 'CALL_CONNECTED', payload: { localStream: null } });
          }
        }
      }

    } catch (error) {
      console.error('Failed to answer call:', error);
      dispatch({ type: 'CALL_FAILED', payload: { error: error instanceof Error ? error.message : 'Failed to answer call' } });
      cleanup();
    }
  };

  const declineCall = async () => {
    if (!state.activeCall) return;

    try {
      await declineCallMutation(state.activeCall.id).unwrap();
      dispatch({ type: 'CALL_DECLINED' });
    } catch (error) {
      dispatch({ type: 'CALL_FAILED', payload: { error: 'Failed to decline call' } });
    } finally {
      cleanup();
    }
  };

  const endCall = async () => {
    if (!state.activeCall) return;

    try {
      await endCallMutation(state.activeCall.id).unwrap();
      dispatch({ type: 'CALL_ENDED', payload: {} });
    } catch (error) {
      dispatch({ type: 'CALL_FAILED', payload: { error: 'Failed to end call' } });
    } finally {
      cleanup();
    }
  };

  const toggleAudio = () => {
    if (webrtcRef.current) {
      const enabled = webrtcRef.current.toggleAudio();
      dispatch({ type: 'TOGGLE_AUDIO', payload: { enabled } });
    }
  };

  const toggleVideo = () => {
    if (webrtcRef.current) {
      const enabled = webrtcRef.current.toggleVideo();
      dispatch({ type: 'TOGGLE_VIDEO', payload: { enabled } });
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_CALL' });
  };

  const contextValue: CallingContextType = {
    activeCall: state.activeCall,
    error: state.error,
    initiateCall,
    answerCall,
    declineCall,
    endCall,
    toggleAudio,
    toggleVideo,
    clearError,
  };

  return (
    <CallingContext.Provider value={contextValue}>
      {children}
    </CallingContext.Provider>
  );
};

export const useCalling = (): CallingContextType => {
  const context = useContext(CallingContext);
  if (!context) {
    throw new Error('useCalling must be used within a CallingProvider');
  }
  return context;
};
