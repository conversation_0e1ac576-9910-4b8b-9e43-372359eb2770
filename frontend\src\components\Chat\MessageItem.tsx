// frontend/src/components/Chat/MessageItem.tsx
import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import { Icon } from '../ui/Icon';
import MessageStatus from './MessageStatus';
import MediaMessage from './MediaMessage';
import { useMessageDecryption } from '../../hooks/useMessageDecryption';
import type { Message as ApiMessage } from '../../services/messageApi';
import type { Message as StoreMessage } from '../../store/slices/messageSlice';
import type { MessageStatusType } from '../../store';
import type { MediaFile } from '../../services/mediaApi';

// Union type to handle both API and store message formats
type MessageType = ApiMessage | StoreMessage;

interface MessageItemProps {
  message: MessageType;
  currentUserId: string;
  isOwnMessage: boolean;
  isSending: boolean;
  messageStatus?: MessageStatusType;
  isFailed: boolean;
  mediaFiles?: MediaFile[];
  conversationId: string;
  onReply?: (message: MessageType) => void;
  onRetry?: () => void;
}

const getInitials = (firstName?: string, lastName?: string): string => {
  const first = firstName?.charAt(0)?.toUpperCase() || '';
  const last = lastName?.charAt(0)?.toUpperCase() || '';
  return first + last || '?';
};

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  currentUserId,
  isOwnMessage,
  isSending,
  messageStatus,
  isFailed,
  mediaFiles = [],
  conversationId,
  onReply,
  onRetry
}) => {
  // Use the decryption hook to get the proper content
  const { decryptedContent, isDecrypting, decryptionError } = useMessageDecryption(message);

  // Determine what content to display
  const displayContent = React.useMemo(() => {
    if (isDecrypting) {
      return '🔓 Decrypting...';
    }
    
    if (decryptionError) {
      return `[Decryption Error: ${decryptionError}]`;
    }
    
    return decryptedContent || '[No Content]';
  }, [decryptedContent, isDecrypting, decryptionError]);

  return (
    <div
      className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
      data-message-id={message.id}
      data-sender-id={message.sender.id}
      data-testid="message"
    >
      <div className={`flex max-w-[70%] items-end space-x-2 ${isOwnMessage ? 'flex-row-reverse space-x-reverse' : ''}`}>
        {!isOwnMessage && (
          <div className="flex-shrink-0">
            {message.sender.profile_picture ? (
              <img
                src={message.sender.profile_picture}
                alt={`${message.sender.first_name} ${message.sender.last_name}`}
                className="w-8 h-8 rounded-full object-cover"
              />
            ) : (
              <div className="w-8 h-8 rounded-full bg-gray-400 flex items-center justify-center text-white text-sm font-medium">
                {getInitials(message.sender.first_name, message.sender.last_name)}
              </div>
            )}
          </div>
        )}

        <div
          className={`relative group rounded-lg px-4 py-2 max-w-full ${
            isOwnMessage
              ? 'bg-blue-600 text-white rounded-br-none'
              : 'bg-gray-100 text-gray-900 rounded-bl-none'
          } ${isSending ? 'opacity-70' : ''} ${isDecrypting ? 'opacity-50' : ''}`}
        >
          {/* Render media files if present */}
          {mediaFiles.length > 0 ? (
            <div className="space-y-2">
              {mediaFiles.map((mediaFile) => (
                <MediaMessage
                  key={mediaFile.id}
                  mediaFile={mediaFile}
                  conversationId={conversationId}
                  isOwn={isOwnMessage}
                />
              ))}
              {/* Show text content if present along with media */}
              {displayContent && displayContent !== '[No Content]' && (
                <div className="text-sm leading-relaxed mt-2">
                  {displayContent}
                </div>
              )}
            </div>
          ) : (
            <div className="text-sm leading-relaxed">
              {displayContent}
            </div>
          )}

          <div className={`flex items-center justify-between mt-1 ${
            isOwnMessage ? 'text-blue-100' : 'text-gray-500'
          }`}>
            <div className="text-xs opacity-70">
              {message.createdAt
                ? formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })
                : 'No date'}
            </div>

            <MessageStatus
              status={messageStatus}
              isOwnMessage={isOwnMessage}
              isSending={isSending}
              onRetry={isFailed ? onRetry : undefined}
              className={isOwnMessage ? 'text-blue-100' : 'text-gray-500'}
            />
          </div>

          {/* Message actions */}
          {onReply && !isSending && !isDecrypting && (
            <button
              onClick={() => onReply(message)}
              className={`absolute top-0 right-0 transform translate-x-8 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-full ${
                isOwnMessage ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-600'
              } hover:bg-opacity-80`}
              title="Reply to message"
            >
              <Icon name="message-circle" size={14} />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default MessageItem;
