// frontend/src/components/Chat/__tests__/MessageInput.test.tsx
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import MessageInput from '../MessageInput'
import { renderWithStore } from '../../../test/utils'

// Mock the SocketContext
const mockSocketContext = {
  sendMessage: vi.fn().mockResolvedValue('msg-id'),
  startTyping: vi.fn(),
  stopTyping: vi.fn(),
  isConnected: true
}

vi.mock('../../../contexts/SocketContext', () => ({
  useSocket: () => mockSocketContext
}))

// Mock the Button component
vi.mock('../../ui/Button', () => ({
  Button: ({ children, disabled, onClick, type, className }: any) => (
    <button 
      type={type}
      disabled={disabled}
      onClick={onClick}
      className={className}
      data-testid="send-button"
    >
      {children}
    </button>
  )
}))

// Mock the Icon component
vi.mock('../../ui/Icon', () => ({
  Icon: ({ name, size, className }: any) => (
    <div data-testid={`icon-${name}`} className={className} style={{ width: size, height: size }}>
      {name}
    </div>
  )
}))

describe('MessageInput', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockSocketContext.isConnected = true
  })

  it('renders message input correctly', () => {
    renderWithStore(<MessageInput conversationId="conv-1" />)
    
    expect(screen.getByPlaceholderText('Type a message...')).toBeInTheDocument()
    expect(screen.getByTestId('send-button')).toBeInTheDocument()
    expect(screen.getByTestId('icon-plus')).toBeInTheDocument() // attachment button
  })

  it('handles text input', async () => {
    const user = userEvent.setup()
    
    renderWithStore(<MessageInput conversationId="conv-1" />)
    
    const input = screen.getByPlaceholderText('Type a message...')
    await user.type(input, 'Hello world!')
    
    expect(input).toHaveValue('Hello world!')
  })

  it('sends message on form submit', async () => {
    const user = userEvent.setup()
    
    renderWithStore(<MessageInput conversationId="conv-1" />)
    
    const input = screen.getByPlaceholderText('Type a message...')
    const sendButton = screen.getByTestId('send-button')
    
    await user.type(input, 'Hello world!')
    await user.click(sendButton)
    
    expect(mockSocketContext.sendMessage).toHaveBeenCalledWith('conv-1', 'Hello world!')
    expect(input).toHaveValue('')
  })

  it('sends message on Enter key press', async () => {
    const user = userEvent.setup()
    
    renderWithStore(<MessageInput conversationId="conv-1" />)
    
    const input = screen.getByPlaceholderText('Type a message...')
    
    await user.type(input, 'Hello world!')
    await user.keyboard('{Enter}')
    
    expect(mockSocketContext.sendMessage).toHaveBeenCalledWith('conv-1', 'Hello world!')
    expect(input).toHaveValue('')
  })

  it('does not send message on Shift+Enter', async () => {
    const user = userEvent.setup()
    
    renderWithStore(<MessageInput conversationId="conv-1" />)
    
    const input = screen.getByPlaceholderText('Type a message...')
    
    await user.type(input, 'Hello world!')
    await user.keyboard('{Shift>}{Enter}{/Shift}')
    
    expect(mockSocketContext.sendMessage).not.toHaveBeenCalled()
    // Should add a new line instead
    expect(input).toHaveValue('Hello world!\n')
  })

  it('does not send empty messages', async () => {
    const user = userEvent.setup()
    
    renderWithStore(<MessageInput conversationId="conv-1" />)
    
    const sendButton = screen.getByTestId('send-button')
    
    await user.click(sendButton)
    
    expect(mockSocketContext.sendMessage).not.toHaveBeenCalled()
  })

  it('trims whitespace from messages', async () => {
    const user = userEvent.setup()
    
    renderWithStore(<MessageInput conversationId="conv-1" />)
    
    const input = screen.getByPlaceholderText('Type a message...')
    const sendButton = screen.getByTestId('send-button')
    
    await user.type(input, '   Hello world!   ')
    await user.click(sendButton)
    
    expect(mockSocketContext.sendMessage).toHaveBeenCalledWith('conv-1', 'Hello world!')
  })

  it('handles typing indicators', async () => {
    const user = userEvent.setup()
    
    renderWithStore(<MessageInput conversationId="conv-1" />)
    
    const input = screen.getByPlaceholderText('Type a message...')
    
    await user.type(input, 'H')
    
    expect(mockSocketContext.startTyping).toHaveBeenCalledWith('conv-1')
  })

  it('disables input when not connected', () => {
    mockSocketContext.isConnected = false
    
    renderWithStore(<MessageInput conversationId="conv-1" />)
    
    const input = screen.getByPlaceholderText('Connecting...')
    const sendButton = screen.getByTestId('send-button')
    
    expect(input).toBeDisabled()
    expect(sendButton).toBeDisabled()
    
    // Should show connection warning
    expect(screen.getByText('Reconnecting to chat server...')).toBeInTheDocument()
    expect(screen.getByTestId('icon-alert-triangle')).toBeInTheDocument()
  })

  it('disables input when disabled prop is true', () => {
    renderWithStore(<MessageInput conversationId="conv-1" disabled={true} />)
    
    const input = screen.getByPlaceholderText('Type a message...')
    const sendButton = screen.getByTestId('send-button')
    
    expect(input).toBeDisabled()
    expect(sendButton).toBeDisabled()
  })
})
