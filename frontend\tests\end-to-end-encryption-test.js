// End-to-End Encryption Flow Test
// Run this in the browser console after logging in and navigating to a conversation

console.log('🚀 [E2E TEST] Starting End-to-End Encryption Flow Test...');

// Test configuration
const TEST_CONFIG = {
  testMessage: 'Hello, this is an encrypted test message!',
  expectedEncryptedFormat: [
    'conversationId',
    'tempId',
    'messageType', 
    'encryptedContent',
    'iv',
    'senderRatchetKey',
    'messageNumber',
    'previousChainLength'
  ],
  timeout: 10000 // 10 seconds
};

// Test state
let testResults = {
  encryptionTest: false,
  socketSendTest: false,
  serverResponseTest: false,
  decryptionTest: false,
  uiDisplayTest: false
};

let capturedOutgoingMessages = [];
let capturedIncomingMessages = [];

// Step 1: Set up message interception
console.log('📡 [E2E TEST] Setting up message interception...');

// Intercept outgoing socket messages
if (window.io && window.io.sockets && window.io.sockets[0]) {
  const socket = window.io.sockets[0];
  const originalEmit = socket.emit;
  
  socket.emit = function(event, data) {
    if (event === 'send_message') {
      capturedOutgoingMessages.push(data);
      console.log('📤 [E2E TEST] Captured outgoing message:', data);
      
      // Test 1: Verify encryption format
      if (data.encryptedContent && data.iv && data.senderRatchetKey) {
        console.log('✅ [E2E TEST] Encryption Test PASSED - Message has encrypted format');
        testResults.encryptionTest = true;
        
        // Verify all required fields
        const hasAllFields = TEST_CONFIG.expectedEncryptedFormat.every(field => 
          data.hasOwnProperty(field)
        );
        
        if (hasAllFields) {
          console.log('✅ [E2E TEST] Socket Send Test PASSED - All required fields present');
          testResults.socketSendTest = true;
        } else {
          console.error('❌ [E2E TEST] Socket Send Test FAILED - Missing required fields');
        }
        
        // Verify no plaintext content
        if (!data.content) {
          console.log('✅ [E2E TEST] No plaintext content found in outgoing message');
        } else {
          console.error('❌ [E2E TEST] Plaintext content found in outgoing message:', data.content);
        }
        
        // Verify senderRatchetKey length
        if (data.senderRatchetKey.length >= 50) {
          console.log('✅ [E2E TEST] senderRatchetKey meets SPKI format requirements');
        } else {
          console.error('❌ [E2E TEST] senderRatchetKey too short:', data.senderRatchetKey.length);
        }
        
      } else {
        console.error('❌ [E2E TEST] Encryption Test FAILED - Message not encrypted');
      }
    }
    return originalEmit.call(this, event, data);
  };
  
  // Intercept incoming messages
  const originalOn = socket.on;
  socket.on = function(event, handler) {
    if (event === 'new_message') {
      const wrappedHandler = function(data) {
        capturedIncomingMessages.push(data);
        console.log('📥 [E2E TEST] Captured incoming message:', data);
        
        // Test 2: Verify server response
        if (data.isEncrypted && data.encryptedContent) {
          console.log('✅ [E2E TEST] Server Response Test PASSED - Received encrypted message');
          testResults.serverResponseTest = true;
        } else if (data.content && !data.isEncrypted) {
          console.log('✅ [E2E TEST] Server Response Test PASSED - Received plaintext message (legacy)');
          testResults.serverResponseTest = true;
        }
        
        // Test 3: Check if message will be decrypted for UI
        setTimeout(() => {
          // Check if the message appears in the UI with decrypted content
          const messageElements = document.querySelectorAll('[data-testid="message"]');
          const lastMessage = messageElements[messageElements.length - 1];
          
          if (lastMessage) {
            const messageText = lastMessage.textContent;
            if (messageText.includes(TEST_CONFIG.testMessage)) {
              console.log('✅ [E2E TEST] UI Display Test PASSED - Decrypted message displayed in UI');
              testResults.uiDisplayTest = true;
              testResults.decryptionTest = true;
            } else if (messageText.includes('[Encrypted Message]') || messageText.includes('Encrypted:')) {
              console.log('⚠️ [E2E TEST] UI Display Test PARTIAL - Encrypted message shown but not decrypted');
              testResults.uiDisplayTest = true;
              testResults.decryptionTest = false;
            } else {
              console.log('❌ [E2E TEST] UI Display Test FAILED - Message not found in UI');
            }
          }
          
          // Print final results after a delay
          setTimeout(printTestResults, 2000);
        }, 1000);
        
        return handler(data);
      };
      return originalOn.call(this, event, wrappedHandler);
    }
    return originalOn.call(this, event, handler);
  };
  
  console.log('✅ [E2E TEST] Message interception set up successfully');
} else {
  console.error('❌ [E2E TEST] Socket not found - make sure you are connected');
}

// Step 2: Function to run the test
window.runEncryptionE2ETest = function() {
  console.log('🧪 [E2E TEST] Starting test execution...');
  
  // Reset test results
  testResults = {
    encryptionTest: false,
    socketSendTest: false,
    serverResponseTest: false,
    decryptionTest: false,
    uiDisplayTest: false
  };
  
  capturedOutgoingMessages = [];
  capturedIncomingMessages = [];
  
  // Find message input and send button
  const messageInput = document.querySelector('[data-testid="message-input"]');
  const sendButton = document.querySelector('[data-testid="send-button"]');
  
  if (!messageInput || !sendButton) {
    console.error('❌ [E2E TEST] Message input or send button not found');
    console.log('Make sure you are on a conversation page with the chat interface visible');
    return;
  }
  
  // Clear input and type test message
  messageInput.value = '';
  messageInput.focus();
  
  // Simulate typing
  messageInput.value = TEST_CONFIG.testMessage;
  messageInput.dispatchEvent(new Event('input', { bubbles: true }));
  
  console.log('📝 [E2E TEST] Test message entered:', TEST_CONFIG.testMessage);
  
  // Click send button
  setTimeout(() => {
    sendButton.click();
    console.log('📤 [E2E TEST] Send button clicked');
  }, 500);
};

// Step 3: Function to print test results
function printTestResults() {
  console.log('\n🏁 [E2E TEST] Test Results Summary:');
  console.log('=====================================');
  
  const tests = [
    { name: 'Message Encryption', passed: testResults.encryptionTest },
    { name: 'Socket Send Format', passed: testResults.socketSendTest },
    { name: 'Server Response', passed: testResults.serverResponseTest },
    { name: 'Message Decryption', passed: testResults.decryptionTest },
    { name: 'UI Display', passed: testResults.uiDisplayTest }
  ];
  
  tests.forEach(test => {
    const status = test.passed ? '✅ PASSED' : '❌ FAILED';
    console.log(`${status} - ${test.name}`);
  });
  
  const passedCount = tests.filter(t => t.passed).length;
  const totalCount = tests.length;
  
  console.log(`\n📊 Overall: ${passedCount}/${totalCount} tests passed`);
  
  if (passedCount === totalCount) {
    console.log('🎉 [E2E TEST] All tests PASSED! End-to-end encryption is working correctly.');
  } else {
    console.log('⚠️ [E2E TEST] Some tests FAILED. Check the issues above.');
  }
  
  // Show captured messages for debugging
  console.log('\n📋 [E2E TEST] Captured Messages:');
  console.log('Outgoing:', capturedOutgoingMessages);
  console.log('Incoming:', capturedIncomingMessages);
}

// Step 4: Instructions
console.log('\n📋 [E2E TEST] Instructions:');
console.log('1. Make sure you are logged in and on a conversation page');
console.log('2. Run: runEncryptionE2ETest()');
console.log('3. Wait for the test to complete (about 5-10 seconds)');
console.log('4. Check the test results summary');

console.log('\n✅ [E2E TEST] Setup complete! Ready to run test.');
