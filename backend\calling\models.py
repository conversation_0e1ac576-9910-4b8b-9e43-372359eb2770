# backend/calling/models.py
import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class Call(models.Model):
    CALL_TYPES = [
        ('audio', 'Audio Call'),
        ('video', 'Video Call'),
    ]
    
    CALL_STATUS = [
        ('initiated', 'Initiated'),
        ('ringing', 'Ringing'),
        ('answered', 'Answered'),
        ('active', 'Active'),
        ('ended', 'Ended'),
        ('missed', 'Missed'),
        ('declined', 'Declined'),
        ('failed', 'Failed'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey('messaging.Conversation', on_delete=models.CASCADE, related_name='calls')
    caller = models.ForeignKey(User, on_delete=models.CASCADE, related_name='initiated_calls')
    callee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_calls')
    
    call_type = models.CharField(max_length=10, choices=CALL_TYPES)
    status = models.CharField(max_length=15, choices=CALL_STATUS, default='initiated')
    
    # Timing
    initiated_at = models.DateTimeField(auto_now_add=True)
    answered_at = models.DateTimeField(null=True, blank=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    duration = models.DurationField(null=True, blank=True)  # Calculated field
    
    # WebRTC session data
    session_id = models.CharField(max_length=100, unique=True)
    caller_sdp = models.TextField(blank=True, null=True)
    callee_sdp = models.TextField(blank=True, null=True)
    
    # Call quality metrics
    quality_rating = models.IntegerField(null=True, blank=True)  # 1-5 rating
    quality_issues = models.JSONField(default=list)  # List of reported issues
    
    # Metadata
    metadata = models.JSONField(default=dict)  # Additional call data
    
    class Meta:
        db_table = 'calls'
        ordering = ['-initiated_at']
        indexes = [
            models.Index(fields=['conversation', 'status']),
            models.Index(fields=['caller', 'initiated_at']),
            models.Index(fields=['callee', 'initiated_at']),
            models.Index(fields=['session_id']),
        ]
    
    def save(self, *args, **kwargs):
        # Calculate duration if call ended
        if self.ended_at and self.answered_at:
            self.duration = self.ended_at - self.answered_at
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.call_type.title()} call from {self.caller.username} to {self.callee.username}"


class CallParticipant(models.Model):
    """For future group calling support"""
    PARTICIPANT_STATUS = [
        ('invited', 'Invited'),
        ('joined', 'Joined'),
        ('left', 'Left'),
        ('declined', 'Declined'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    call = models.ForeignKey(Call, on_delete=models.CASCADE, related_name='participants')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='call_participations')
    status = models.CharField(max_length=15, choices=PARTICIPANT_STATUS, default='invited')
    
    joined_at = models.DateTimeField(null=True, blank=True)
    left_at = models.DateTimeField(null=True, blank=True)
    
    # Media state
    audio_enabled = models.BooleanField(default=True)
    video_enabled = models.BooleanField(default=True)
    screen_sharing = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'call_participants'
        unique_together = ['call', 'user']
        indexes = [
            models.Index(fields=['call', 'status']),
            models.Index(fields=['user', 'joined_at']),
        ]


class CallEvent(models.Model):
    """Track call events for debugging and analytics"""
    EVENT_TYPES = [
        ('call_initiated', 'Call Initiated'),
        ('call_ringing', 'Call Ringing'),
        ('call_answered', 'Call Answered'),
        ('call_ended', 'Call Ended'),
        ('call_declined', 'Call Declined'),
        ('call_failed', 'Call Failed'),
        ('ice_candidate', 'ICE Candidate'),
        ('media_toggle', 'Media Toggle'),
        ('quality_issue', 'Quality Issue'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    call = models.ForeignKey(Call, on_delete=models.CASCADE, related_name='events')
    event_type = models.CharField(max_length=20, choices=EVENT_TYPES)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='call_events')
    event_data = models.JSONField(default=dict)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'call_events'
        ordering = ['timestamp']
        indexes = [
            models.Index(fields=['call', 'timestamp']),
            models.Index(fields=['event_type', 'timestamp']),
        ]


class CallQualityMetric(models.Model):
    """Store call quality metrics"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    call = models.ForeignKey(Call, on_delete=models.CASCADE, related_name='quality_metrics')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='call_quality_reports')

    # Network metrics
    packet_loss = models.FloatField(null=True, blank=True)  # Percentage
    jitter = models.FloatField(null=True, blank=True)  # Milliseconds
    round_trip_time = models.FloatField(null=True, blank=True)  # Milliseconds
    bandwidth_upload = models.IntegerField(null=True, blank=True)  # Kbps
    bandwidth_download = models.IntegerField(null=True, blank=True)  # Kbps

    # Audio metrics
    audio_level = models.FloatField(null=True, blank=True)
    audio_quality_score = models.FloatField(null=True, blank=True)

    # Video metrics
    video_resolution = models.CharField(max_length=20, blank=True, null=True)  # e.g., "1280x720"
    video_framerate = models.FloatField(null=True, blank=True)
    video_quality_score = models.FloatField(null=True, blank=True)

    recorded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'call_quality_metrics'
        indexes = [
            models.Index(fields=['call', 'recorded_at']),
            models.Index(fields=['user', 'recorded_at']),
        ]
