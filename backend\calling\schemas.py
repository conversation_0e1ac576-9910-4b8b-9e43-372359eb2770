# backend/calling/schemas.py
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
import uuid
from enum import Enum

# Import the camelCase alias generator from messaging
from pydantic.alias_generators import to_camel


class CallType(str, Enum):
    AUDIO = "audio"
    VIDEO = "video"


class CallStatus(str, Enum):
    INITIATED = "initiated"
    RINGING = "ringing"
    ANSWERED = "answered"
    ACTIVE = "active"
    ENDED = "ended"
    MISSED = "missed"
    DECLINED = "declined"
    FAILED = "failed"


class UserBasic(BaseModel):
    """Basic user info for call responses"""
    model_config = ConfigDict(
        from_attributes=True,
        alias_generator=to_camel,
        populate_by_name=True,
        json_encoders={
            uuid.UUID: str,
            datetime: lambda v: v.isoformat()
        }
    )
    
    id: uuid.UUID
    username: str
    first_name: str
    last_name: str
    profile_picture: Optional[str] = None


class CallInitiateRequest(BaseModel):
    """Request schema for initiating a call"""
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True
    )
    
    conversation_id: uuid.UUID
    call_type: CallType = CallType.AUDIO


class CallSDPUpdateRequest(BaseModel):
    """Request schema for SDP updates"""
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True
    )
    
    type: str = Field(..., pattern="^(offer|answer)$")
    sdp: str


class CallQualityReportRequest(BaseModel):
    """Request schema for call quality reporting"""
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True
    )
    
    packet_loss: Optional[float] = None
    jitter: Optional[float] = None
    round_trip_time: Optional[float] = None
    bandwidth_upload: Optional[int] = None
    bandwidth_download: Optional[int] = None
    audio_level: Optional[float] = None
    audio_quality_score: Optional[float] = None
    video_resolution: Optional[str] = None
    video_framerate: Optional[float] = None
    video_quality_score: Optional[float] = None


class CallEventResponse(BaseModel):
    """Response schema for call events"""
    model_config = ConfigDict(
        from_attributes=True,
        alias_generator=to_camel,
        populate_by_name=True,
        json_encoders={
            uuid.UUID: str,
            datetime: lambda v: v.isoformat()
        }
    )
    
    id: uuid.UUID
    event_type: str
    user: UserBasic
    event_data: Optional[Dict[str, Any]] = None
    timestamp: datetime


class CallQualityMetricResponse(BaseModel):
    """Response schema for call quality metrics"""
    model_config = ConfigDict(
        from_attributes=True,
        alias_generator=to_camel,
        populate_by_name=True,
        json_encoders={
            uuid.UUID: str,
            datetime: lambda v: v.isoformat()
        }
    )
    
    id: uuid.UUID
    user: UserBasic
    packet_loss: Optional[float] = None
    jitter: Optional[float] = None
    round_trip_time: Optional[float] = None
    bandwidth_upload: Optional[int] = None
    bandwidth_download: Optional[int] = None
    audio_level: Optional[float] = None
    audio_quality_score: Optional[float] = None
    video_resolution: Optional[str] = None
    video_framerate: Optional[float] = None
    video_quality_score: Optional[float] = None
    recorded_at: datetime


class CallParticipantResponse(BaseModel):
    """Response schema for call participants"""
    model_config = ConfigDict(
        from_attributes=True,
        alias_generator=to_camel,
        populate_by_name=True,
        json_encoders={
            uuid.UUID: str,
            datetime: lambda v: v.isoformat()
        }
    )
    
    id: uuid.UUID
    user: UserBasic
    status: str
    joined_at: Optional[datetime] = None
    left_at: Optional[datetime] = None
    audio_enabled: bool = True
    video_enabled: bool = True
    screen_sharing: bool = False


class CallResponse(BaseModel):
    """Response schema for call details"""
    model_config = ConfigDict(
        from_attributes=True,
        alias_generator=to_camel,
        populate_by_name=True,
        json_encoders={
            uuid.UUID: str,
            datetime: lambda v: v.isoformat()
        }
    )
    
    id: uuid.UUID
    conversation: uuid.UUID  # This will be converted to conversationId in response
    caller: UserBasic
    callee: UserBasic
    call_type: CallType
    status: CallStatus
    initiated_at: datetime
    answered_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    duration: Optional[str] = None  # Duration as string representation
    session_id: str
    quality_rating: Optional[int] = None
    quality_issues: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    participants: Optional[List[CallParticipantResponse]] = None
    events: Optional[List[CallEventResponse]] = None
    quality_metrics: Optional[List[CallQualityMetricResponse]] = None


class IncomingCallNotification(BaseModel):
    """Schema for incoming call notifications"""
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        json_encoders={
            uuid.UUID: str,
            datetime: lambda v: v.isoformat()
        }
    )
    
    call_id: uuid.UUID
    caller: UserBasic
    call_type: CallType
    conversation_id: uuid.UUID
    timestamp: datetime


class CallStatusNotification(BaseModel):
    """Schema for call status change notifications"""
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        json_encoders={
            uuid.UUID: str,
            datetime: lambda v: v.isoformat()
        }
    )
    
    call_id: uuid.UUID
    status: CallStatus
    timestamp: datetime
    user_id: Optional[uuid.UUID] = None  # For events like 'ended_by'