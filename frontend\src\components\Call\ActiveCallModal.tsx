// frontend/src/components/Call/ActiveCallModal.tsx
import React, { useRef, useEffect, useState } from 'react';
import { 
  Phone, 
  PhoneOff, 
  Mic, 
  MicOff, 
  Video, 
  VideoOff, 
  User,
  Minimize2,
  Maximize2,
  Settings
} from 'lucide-react';
import { useCalling } from '../../contexts/CallingContext';

interface ActiveCallModalProps {
  isOpen: boolean;
}

export const ActiveCallModal: React.FC<ActiveCallModalProps> = ({ isOpen }) => {
  const { activeCall, endCall, toggleAudio, toggleVideo } = useCalling();
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const [isMinimized, setIsMinimized] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [controlsTimeout, setControlsTimeout] = useState<NodeJS.Timeout | null>(null);

  // Set up video streams
  useEffect(() => {
    if (activeCall?.localStream && localVideoRef.current) {
      localVideoRef.current.srcObject = activeCall.localStream;
    }
  }, [activeCall?.localStream]);

  useEffect(() => {
    if (activeCall?.remoteStream && remoteVideoRef.current) {
      remoteVideoRef.current.srcObject = activeCall.remoteStream;
    }
  }, [activeCall?.remoteStream]);

  // Auto-hide controls for video calls
  useEffect(() => {
    if (activeCall?.type === 'video' && showControls) {
      if (controlsTimeout) {
        clearTimeout(controlsTimeout);
      }
      
      const timeout = setTimeout(() => {
        setShowControls(false);
      }, 3000);
      
      setControlsTimeout(timeout);
    }

    return () => {
      if (controlsTimeout) {
        clearTimeout(controlsTimeout);
      }
    };
  }, [showControls, activeCall?.type]);

  if (!isOpen || !activeCall || activeCall.status === 'idle') {
    return null;
  }

  const handleEndCall = () => {
    endCall();
  };

  const handleToggleAudio = () => {
    toggleAudio();
  };

  const handleToggleVideo = () => {
    toggleVideo();
  };

  const handleMouseMove = () => {
    if (activeCall.type === 'video') {
      setShowControls(true);
    }
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getDisplayName = () => {
    const participant = activeCall.isIncoming ? activeCall.caller : activeCall.callee;
    const { firstName, lastName, username } = participant;
    if (firstName || lastName) {
      return `${firstName || ''} ${lastName || ''}`.trim();
    }
    return username;
  };

  const getConnectionStatus = () => {
    switch (activeCall.status) {
      case 'initiating':
        return 'Calling...';
      case 'ringing':
        return 'Ringing...';
      case 'connecting':
        return 'Connecting...';
      case 'active':
        return activeCall.duration ? formatDuration(activeCall.duration) : 'Connected';
      default:
        return '';
    }
  };

  // Minimized view
  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-4 z-50 min-w-[280px]">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
              <User className="w-6 h-6 text-gray-600" />
            </div>
            <div>
              <p className="font-medium text-sm">{getDisplayName()}</p>
              <p className="text-xs text-gray-500">{getConnectionStatus()}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleToggleAudio}
              className={`p-2 rounded-full ${
                activeCall.isAudioEnabled 
                  ? 'bg-gray-100 text-gray-700' 
                  : 'bg-red-100 text-red-600'
              }`}
            >
              {activeCall.isAudioEnabled ? (
                <Mic className="w-4 h-4" />
              ) : (
                <MicOff className="w-4 h-4" />
              )}
            </button>
            
            <button
              onClick={handleEndCall}
              className="p-2 rounded-full bg-red-500 text-white hover:bg-red-600"
            >
              <PhoneOff className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => setIsMinimized(false)}
              className="p-2 rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200"
            >
              <Maximize2 className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Full call view
  return (
    <div 
      className="fixed inset-0 bg-black z-50 flex flex-col"
      onMouseMove={handleMouseMove}
    >
      {/* Video call layout */}
      {activeCall.type === 'video' ? (
        <>
          {/* Remote video (main) */}
          <div className="flex-1 relative">
            {activeCall.remoteStream ? (
              <video
                ref={remoteVideoRef}
                autoPlay
                playsInline
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gray-900 flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-gray-700 flex items-center justify-center">
                    <User className="w-12 h-12" />
                  </div>
                  <p className="text-lg">{getDisplayName()}</p>
                  <p className="text-sm text-gray-400">{getConnectionStatus()}</p>
                </div>
              </div>
            )}

            {/* Local video (picture-in-picture) */}
            <div className="absolute top-4 right-4 w-32 h-24 bg-gray-800 rounded-lg overflow-hidden">
              {activeCall.localStream && activeCall.isVideoEnabled ? (
                <video
                  ref={localVideoRef}
                  autoPlay
                  playsInline
                  muted
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gray-700 flex items-center justify-center">
                  <User className="w-8 h-8 text-gray-400" />
                </div>
              )}
            </div>
          </div>

          {/* Controls overlay */}
          {showControls && (
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6">
              <div className="flex items-center justify-between text-white">
                <div>
                  <p className="font-medium">{getDisplayName()}</p>
                  <p className="text-sm text-gray-300">{getConnectionStatus()}</p>
                </div>
                
                <div className="flex items-center space-x-4">
                  <button
                    onClick={handleToggleAudio}
                    className={`p-3 rounded-full ${
                      activeCall.isAudioEnabled 
                        ? 'bg-gray-700 hover:bg-gray-600' 
                        : 'bg-red-600 hover:bg-red-700'
                    }`}
                  >
                    {activeCall.isAudioEnabled ? (
                      <Mic className="w-6 h-6" />
                    ) : (
                      <MicOff className="w-6 h-6" />
                    )}
                  </button>
                  
                  <button
                    onClick={handleToggleVideo}
                    className={`p-3 rounded-full ${
                      activeCall.isVideoEnabled 
                        ? 'bg-gray-700 hover:bg-gray-600' 
                        : 'bg-red-600 hover:bg-red-700'
                    }`}
                  >
                    {activeCall.isVideoEnabled ? (
                      <Video className="w-6 h-6" />
                    ) : (
                      <VideoOff className="w-6 h-6" />
                    )}
                  </button>
                  
                  <button
                    onClick={handleEndCall}
                    className="p-3 rounded-full bg-red-600 hover:bg-red-700"
                  >
                    <PhoneOff className="w-6 h-6" />
                  </button>
                  
                  <button
                    onClick={() => setIsMinimized(true)}
                    className="p-3 rounded-full bg-gray-700 hover:bg-gray-600"
                  >
                    <Minimize2 className="w-6 h-6" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </>
      ) : (
        /* Audio call layout */
        <div className="flex-1 bg-gradient-to-br from-blue-900 to-purple-900 flex flex-col items-center justify-center text-white">
          {/* Caller info */}
          <div className="text-center mb-8">
            <div className="w-32 h-32 mx-auto mb-6 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
              <User className="w-16 h-16" />
            </div>
            <h2 className="text-2xl font-semibold mb-2">{getDisplayName()}</h2>
            <p className="text-lg text-blue-200">{getConnectionStatus()}</p>
          </div>

          {/* Audio controls */}
          <div className="flex items-center space-x-6">
            <button
              onClick={handleToggleAudio}
              className={`p-4 rounded-full ${
                activeCall.isAudioEnabled 
                  ? 'bg-white bg-opacity-20 hover:bg-opacity-30' 
                  : 'bg-red-600 hover:bg-red-700'
              }`}
            >
              {activeCall.isAudioEnabled ? (
                <Mic className="w-8 h-8" />
              ) : (
                <MicOff className="w-8 h-8" />
              )}
            </button>
            
            <button
              onClick={handleEndCall}
              className="p-4 rounded-full bg-red-600 hover:bg-red-700"
            >
              <PhoneOff className="w-8 h-8" />
            </button>
            
            <button
              onClick={() => setIsMinimized(true)}
              className="p-4 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30"
            >
              <Minimize2 className="w-8 h-8" />
            </button>
          </div>

          {/* Connection quality indicator */}
          {activeCall.qualityMetrics && (
            <div className="mt-8 text-center">
              <p className="text-sm text-blue-200">
                Connection: {activeCall.connectionState}
              </p>
              {activeCall.qualityMetrics.roundTripTime && (
                <p className="text-xs text-blue-300">
                  Latency: {Math.round(activeCall.qualityMetrics.roundTripTime)}ms
                </p>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
