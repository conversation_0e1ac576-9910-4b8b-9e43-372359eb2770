// frontend/src/components/Chat/MessageList.tsx
import React, { useEffect, useRef, useMemo, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { Icon } from '../ui/Icon';
import { useSocket } from '../../contexts/SocketContext';
import { useGetMessagesQuery } from '../../services/messageApi';
import MessageItem from './MessageItem';
import {
  selectSortedMessagesByConversation,
  selectSendingMessages,
  selectMessageStatuses,
  selectFailedMessages,
  selectTypingUsersByConversation,
  type RootState
} from '../../store';
import type { Message } from '../../services/messageApi';

interface MessageListProps {
  conversationId: string;
  currentUserId: string;
  onReply?: (message: Message) => void;
}

const MessageList: React.FC<MessageListProps> = ({
  conversationId,
  currentUserId,
  onReply
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Check if this is a draft conversation
  const isDraftConversation = conversationId?.startsWith('draft-');

  // Use RTK Query to get messages - skip for draft conversations
  const { data: messagesData, isLoading, error } = useGetMessagesQuery(
    { conversationId },
    { skip: !conversationId || isDraftConversation }
  );

  // Use memoized selectors for Redux store data (real-time features)
  const sendingMessages = useSelector(selectSendingMessages);
  const messageStatuses = useSelector(selectMessageStatuses);
  const failedMessages = useSelector(selectFailedMessages);
  const reduxMessages = useSelector((state: RootState) =>
    selectSortedMessagesByConversation(state, conversationId)
  );
  const typingUsers = useSelector((state: RootState) =>
    selectTypingUsersByConversation(state, conversationId)
  );

  const { retryFailedMessage, markMessageAsRead } = useSocket();

  const messages = useMemo(() => {
    // For draft conversations, use Redux store messages (includes optimistic updates)
    // reduxMessages is already sorted by the memoized selector
    if (isDraftConversation) {
      return reduxMessages;
    }

    // For real conversations, use RTK Query data
    if (!messagesData?.results) return [];
    return [...messagesData.results].sort((a, b) =>
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
  }, [messagesData, reduxMessages, isDraftConversation]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Mark messages as read when they come into view
  const handleMessageVisible = useCallback((messageId: string, senderId: string) => {
    // Only mark as read if it's not our own message and not already read
    if (senderId !== currentUserId && messageStatuses[messageId] !== 'READ') {
      markMessageAsRead(messageId);
    }
  }, [currentUserId, messageStatuses, markMessageAsRead]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Set up intersection observer for read receipts
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const messageElement = entry.target as HTMLElement;
            const messageId = messageElement.dataset.messageId;
            const senderId = messageElement.dataset.senderId;

            if (messageId && senderId) {
              handleMessageVisible(messageId, senderId);
            }
          }
        });
      },
      {
        threshold: 0.5, // Message is considered "read" when 50% visible
        rootMargin: '0px'
      }
    );

    // Observe all message elements
    const messageElements = document.querySelectorAll('[data-message-id]');
    messageElements.forEach((element) => observer.observe(element));

    return () => {
      observer.disconnect();
    };
  }, [messages, handleMessageVisible]);



  const isMessageSending = (messageId: string) => {
    return sendingMessages[messageId] || false;
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center text-gray-500">
          <Icon name="loader" size={48} className="mx-auto mb-4 text-gray-300 animate-spin" />
          <p className="text-lg font-medium mb-2">Loading messages...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center text-red-500">
          <Icon name="alert" size={48} className="mx-auto mb-4 text-red-300" />
          <p className="text-lg font-medium mb-2">Failed to load messages</p>
          <p className="text-sm">Please try refreshing the page</p>
        </div>
      </div>
    );
  }

  // Empty state
  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center text-gray-500">
          <Icon name="message-circle" size={48} className="mx-auto mb-4 text-gray-300" />
          <p className="text-lg font-medium mb-2">No messages yet</p>
          <p className="text-sm">Start the conversation by sending a message</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-auto p-4 space-y-4" data-testid="message-list">
      
      {messages.map((message) => {
        const isOwnMessage = message.sender.id === currentUserId;
        const isSending = isMessageSending(message.id);

        return (
          <MessageItem
            key={message.id}
            message={message}
            currentUserId={currentUserId}
            isOwnMessage={isOwnMessage}
            isSending={isSending}
            messageStatus={messageStatuses[message.id]}
            isFailed={failedMessages[message.id] || false}
            mediaFiles={message.media_files || []}
            conversationId={conversationId}
            onReply={onReply}
            onRetry={failedMessages[message.id] ? () => {
              retryFailedMessage(message.id, conversationId, message.content || '', message.messageType);
            } : undefined}
          />
        );
      })}

      {/* Typing indicators */}
      {typingUsers.length > 0 && (
        <div className="flex justify-start">
          <div className="bg-gray-100 rounded-lg px-4 py-2 text-gray-600 text-sm">
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span>
                {typingUsers.length === 1 ? 'Someone is' : `${typingUsers.length} people are`} typing...
              </span>
            </div>
          </div>
        </div>
      )}

      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
