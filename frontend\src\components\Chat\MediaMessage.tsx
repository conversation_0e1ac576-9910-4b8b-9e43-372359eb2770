// frontend/src/components/Chat/MediaMessage.tsx
import React, { useState, useCallback } from 'react';
import { Download, Eye, File, Image, Video, Music, Archive, FileText, Loader2 } from 'lucide-react';
import { mediaApi, type MediaFile } from '../../services/mediaApi';
import { useSocket } from '../../contexts/SocketContext';

interface MediaMessageProps {
  mediaFile: MediaFile;
  conversationId: string;
  isOwn?: boolean;
  className?: string;
}

interface DownloadState {
  downloading: boolean;
  progress: number;
  error?: string;
}

export const MediaMessage: React.FC<MediaMessageProps> = ({
  mediaFile,
  conversationId,
  isOwn = false,
  className = ''
}) => {
  const [downloadState, setDownloadState] = useState<DownloadState>({
    downloading: false,
    progress: 0
  });
  const [thumbnail, setThumbnail] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const { socket } = useSocket();

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'image': return <Image className="w-6 h-6 text-blue-500" />;
      case 'video': return <Video className="w-6 h-6 text-purple-500" />;
      case 'audio': return <Music className="w-6 h-6 text-green-500" />;
      case 'archive': return <Archive className="w-6 h-6 text-orange-500" />;
      case 'document': return <FileText className="w-6 h-6 text-red-500" />;
      default: return <File className="w-6 h-6 text-gray-500" />;
    }
  };

  const loadThumbnail = useCallback(async () => {
    if (!mediaFile.has_thumbnail || thumbnail) return;

    try {
      const thumbnailData = await mediaApi.getThumbnail(mediaFile.id);
      
      // Decrypt thumbnail (placeholder - integrate with encryption context)
      // For now, just decode the base64
      const thumbnailBlob = new Blob([
        Uint8Array.from(atob(thumbnailData.encrypted_thumbnail), c => c.charCodeAt(0))
      ], { type: 'image/jpeg' });
      
      const thumbnailUrl = URL.createObjectURL(thumbnailBlob);
      setThumbnail(thumbnailUrl);
    } catch (error) {
      console.error('Failed to load thumbnail:', error);
    }
  }, [mediaFile.id, mediaFile.has_thumbnail, thumbnail]);

  const downloadFile = useCallback(async () => {
    if (downloadState.downloading) return;

    try {
      setDownloadState({ downloading: true, progress: 0 });

      // Notify socket that download started
      socket?.emit('media_download_started', {
        conversationId,
        mediaFileId: mediaFile.id,
        fileName: mediaFile.original_filename
      });

      // Get download token
      const downloadData = await mediaApi.getDownloadToken(mediaFile.id);
      
      // Extract token from URL
      const token = downloadData.download_url.split('/').slice(-2, -1)[0];
      
      // Download the encrypted file
      const encryptedBlob = await mediaApi.downloadFile(token);
      
      // Decrypt file (placeholder - integrate with encryption context)
      // For now, just download the encrypted file
      const url = URL.createObjectURL(encryptedBlob);
      
      // Create download link
      const link = document.createElement('a');
      link.href = url;
      link.download = mediaFile.original_filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up
      URL.revokeObjectURL(url);
      
      setDownloadState({ downloading: false, progress: 100 });
      
      // Reset progress after a delay
      setTimeout(() => {
        setDownloadState({ downloading: false, progress: 0 });
      }, 2000);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Download failed';
      setDownloadState({ 
        downloading: false, 
        progress: 0, 
        error: errorMessage 
      });
      
      // Clear error after a delay
      setTimeout(() => {
        setDownloadState({ downloading: false, progress: 0 });
      }, 3000);
    }
  }, [downloadState.downloading, socket, conversationId, mediaFile]);

  const openPreview = useCallback(() => {
    if (mediaFile.is_image || mediaFile.is_video) {
      setShowPreview(true);
      loadThumbnail();
    }
  }, [mediaFile.is_image, mediaFile.is_video, loadThumbnail]);

  // Load thumbnail on mount for images
  React.useEffect(() => {
    if (mediaFile.is_image && mediaFile.has_thumbnail) {
      loadThumbnail();
    }
  }, [mediaFile.is_image, mediaFile.has_thumbnail, loadThumbnail]);

  return (
    <>
      <div className={`media-message ${className}`}>
        <div className={`
          max-w-sm rounded-lg border p-3 
          ${isOwn 
            ? 'bg-blue-500 text-white border-blue-600' 
            : 'bg-white text-gray-900 border-gray-200'
          }
        `}>
          {/* Media preview area */}
          <div className="mb-3">
            {mediaFile.is_image && thumbnail ? (
              <div 
                className="relative cursor-pointer group"
                onClick={openPreview}
              >
                <img 
                  src={thumbnail} 
                  alt={mediaFile.original_filename}
                  className="w-full h-32 object-cover rounded"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded flex items-center justify-center">
                  <Eye className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-16 bg-gray-100 rounded">
                {getFileIcon(mediaFile.file_type)}
              </div>
            )}
          </div>

          {/* File info */}
          <div className="space-y-2">
            <div>
              <p className={`text-sm font-medium truncate ${
                isOwn ? 'text-white' : 'text-gray-900'
              }`}>
                {mediaFile.original_filename}
              </p>
              <p className={`text-xs ${
                isOwn ? 'text-blue-100' : 'text-gray-500'
              }`}>
                {mediaApi.formatFileSize(mediaFile.file_size)} • {mediaFile.file_type}
              </p>
            </div>

            {/* Processing status */}
            {mediaFile.processing_status === 'processing' && (
              <div className={`text-xs flex items-center space-x-1 ${
                isOwn ? 'text-blue-100' : 'text-gray-500'
              }`}>
                <Loader2 className="w-3 h-3 animate-spin" />
                <span>Processing...</span>
              </div>
            )}

            {/* Download section */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {/* Preview button for images/videos */}
                {(mediaFile.is_image || mediaFile.is_video) && (
                  <button
                    onClick={openPreview}
                    className={`text-xs px-2 py-1 rounded transition-colors ${
                      isOwn 
                        ? 'text-blue-100 hover:text-white hover:bg-blue-600' 
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <Eye className="w-3 h-3 inline mr-1" />
                    Preview
                  </button>
                )}

                {/* Download button */}
                <button
                  onClick={downloadFile}
                  disabled={downloadState.downloading}
                  className={`text-xs px-2 py-1 rounded transition-colors flex items-center space-x-1 ${
                    isOwn 
                      ? 'text-blue-100 hover:text-white hover:bg-blue-600 disabled:text-blue-200' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 disabled:text-gray-400'
                  }`}
                >
                  {downloadState.downloading ? (
                    <Loader2 className="w-3 h-3 animate-spin" />
                  ) : (
                    <Download className="w-3 h-3" />
                  )}
                  <span>
                    {downloadState.downloading ? 'Downloading...' : 'Download'}
                  </span>
                </button>
              </div>

              {/* Download progress */}
              {downloadState.downloading && downloadState.progress > 0 && (
                <div className="text-xs">
                  {downloadState.progress}%
                </div>
              )}
            </div>

            {/* Error message */}
            {downloadState.error && (
              <p className="text-xs text-red-400">
                {downloadState.error}
              </p>
            )}
          </div>
        </div>

        {/* Upload timestamp */}
        <p className={`text-xs mt-1 ${
          isOwn ? 'text-right text-gray-400' : 'text-left text-gray-500'
        }`}>
          {new Date(mediaFile.created_at).toLocaleTimeString()}
        </p>
      </div>

      {/* Preview modal */}
      {showPreview && (mediaFile.is_image || mediaFile.is_video) && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
          onClick={() => setShowPreview(false)}
        >
          <div className="max-w-4xl max-h-4xl p-4">
            {mediaFile.is_image && thumbnail ? (
              <img 
                src={thumbnail} 
                alt={mediaFile.original_filename}
                className="max-w-full max-h-full object-contain"
                onClick={(e) => e.stopPropagation()}
              />
            ) : (
              <div className="bg-white rounded-lg p-8 text-center">
                <p className="text-gray-600">Preview not available</p>
                <button
                  onClick={downloadFile}
                  className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  Download to view
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default MediaMessage;
