// frontend/src/services/index.ts
/**
 * Central export file for all API services and hooks
 * This provides a clean interface for importing RTK Query hooks throughout the app
 */

// Main API service (import this first to avoid circular dependencies)
export { api } from './api';

// Import and re-export API services after the main api is defined
// This ensures proper initialization order

// Authentication API
export {
  authApi,
  useLoginMutation,
  useRegisterMutation,
  useLogoutMutation,
  useGetCurrentUserQuery,
  useLazyGetCurrentUserQuery,
} from './authApi';

// Message API
export {
  messageApi,
  useGetMessagesQuery,
  useLazyGetMessagesQuery,
  useSendMessageMutation,
  useAddMessageToCacheMutation,
} from './messageApi';

// Conversation API
export {
  conversationApi,
  useGetConversationsQuery,
  useLazyGetConversationsQuery,
  useCreateConversationMutation,
  useUpdateConversationInCacheMutation,
  useUpdateConversationLastMessageMutation,
  useGetConversationQuery,
  useLazyGetConversationQuery,
  useUpdateGroupInfoMutation,
  useAddGroupMemberMutation,
  useRemoveGroupMemberMutation,
  useLeaveGroupMutation,
} from './conversationApi';

// User API
export {
  userApi,
  useSearchUsersQuery,
  useLazySearchUsersQuery,
  useGetUserProfileQuery,
  useLazyGetUserProfileQuery,
  useUpdateUserProfileMutation,
} from './userApi';

// Encryption API
export {
  encryptionApi,
  useUploadKeyBundleMutation,
  useGetKeyBundleQuery,
  useLazyGetKeyBundleQuery,
  useUploadOneTimePreKeysMutation,
  useGetPreKeyCountQuery,
  useLazyGetPreKeyCountQuery,
  updatePreKeyCountCache,
  invalidateKeyBundleCache,
  prefetchKeyBundle,
  isRateLimitError,
  isValidationError,
  isAuthError,
  getErrorMessage,
  shouldRetry,
  getRetryDelay,
  isSuccessResponse,
  isErrorResponse,
} from './encryptionApi';

// Calling API
export {
  callingApi,
  useInitiateCallMutation,
  useAnswerCallMutation,
  useDeclineCallMutation,
  useEndCallMutation,
  useUpdateCallSDPMutation,
  useReportCallQualityMutation,
  useGetCallHistoryQuery,
  useGetCallDetailQuery,
} from './callingApi';

// Cache utilities
export {
  invalidateConversations,
  invalidateMessages,
  invalidateUserSearch,
  addMessageToCache,
  updateConversationInCache,
  updateConversationLastMessage,
  addOptimisticMessage,
  replaceOptimisticMessage,
  removeOptimisticMessage,
  prefetchMessages,
  prefetchConversation,
  clearAllCache,
  clearConversationCache,
} from './cacheUtils';

// Type exports
export type { Message, SendMessageRequest, FetchMessagesRequest } from './messageApi';
export type { Conversation, CreateConversationRequest } from './conversationApi';
export type { SearchUser } from './userApi';
export type {
  KeyBundleUpload,
  KeyBundleResponse,
  OneTimePreKeyBatch,
  PreKeyCountResponse,
  EncryptionApiResponse
} from '../types/encryption';
export type {
  Call,
  CallEvent,
  CallQualityMetric,
  InitiateCallRequest,
  UpdateSDPRequest,
  CallQualityReportRequest
} from './callingApi';
