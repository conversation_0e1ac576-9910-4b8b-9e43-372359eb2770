import axios from 'axios';

export interface TestUser {
  id?: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  username: string;
}

export interface TestConversation {
  id?: string;
  type: 'DIRECT' | 'GROUP';
  name?: string;
  participantIds: string[];
}

export class TestDataManager {
  private baseURL = 'http://localhost:6000/api';
  private testUsers: Map<string, TestUser> = new Map();
  private testConversations: Map<string, TestConversation> = new Map();
  private authTokens: Map<string, string> = new Map();

  constructor() {
    this.initializeTestUsers();
  }

  private initializeTestUsers() {
    // Define test users for different scenarios - using the specific users mentioned
    this.testUsers.set('alice', {
      email: '<EMAIL>',
      password: 'testpass123',
      firstName: 'Alice',
      lastName: 'Smith',
      username: 'alice'
    });

    this.testUsers.set('harry', {
      email: '<EMAIL>',
      password: 'testpass123',
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      username: 'harry'
    });

    this.testUsers.set('bob', {
      email: '<EMAIL>',
      password: 'testpass123',
      firstName: '<PERSON>',
      lastName: 'Johnson',
      username: 'bob'
    });

    this.testUsers.set('charlie', {
      email: '<EMAIL>',
      password: 'testpass123',
      firstName: 'Charlie',
      lastName: 'Brown',
      username: 'charlie'
    });

    // Keep legacy mappings for backward compatibility
    this.testUsers.set('primary', {
      email: '<EMAIL>',
      password: 'testpass123',
      firstName: 'Alice',
      lastName: 'Smith',
      username: 'alice'
    });

    this.testUsers.set('secondary', {
      email: '<EMAIL>',
      password: 'testpass123',
      firstName: 'Harry',
      lastName: 'Potter',
      username: 'harry'
    });

    this.testUsers.set('tertiary', {
      email: '<EMAIL>',
      password: 'testpass123',
      firstName: 'Charlie',
      lastName: 'Brown',
      username: 'charlie'
    });
  }

  async setupTestData() {
    console.log('📊 Setting up test data...');

    try {
      // Create test users
      for (const [key, userData] of this.testUsers) {
        await this.createTestUser(key, userData);
      }

      // Create test conversations
      await this.createTestConversations();

      console.log('✅ Test data setup completed');
    } catch (error) {
      console.error('❌ Failed to setup test data:', error);
      throw error;
    }
  }

  async createTestUser(key: string, userData: TestUser): Promise<TestUser> {
    try {
      // Try to create the user
      const response = await axios.post(`${this.baseURL}/auth/register/`, {
        email: userData.email,
        password: userData.password,
        first_name: userData.firstName,
        last_name: userData.lastName,
        username: userData.username
      });

      if (response.data.success) {
        const createdUser = response.data.data.user;
        userData.id = createdUser.id;
        
        // Store auth token
        this.authTokens.set(key, response.data.data.tokens.access);
        
        console.log(`✅ Created test user: ${userData.email}`);
        return userData;
      }
    } catch (error: any) {
      // User might already exist, try to login
      if (error.response?.status === 400) {
        console.log(`ℹ️  Test user already exists, logging in: ${userData.email}`);
        return await this.loginTestUser(key, userData);
      }
      throw error;
    }

    throw new Error(`Failed to create test user: ${userData.email}`);
  }

  async loginTestUser(key: string, userData: TestUser): Promise<TestUser> {
    try {
      const response = await axios.post(`${this.baseURL}/auth/login/`, {
        email: userData.email,
        password: userData.password
      });

      if (response.data.success) {
        const user = response.data.data.user;
        userData.id = user.id;
        
        // Store auth token
        this.authTokens.set(key, response.data.data.tokens.access);
        
        console.log(`✅ Logged in test user: ${userData.email}`);
        return userData;
      }
    } catch (error) {
      console.error(`❌ Failed to login test user: ${userData.email}`, error);
      throw error;
    }

    throw new Error(`Failed to login test user: ${userData.email}`);
  }

  async createTestConversations() {
    console.log('💬 Creating test conversations...');

    try {
      const primaryUser = this.testUsers.get('primary')!;
      const secondaryUser = this.testUsers.get('secondary')!;
      const tertiaryUser = this.testUsers.get('tertiary')!;
      const primaryToken = this.authTokens.get('primary')!;

      // Create direct conversation
      const directConversation = await this.createConversation(
        primaryToken,
        {
          type: 'DIRECT',
          participantIds: [secondaryUser.id!]
        }
      );
      this.testConversations.set('direct', directConversation);

      // Create group conversation
      const groupConversation = await this.createConversation(
        primaryToken,
        {
          type: 'GROUP',
          name: 'Test Group Chat',
          participantIds: [secondaryUser.id!, tertiaryUser.id!]
        }
      );
      this.testConversations.set('group', groupConversation);

      console.log('✅ Test conversations created');
    } catch (error) {
      console.error('❌ Failed to create test conversations:', error);
      // Don't throw error as conversations might already exist
    }
  }

  async createConversation(token: string, conversationData: TestConversation): Promise<TestConversation> {
    try {
      const response = await axios.post(
        `${this.baseURL}/messaging/conversations/create/`,
        {
          type: conversationData.type,
          name: conversationData.name,
          participant_ids: conversationData.participantIds
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      conversationData.id = response.data.id;
      return conversationData;
    } catch (error) {
      console.error('❌ Failed to create conversation:', error);
      throw error;
    }
  }

  async cleanupTestData() {
    console.log('🧹 Cleaning up test data...');

    try {
      // In a real scenario, you might want to delete test users and conversations
      // For now, we'll just clear our local data
      this.testUsers.clear();
      this.testConversations.clear();
      this.authTokens.clear();

      console.log('✅ Test data cleanup completed');
    } catch (error) {
      console.error('❌ Failed to cleanup test data:', error);
    }
  }

  getTestUser(key: string): TestUser {
    const user = this.testUsers.get(key);
    if (!user) {
      throw new Error(`Test user not found: ${key}`);
    }
    return user;
  }

  getTestConversation(key: string): TestConversation {
    const conversation = this.testConversations.get(key);
    if (!conversation) {
      throw new Error(`Test conversation not found: ${key}`);
    }
    return conversation;
  }

  getAuthToken(userKey: string): string {
    const token = this.authTokens.get(userKey);
    if (!token) {
      throw new Error(`Auth token not found for user: ${userKey}`);
    }
    return token;
  }

  getAllTestUsers(): Map<string, TestUser> {
    return new Map(this.testUsers);
  }

  getAllTestConversations(): Map<string, TestConversation> {
    return new Map(this.testConversations);
  }
}
