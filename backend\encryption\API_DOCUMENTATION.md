# Phase 3 Encryption API Documentation

## Overview

The Phase 3 Encryption API provides secure endpoints for end-to-end encryption key management. All endpoints require JWT authentication and implement comprehensive security measures including signature verification, rate limiting, and atomic operations.

**Base URL**: `http://127.0.0.1:8000/api/encryption/`

## Authentication

All endpoints require JWT authentication. Include the access token in the Authorization header:

```bash
Authorization: Bearer <your_jwt_access_token>
```

To obtain a JWT token, use the existing authentication endpoints:

```bash
# Login to get JWT tokens
curl -X POST http://127.0.0.1:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_password"
  }'
```

## Endpoints

### 1. Upload Key Bundle

Upload a user's public key bundle with signature verification.

**Endpoint**: `POST /api/encryption/bundles/`

**Security Features**:
- Rate limited: 10 uploads per hour per user
- Server-side signature verification over raw SPKI bytes
- Atomic database operations
- Security event logging

**Request Body**:
```json
{
  "identity_public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE...",
  "signed_prekey_id": 1,
  "signed_prekey_public": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE...",
  "signed_prekey_signature": "MEUCIQDxyz..."
}
```

**Field Descriptions**:
- `identity_public_key`: ECDSA P-256 identity public key (SPKI format, base64 encoded)
- `signed_prekey_id`: Signed pre-key identifier (integer)
- `signed_prekey_public`: ECDH P-256 signed pre-key public key (SPKI format, base64 encoded)
- `signed_prekey_signature`: ECDSA signature over signed pre-key SPKI bytes (base64 encoded)

**Success Response** (201 Created):
```json
{
  "key_bundle_id": "46014ecf-8864-4f88-a96b-3032a3e3eccd",
  "message": "Key bundle uploaded successfully"
}
```

**Error Responses**:

*Invalid Signature (400 Bad Request)*:
```json
{
  "error": "Invalid signed pre-key signature",
  "code": "SIGNATURE_INVALID"
}
```

*Rate Limited (429 Too Many Requests)*:
```json
{
  "error": "Rate limit exceeded. Please try again later."
}
```

**Working cURL Example**:
```bash
# Note: Replace <JWT_TOKEN> with actual token from authentication
curl -X POST http://127.0.0.1:8000/api/encryption/bundles/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -d '{
    "identity_public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEVkpOnGmFntib9F/I6tIQmWeEDO186HPnDEKbTtMfiqAslpRFuBc6vvenIetWqyYJu/hXt25NdcJFoVAk2yr6zw==",
    "signed_prekey_id": 1,
    "signed_prekey_public": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE9o1LVOhahkUcLCAZgtF/2YpK8V7brrv0j5Au+B95HJ4lyDWAPMZfoEYJFYyE+7PaFG+lxo2B/jBch0aeTlI3dQ==",
    "signed_prekey_signature": "MEUCIF4z5ltcLiOHEBq8ykORetLXOh4oD3yZjGtCKu8Rv/QSAiEAjK8Y4pOJYSGOPEX65j2XLK9IgkMjC7zRXMtUAvrUxEQ="
  }'
```

### 2. Get Key Bundle

Retrieve another user's key bundle for key exchange with atomic one-time pre-key consumption.

**Endpoint**: `GET /api/encryption/bundles/<user_id>/`

**Security Features**:
- Atomic one-time pre-key consumption using SELECT FOR UPDATE
- Prevents race conditions when multiple users request the same OPK
- Returns bundle + one unused OPK (if available)

**Path Parameters**:
- `user_id`: UUID of the target user

**Success Response** (200 OK):
```json
{
  "identity_public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE...",
  "signed_prekey": {
    "id": 1,
    "public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE...",
    "signature": "MEUCIQDxyz..."
  },
  "one_time_prekey": {
    "id": 42,
    "public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE..."
  }
}
```

**Note**: `one_time_prekey` field is optional and only included if an unused OPK is available.

**Error Response** (404 Not Found):
```json
{
  "error": "Key bundle not found for user",
  "code": "KEY_BUNDLE_NOT_FOUND"
}
```

**Working cURL Example**:
```bash
# Replace <USER_ID> with actual user UUID and <JWT_TOKEN> with actual token
curl -X GET http://127.0.0.1:8000/api/encryption/bundles/de7518b9-03b7-44c1-9e82-a20f8672cc55/ \
  -H "Authorization: Bearer <JWT_TOKEN>"
```

### 3. Upload One-Time Pre-Keys

Upload a batch of one-time pre-keys for perfect forward secrecy.

**Endpoint**: `POST /api/encryption/prekeys/`

**Security Features**:
- Rate limited: 50 batches per hour per user
- Key format validation for all keys
- Atomic batch operations
- Duplicate prevention

**Request Body**:
```json
{
  "prekeys": [
    {
      "key_id": 1,
      "public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE..."
    },
    {
      "key_id": 2,
      "public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE..."
    }
  ]
}
```

**Field Descriptions**:
- `prekeys`: Array of one-time pre-keys (1-100 keys per batch)
- `key_id`: Unique identifier for the key (per user)
- `public_key`: ECDH P-256 public key (SPKI format, base64 encoded)

**Success Response** (201 Created):
```json
{
  "message": "5 one-time pre-keys uploaded successfully",
  "count": 5
}
```

**Error Responses**:

*Duplicate Key ID (400 Bad Request)*:
```json
{
  "error": "Key ID 1 already exists",
  "code": "DUPLICATE_KEY_ID"
}
```

*Invalid Key Format (400 Bad Request)*:
```json
{
  "error": "Invalid ECDH public key format for key ID 1",
  "code": "KEY_FORMAT_INVALID"
}
```

**Working cURL Example**:
```bash
curl -X POST http://127.0.0.1:8000/api/encryption/prekeys/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -d '{
    "prekeys": [
      {
        "key_id": 10,
        "public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE5yrcY99xmEbnRvXvZriCLjc2SyoGxrGuE23HNMNU4bEqwdg0f3Tf7Clf4kmYzQ9gummdQszpsLOsE+JVZs4JKw=="
      },
      {
        "key_id": 11,
        "public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEyZ6+hgPsQd+SYQr3ftgZEeDI11y2c+kXfVUNgoOmSq8Rc+Y3bSFsNnQCzpUiYlAbiIlV7V/RFsolUfsny/yCSQ=="
      }
    ]
  }'
```

### 4. Get Pre-Key Count

Get count of available one-time pre-keys for the authenticated user.

**Endpoint**: `GET /api/encryption/prekeys/count/`

**Success Response** (200 OK):
```json
{
  "available_count": 5,
  "total_count": 10,
  "used_count": 5
}
```

**Field Descriptions**:
- `available_count`: Number of unused one-time pre-keys
- `total_count`: Total number of one-time pre-keys ever created
- `used_count`: Number of consumed one-time pre-keys

**Working cURL Example**:
```bash
curl -X GET http://127.0.0.1:8000/api/encryption/prekeys/count/ \
  -H "Authorization: Bearer <JWT_TOKEN>"
```

## Error Handling

### Common HTTP Status Codes

- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request data or failed validation
- **401 Unauthorized**: Authentication required or invalid token
- **404 Not Found**: Resource not found
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

### Error Response Format

All error responses follow this format:
```json
{
  "error": "Human-readable error message",
  "code": "ERROR_CODE",
  "details": {
    "field": "additional_context"
  }
}
```

### Common Error Codes

- `SIGNATURE_INVALID`: Signed pre-key signature verification failed
- `KEY_FORMAT_INVALID`: Public key format is invalid
- `KEY_BUNDLE_NOT_FOUND`: User's key bundle not found
- `DUPLICATE_KEY_ID`: One-time pre-key ID already exists
- `CRYPTO_ERROR`: General cryptographic operation error

## Security Considerations

### Server Security
- **Never stores private keys**: Only public keys and encrypted data
- **Signature verification**: All key bundles verified before storage
- **Rate limiting**: Prevents DoS attacks on key endpoints
- **Atomic operations**: Race condition prevention
- **Security logging**: All operations logged with IP addresses

### Key Management
- **ECDSA P-256**: Identity keys for signing
- **ECDH P-256**: Pre-keys for key exchange
- **SPKI format**: Standard public key format
- **Atomic consumption**: One-time pre-keys consumed exactly once

### Data Flow
1. Client generates key pairs locally (private keys never leave client)
2. Client uploads public key bundle with signature
3. Server verifies signature and stores public keys only
4. Other clients can retrieve public key bundles for key exchange
5. One-time pre-keys are consumed atomically during key exchange

## Rate Limits

- **Key Bundle Upload**: 10 per hour per user
- **One-Time Pre-Key Upload**: 50 batches per hour per user
- **Key Bundle Retrieval**: No specific limit (reasonable use expected)
- **Pre-Key Count**: No specific limit (lightweight operation)

Rate limits are enforced per user and per IP address for additional security.

## Database Schema

The encryption system uses the following database tables:

- `user_key_bundles`: User identity and signed pre-keys
- `one_time_prekeys`: One-time pre-keys for perfect forward secrecy
- `conversation_sessions`: Encrypted Double Ratchet session state
- `message_keys`: Out-of-order message key storage
- `key_bundle_upload_logs`: Security monitoring and rate limiting

All sensitive data is encrypted before storage, and private keys are never stored on the server.

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Authentication Errors

**Problem**: `{"detail":"Authentication credentials were not provided."}`

**Solution**:
- Ensure you include the Authorization header with a valid JWT token
- Check that the token hasn't expired
- Verify the token format: `Bearer <token>`

```bash
# Get a fresh token
curl -X POST http://127.0.0.1:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'
```

#### 2. Signature Verification Failures

**Problem**: `{"error": "Invalid signed pre-key signature", "code": "SIGNATURE_INVALID"}`

**Causes**:
- Signature was created over wrong data (must be raw SPKI bytes)
- Wrong signing algorithm (must be ECDSA with SHA-256)
- Identity key doesn't match the signature

**Solution**:
```python
# Correct signature creation
signature = identity_private_key.sign(
    signed_prekey_spki_bytes,  # Raw SPKI bytes, not base64
    ec.ECDSA(hashes.SHA256())
)
```

#### 3. Key Format Errors

**Problem**: `{"error": "Invalid ECDH public key format", "code": "KEY_FORMAT_INVALID"}`

**Solution**:
- Ensure keys are in SPKI format (not raw or other formats)
- Use P-256 curve (SECP256R1)
- Base64 encode the SPKI bytes

```python
# Correct key export
public_spki = public_key.public_bytes(
    encoding=serialization.Encoding.DER,
    format=serialization.PublicFormat.SubjectPublicKeyInfo
)
public_key_b64 = base64.b64encode(public_spki).decode('ascii')
```

#### 4. Rate Limiting

**Problem**: `{"error": "Rate limit exceeded. Please try again later."}`

**Solution**:
- Wait for the rate limit window to reset (1 hour)
- Check if you're making too many requests
- Consider implementing exponential backoff

#### 5. Database Migration Issues

**Problem**: `django.db.utils.ProgrammingError: column "encrypted_content" of relation "messages" already exists`

**Solution**:
```bash
# Mark the migration as fake-applied if fields already exist
python manage.py migrate messaging 0003 --fake
python manage.py migrate encryption
```

#### 6. Server Not Starting

**Problem**: Import errors or missing dependencies

**Solution**:
```bash
# Install required dependencies
pip install cryptography>=41.0.0

# Check for syntax errors
python manage.py check

# Run with verbose output
python manage.py runserver --verbosity=2
```

### Testing Your Implementation

#### 1. Basic Connectivity Test
```bash
curl -s http://127.0.0.1:8000/api/encryption/prekeys/count/
# Should return: {"detail":"Authentication credentials were not provided."}
```

#### 2. Full API Test
Use the provided test script:
```bash
cd backend
python test_encryption_api.py
```

#### 3. Database Verification
```bash
python manage.py shell -c "
from encryption.models import UserKeyBundle, OneTimePreKey
print('Key bundles:', UserKeyBundle.objects.count())
print('One-time pre-keys:', OneTimePreKey.objects.count())
"
```

### Performance Considerations

#### 1. Database Indexes
Ensure proper indexes are created:
```sql
-- Check indexes
SELECT indexname, tablename FROM pg_indexes
WHERE tablename LIKE '%key%' OR tablename LIKE '%session%';
```

#### 2. Connection Pooling
For production, configure database connection pooling:
```python
# settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'OPTIONS': {
            'MAX_CONNS': 20,
            'MIN_CONNS': 5,
        }
    }
}
```

#### 3. Monitoring
Monitor key metrics:
- Key bundle upload success rate
- One-time pre-key consumption rate
- Signature verification failure rate
- Rate limiting violations

### Security Checklist

- [ ] Server never stores private keys
- [ ] All signatures verified server-side
- [ ] Rate limiting enabled and configured
- [ ] HTTPS enabled in production
- [ ] Database connections encrypted
- [ ] Security logging enabled
- [ ] Regular security audits scheduled

### Getting Help

1. **Check the logs**: Look for detailed error messages in Django logs
2. **Run tests**: Use the provided test script to verify functionality
3. **Verify database**: Check that all tables and data are correct
4. **Review security**: Ensure no private keys are stored server-side
5. **Monitor performance**: Watch for rate limiting and slow queries
