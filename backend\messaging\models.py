# backend/messaging/models.py
import uuid
import secrets
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

class Conversation(models.Model):
    CONVERSATION_TYPES = [
        ('DIRECT', 'Direct Message'),
        ('GROUP', 'Group Chat'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    type = models.Char<PERSON>ield(max_length=10, choices=CONVERSATION_TYPES, default='DIRECT')
    name = models.CharField(max_length=100, blank=True, null=True)  # For group chats
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # PHASE 4: Group-specific fields
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_conversations',
        help_text="User who created this conversation"
    )
    description = models.TextField(blank=True, help_text="Group description")
    max_participants = models.Integer<PERSON>ield(default=50, help_text="Maximum number of participants")
    is_public = models.BooleanField(default=False, help_text="Whether this is a public group")
    invite_link = models.CharField(
        max_length=100,
        unique=True,
        null=True,
        blank=True,
        help_text="Unique invite link for the group"
    )
    group_settings = models.JSONField(
        default=dict,
        help_text="Group preferences and settings"
    )
    avatar_url = models.URLField(blank=True, null=True, help_text="Group avatar URL")
    pinned_message = models.ForeignKey(
        'Message',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='pinned_in_conversations',
        help_text="Pinned message in the group"
    )

    class Meta:
        db_table = 'conversations'
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['type', 'created_at']),
            models.Index(fields=['created_by', 'created_at']),
            models.Index(fields=['is_public']),
        ]

    def save(self, *args, **kwargs):
        # Generate invite link for groups if not exists
        if self.type == 'GROUP' and not self.invite_link:
            self.invite_link = self.generate_invite_link()
        super().save(*args, **kwargs)

    def generate_invite_link(self):
        """Generate a unique invite link for the group"""
        return secrets.token_urlsafe(16)

    def __str__(self):
        if self.type == 'GROUP':
            return self.name or f"Group {self.id}"
        return f"Direct conversation {self.id}"

class ConversationParticipant(models.Model):
    PARTICIPANT_ROLES = [
        ('admin', 'Admin'),
        ('moderator', 'Moderator'),
        ('member', 'Member'),
        ('restricted', 'Restricted'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='participants')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversation_memberships')
    role = models.CharField(max_length=15, choices=PARTICIPANT_ROLES, default='member')
    joined_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True, help_text="Whether the participant is active in the conversation")

    # PHASE 4: Group-specific permissions
    can_add_members = models.BooleanField(default=False, help_text="Can add new members to the group")
    can_remove_members = models.BooleanField(default=False, help_text="Can remove members from the group")
    can_edit_group_info = models.BooleanField(default=False, help_text="Can edit group name, description, etc.")
    can_pin_messages = models.BooleanField(default=False, help_text="Can pin/unpin messages")
    can_delete_messages = models.BooleanField(default=False, help_text="Can delete messages from others")

    # PHASE 4: Notification preferences
    notifications_enabled = models.BooleanField(default=True, help_text="Receive notifications for this group")
    mention_notifications_only = models.BooleanField(default=False, help_text="Only notify when mentioned")

    class Meta:
        db_table = 'conversation_participants'
        unique_together = ['conversation', 'user']
        indexes = [
            models.Index(fields=['conversation', 'is_active']),
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['role']),
        ]

    def save(self, *args, **kwargs):
        """Set permissions based on role"""
        if self.role == 'admin':
            self.can_add_members = True
            self.can_remove_members = True
            self.can_edit_group_info = True
            self.can_pin_messages = True
            self.can_delete_messages = True
        elif self.role == 'moderator':
            self.can_pin_messages = True
            self.can_delete_messages = True
        elif self.role == 'restricted':
            # Restricted users have no special permissions
            self.can_add_members = False
            self.can_remove_members = False
            self.can_edit_group_info = False
            self.can_pin_messages = False
            self.can_delete_messages = False

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.username} ({self.role}) in {self.conversation}"

class Message(models.Model):
    MESSAGE_TYPES = [
        ('TEXT', 'Text Message'),
        ('IMAGE', 'Image'),
        ('FILE', 'File'),
        ('SYSTEM', 'System Message'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')

    # PHASE 3: Backward compatibility - keep for existing messages
    content = models.TextField(
        blank=True,
        help_text="Plaintext content (Phase 2 compatibility, empty for encrypted messages)"
    )

    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='TEXT')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # PHASE 3: Encryption fields
    encrypted_content = models.TextField(
        blank=True,
        help_text="AES-GCM encrypted message content (base64 encoded)"
    )
    iv = models.TextField(
        blank=True,
        help_text="96-bit initialization vector (base64 encoded)"
    )
    sender_ratchet_key = models.TextField(
        blank=True,
        help_text="Sender's current DH ratchet public key (SPKI format, base64)"
    )
    message_number = models.IntegerField(
        default=0,
        help_text="Message counter in Double Ratchet (N)"
    )
    previous_chain_length = models.IntegerField(
        default=0,
        help_text="Previous chain length in Double Ratchet (PN)"
    )

    # PHASE 5: Media message support - OPTIMIZED
    has_media = models.BooleanField(default=False, db_index=True)
    media_count = models.IntegerField(default=0)

    class Meta:
        db_table = 'messages'
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['conversation', 'created_at']),
            models.Index(fields=['sender', 'created_at']),
            models.Index(fields=['conversation', 'message_number']),
            models.Index(fields=['conversation', 'has_media', 'created_at']),
        ]

    def __str__(self):
        return f"Message from {self.sender.username} in {self.conversation}"

    @property
    def is_encrypted(self):
        """Check if this message is encrypted"""
        return bool(self.encrypted_content)

    def get_content(self):
        """Get message content (encrypted or plaintext for backward compatibility)"""
        if self.encrypted_content:
            return "[Encrypted Message]"  # Client will decrypt
        return self.content


class MessageStatus(models.Model):
    STATUS_TYPES = [
        ('SENT', 'Sent'),
        ('DELIVERED', 'Delivered'),
        ('READ', 'Read'),
        ('FAILED', 'Failed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='statuses')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='message_statuses')
    status = models.CharField(max_length=10, choices=STATUS_TYPES, default='SENT')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'message_statuses'
        unique_together = ['message', 'user']
        ordering = ['created_at']

    def __str__(self):
        return f"{self.user.username} - {self.message.id} - {self.status}"


# PHASE 4: Group-specific models

class GroupInvite(models.Model):
    """Track group invitations"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='invites')
    invited_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_invites')
    invited_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='received_invites',
        null=True,
        blank=True
    )
    invited_email = models.EmailField(null=True, blank=True, help_text="For non-users")
    invite_code = models.CharField(max_length=50, unique=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)
    used_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'group_invites'
        unique_together = ['conversation', 'invited_user']
        indexes = [
            models.Index(fields=['invite_code']),
            models.Index(fields=['expires_at', 'is_used']),
            models.Index(fields=['conversation', 'is_used']),
        ]

    def save(self, *args, **kwargs):
        if not self.invite_code:
            self.invite_code = secrets.token_urlsafe(32)
        super().save(*args, **kwargs)

    def __str__(self):
        target = self.invited_user.username if self.invited_user else self.invited_email
        return f"Invite to {self.conversation.name} for {target}"


class GroupEvent(models.Model):
    """Track group events for audit and notifications"""
    EVENT_TYPES = [
        ('member_added', 'Member Added'),
        ('member_removed', 'Member Removed'),
        ('member_left', 'Member Left'),
        ('role_changed', 'Role Changed'),
        ('group_info_updated', 'Group Info Updated'),
        ('message_pinned', 'Message Pinned'),
        ('message_unpinned', 'Message Unpinned'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='events')
    event_type = models.CharField(max_length=20, choices=EVENT_TYPES)
    actor = models.ForeignKey(User, on_delete=models.CASCADE, related_name='group_actions')
    target_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='group_events_received',
        null=True,
        blank=True
    )
    event_data = models.JSONField(default=dict, help_text="Additional event details")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'group_events'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['conversation', 'created_at']),
            models.Index(fields=['event_type', 'created_at']),
            models.Index(fields=['actor', 'created_at']),
        ]

    def __str__(self):
        return f"{self.event_type} in {self.conversation.name} by {self.actor.username}"
