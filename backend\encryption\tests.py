# backend/encryption/tests.py
"""
Comprehensive tests for encryption app.
SECURITY: Tests include signature verification, rate limiting, and atomic operations.
"""
import base64
import json
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, MagicMock
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import ec

from .models import UserKeyBundle, OneTimePreKey, ConversationSession, MessageKey, KeyBundleUploadLog
from .crypto_utils import (
    verify_signed_prekey_signature, validate_ecdh_public_key,
    decode_base64_key, load_ecdsa_public_key, CryptoError
)

User = get_user_model()


class CryptoUtilsTestCase(TestCase):
    """Test cryptographic utility functions"""
    
    def setUp(self):
        # Generate test keys
        self.identity_private_key = ec.generate_private_key(ec.SECP256R1())
        self.identity_public_key = self.identity_private_key.public_key()
        
        self.signed_prekey_private = ec.generate_private_key(ec.SECP256R1())
        self.signed_prekey_public = self.signed_prekey_private.public_key()
        
        # Export to SPKI format
        self.identity_public_spki = self.identity_public_key.public_key_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        self.signed_prekey_spki = self.signed_prekey_public.public_key_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        # Create signature
        self.signature = self.identity_private_key.sign(
            self.signed_prekey_spki,
            ec.ECDSA(hashes.SHA256())
        )
        
        # Base64 encode
        self.identity_public_b64 = base64.b64encode(self.identity_public_spki).decode('ascii')
        self.signed_prekey_b64 = base64.b64encode(self.signed_prekey_spki).decode('ascii')
        self.signature_b64 = base64.b64encode(self.signature).decode('ascii')
    
    def test_decode_base64_key_valid(self):
        """Test decoding valid base64 key"""
        decoded = decode_base64_key(self.identity_public_b64)
        self.assertEqual(decoded, self.identity_public_spki)
    
    def test_decode_base64_key_invalid(self):
        """Test decoding invalid base64 key"""
        with self.assertRaises(CryptoError):
            decode_base64_key("invalid_base64!")
    
    def test_decode_base64_key_too_short(self):
        """Test decoding key that's too short"""
        short_key = base64.b64encode(b"short").decode('ascii')
        with self.assertRaises(CryptoError):
            decode_base64_key(short_key)
    
    def test_load_ecdsa_public_key_valid(self):
        """Test loading valid ECDSA public key"""
        loaded_key = load_ecdsa_public_key(self.identity_public_spki)
        self.assertIsInstance(loaded_key, ec.EllipticCurvePublicKey)
        self.assertIsInstance(loaded_key.curve, ec.SECP256R1)
    
    def test_load_ecdsa_public_key_invalid(self):
        """Test loading invalid key data"""
        with self.assertRaises(CryptoError):
            load_ecdsa_public_key(b"invalid_key_data")
    
    def test_verify_signed_prekey_signature_valid(self):
        """Test valid signature verification"""
        result = verify_signed_prekey_signature(
            self.identity_public_b64,
            self.signed_prekey_b64,
            self.signature_b64
        )
        self.assertTrue(result)
    
    def test_verify_signed_prekey_signature_invalid(self):
        """Test invalid signature verification"""
        # Create wrong signature
        wrong_signature = self.identity_private_key.sign(
            b"wrong_data",
            ec.ECDSA(hashes.SHA256())
        )
        wrong_signature_b64 = base64.b64encode(wrong_signature).decode('ascii')
        
        result = verify_signed_prekey_signature(
            self.identity_public_b64,
            self.signed_prekey_b64,
            wrong_signature_b64
        )
        self.assertFalse(result)
    
    def test_validate_ecdh_public_key_valid(self):
        """Test valid ECDH key validation"""
        result = validate_ecdh_public_key(self.signed_prekey_b64)
        self.assertTrue(result)
    
    def test_validate_ecdh_public_key_invalid(self):
        """Test invalid ECDH key validation"""
        result = validate_ecdh_public_key("invalid_key")
        self.assertFalse(result)


class UserKeyBundleModelTestCase(TestCase):
    """Test UserKeyBundle model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_create_key_bundle(self):
        """Test creating a key bundle"""
        bundle = UserKeyBundle.objects.create(
            user=self.user,
            identity_public_key='test_identity_key',
            signed_prekey_id=1,
            signed_prekey_public='test_signed_prekey',
            signed_prekey_signature='test_signature'
        )
        
        self.assertEqual(bundle.user, self.user)
        self.assertEqual(bundle.signed_prekey_id, 1)
        self.assertTrue(bundle.id)
        self.assertTrue(bundle.created_at)
    
    def test_key_bundle_str(self):
        """Test string representation"""
        bundle = UserKeyBundle.objects.create(
            user=self.user,
            identity_public_key='test_identity_key',
            signed_prekey_id=1,
            signed_prekey_public='test_signed_prekey',
            signed_prekey_signature='test_signature'
        )
        
        expected = f"Key bundle for {self.user.username}"
        self.assertEqual(str(bundle), expected)


class OneTimePreKeyModelTestCase(TestCase):
    """Test OneTimePreKey model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_create_one_time_prekey(self):
        """Test creating a one-time pre-key"""
        prekey = OneTimePreKey.objects.create(
            user=self.user,
            key_id=1,
            public_key='test_public_key'
        )
        
        self.assertEqual(prekey.user, self.user)
        self.assertEqual(prekey.key_id, 1)
        self.assertFalse(prekey.is_used)
        self.assertIsNone(prekey.used_at)
    
    def test_mark_as_used(self):
        """Test marking pre-key as used"""
        prekey = OneTimePreKey.objects.create(
            user=self.user,
            key_id=1,
            public_key='test_public_key'
        )
        
        prekey.mark_as_used()
        prekey.refresh_from_db()
        
        self.assertTrue(prekey.is_used)
        self.assertIsNotNone(prekey.used_at)
    
    def test_unique_constraint(self):
        """Test unique constraint on user + key_id"""
        OneTimePreKey.objects.create(
            user=self.user,
            key_id=1,
            public_key='test_public_key_1'
        )
        
        # Should raise IntegrityError for duplicate key_id
        with self.assertRaises(Exception):
            OneTimePreKey.objects.create(
                user=self.user,
                key_id=1,
                public_key='test_public_key_2'
            )


class EncryptionAPITestCase(APITestCase):
    """Test encryption API endpoints"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Generate test crypto keys
        self.identity_private_key = ec.generate_private_key(ec.SECP256R1())
        self.identity_public_key = self.identity_private_key.public_key()
        
        self.signed_prekey_private = ec.generate_private_key(ec.SECP256R1())
        self.signed_prekey_public = self.signed_prekey_private.public_key()
        
        # Export to SPKI and create signature
        identity_spki = self.identity_public_key.public_key_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        signed_prekey_spki = self.signed_prekey_public.public_key_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        signature = self.identity_private_key.sign(
            signed_prekey_spki,
            ec.ECDSA(hashes.SHA256())
        )
        
        self.valid_bundle_data = {
            'identity_public_key': base64.b64encode(identity_spki).decode('ascii'),
            'signed_prekey_id': 1,
            'signed_prekey_public': base64.b64encode(signed_prekey_spki).decode('ascii'),
            'signed_prekey_signature': base64.b64encode(signature).decode('ascii')
        }
    
    def test_upload_key_bundle_success(self):
        """Test successful key bundle upload"""
        self.client.force_authenticate(user=self.user)
        
        response = self.client.post(
            reverse('encryption:upload_key_bundle'),
            data=self.valid_bundle_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('key_bundle_id', response.data)
        self.assertIn('message', response.data)
        
        # Verify bundle was created
        bundle = UserKeyBundle.objects.get(user=self.user)
        self.assertEqual(bundle.signed_prekey_id, 1)
    
    def test_upload_key_bundle_invalid_signature(self):
        """Test key bundle upload with invalid signature"""
        self.client.force_authenticate(user=self.user)
        
        invalid_data = self.valid_bundle_data.copy()
        invalid_data['signed_prekey_signature'] = base64.b64encode(b'invalid_signature').decode('ascii')
        
        response = self.client.post(
            reverse('encryption:upload_key_bundle'),
            data=invalid_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
        self.assertEqual(response.data['code'], 'SIGNATURE_INVALID')
    
    def test_upload_key_bundle_unauthenticated(self):
        """Test key bundle upload without authentication"""
        response = self.client.post(
            reverse('encryption:upload_key_bundle'),
            data=self.valid_bundle_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_get_key_bundle_success(self):
        """Test successful key bundle retrieval"""
        # Create key bundle for other user
        UserKeyBundle.objects.create(
            user=self.other_user,
            **self.valid_bundle_data
        )
        
        # Create one-time pre-key
        OneTimePreKey.objects.create(
            user=self.other_user,
            key_id=42,
            public_key='test_opk_key'
        )
        
        self.client.force_authenticate(user=self.user)
        
        response = self.client.get(
            reverse('encryption:get_key_bundle', kwargs={'user_id': self.other_user.id})
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('identity_public_key', response.data)
        self.assertIn('signed_prekey', response.data)
        self.assertIn('one_time_prekey', response.data)
        
        # Verify one-time pre-key was consumed
        opk = OneTimePreKey.objects.get(user=self.other_user, key_id=42)
        self.assertTrue(opk.is_used)
        self.assertIsNotNone(opk.used_at)
    
    def test_get_key_bundle_not_found(self):
        """Test key bundle retrieval for user without bundle"""
        self.client.force_authenticate(user=self.user)
        
        response = self.client.get(
            reverse('encryption:get_key_bundle', kwargs={'user_id': self.other_user.id})
        )
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['code'], 'KEY_BUNDLE_NOT_FOUND')
    
    def test_upload_one_time_prekeys_success(self):
        """Test successful one-time pre-key upload"""
        self.client.force_authenticate(user=self.user)
        
        # Generate ECDH key for testing
        ecdh_key = ec.generate_private_key(ec.SECP256R1())
        ecdh_public = ecdh_key.public_key()
        ecdh_spki = ecdh_public.public_key_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        ecdh_b64 = base64.b64encode(ecdh_spki).decode('ascii')
        
        data = {
            'prekeys': [
                {'key_id': 1, 'public_key': ecdh_b64},
                {'key_id': 2, 'public_key': ecdh_b64},
            ]
        }
        
        response = self.client.post(
            reverse('encryption:upload_one_time_prekeys'),
            data=data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['count'], 2)
        
        # Verify pre-keys were created
        prekeys = OneTimePreKey.objects.filter(user=self.user)
        self.assertEqual(prekeys.count(), 2)
    
    def test_get_prekey_count(self):
        """Test getting pre-key count"""
        # Create some pre-keys
        OneTimePreKey.objects.create(user=self.user, key_id=1, public_key='key1')
        OneTimePreKey.objects.create(user=self.user, key_id=2, public_key='key2', is_used=True)
        
        self.client.force_authenticate(user=self.user)
        
        response = self.client.get(reverse('encryption:get_prekey_count'))
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_count'], 2)
        self.assertEqual(response.data['available_count'], 1)
        self.assertEqual(response.data['used_count'], 1)
