// socket-server/src/schemas/mediaSchemas.ts
import { z } from 'zod';

// Media upload event schemas
export const MediaUploadStartedSchema = z.object({
  conversationId: z.string().uuid(),
  messageId: z.string().uuid(),
  fileName: z.string().min(1).max(255),
  fileSize: z.number().positive().max(100 * 1024 * 1024), // 100MB max
  fileType: z.enum(['image', 'document', 'audio', 'video', 'archive', 'other']),
  tempId: z.string().optional(),
});

export const MediaUploadProgressSchema = z.object({
  conversationId: z.string().uuid(),
  messageId: z.string().uuid(),
  progress: z.number().min(0).max(100),
  tempId: z.string().optional(),
});

export const MediaUploadCompletedSchema = z.object({
  conversationId: z.string().uuid(),
  messageId: z.string().uuid(),
  mediaFileId: z.string().uuid(),
  tempId: z.string().optional(),
});

export const MediaUploadFailedSchema = z.object({
  conversationId: z.string().uuid(),
  messageId: z.string().uuid(),
  error: z.string(),
  tempId: z.string().optional(),
});

export const MediaDownloadStartedSchema = z.object({
  conversationId: z.string().uuid(),
  mediaFileId: z.string().uuid(),
  fileName: z.string().optional(),
});

// Media message schemas
export const MediaMessageSchema = z.object({
  id: z.string().uuid(),
  conversationId: z.string().uuid(),
  sender: z.object({
    id: z.string().uuid(),
    username: z.string(),
    firstName: z.string(),
    lastName: z.string(),
  }),
  messageType: z.literal('FILE'),
  hasMedia: z.literal(true),
  mediaCount: z.number().positive(),
  mediaFileId: z.string().uuid(),
  createdAt: z.string(),
  updatedAt: z.string(),
  tempId: z.string().optional(),
  // Encryption fields (optional)
  encryptedContent: z.string().optional(),
  iv: z.string().optional(),
  senderRatchetKey: z.string().optional(),
  messageNumber: z.number().optional(),
  previousChainLength: z.number().optional(),
  isEncrypted: z.boolean().optional(),
  // Plaintext content (optional)
  content: z.string().optional(),
});

// Type exports
export type MediaUploadStarted = z.infer<typeof MediaUploadStartedSchema>;
export type MediaUploadProgress = z.infer<typeof MediaUploadProgressSchema>;
export type MediaUploadCompleted = z.infer<typeof MediaUploadCompletedSchema>;
export type MediaUploadFailed = z.infer<typeof MediaUploadFailedSchema>;
export type MediaDownloadStarted = z.infer<typeof MediaDownloadStartedSchema>;
export type MediaMessage = z.infer<typeof MediaMessageSchema>;

// Validation helper
export function validateMediaEvent<T>(schema: z.ZodSchema<T>, data: unknown): T {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(`Media event validation failed: ${error.issues.map(i => i.message).join(', ')}`);
    }
    throw error;
  }
}

// Media file type validation
export const SUPPORTED_FILE_TYPES = {
  image: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
  document: ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
  audio: ['.mp3', '.wav', '.ogg'],
  video: ['.mp4', '.webm', '.mov'],
  archive: ['.zip', '.rar', '.7z'],
} as const;

export const MAX_FILE_SIZES = {
  image: 10 * 1024 * 1024,    // 10MB
  document: 25 * 1024 * 1024, // 25MB
  audio: 25 * 1024 * 1024,    // 25MB
  video: 100 * 1024 * 1024,   // 100MB
  archive: 50 * 1024 * 1024,  // 50MB
} as const;

export function getFileTypeFromExtension(filename: string): string {
  const extension = filename.toLowerCase().split('.').pop();
  if (!extension) return 'other';

  for (const [type, extensions] of Object.entries(SUPPORTED_FILE_TYPES)) {
    if ((extensions as readonly string[]).includes(`.${extension}`)) {
      return type;
    }
  }
  return 'other';
}

export function validateFileSize(fileSize: number, fileType: string): boolean {
  const maxSize = MAX_FILE_SIZES[fileType as keyof typeof MAX_FILE_SIZES] || MAX_FILE_SIZES.document;
  return fileSize <= maxSize;
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
