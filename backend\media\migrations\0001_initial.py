# Generated by Django 5.2.4 on 2025-08-31 07:28

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('messaging', '0005_message_has_media_message_media_count_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MediaChunk',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('upload_session', models.Char<PERSON>ield(db_index=True, max_length=100)),
                ('chunk_number', models.IntegerField()),
                ('total_chunks', models.IntegerField()),
                ('chunk_data', models.BinaryField()),
                ('chunk_hash', models.CharField(max_length=64)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'media_chunks',
                'indexes': [models.Index(fields=['upload_session', 'chunk_number'], name='media_chunk_upload__e4884a_idx')],
                'unique_together': {('upload_session', 'chunk_number')},
            },
        ),
        migrations.CreateModel(
            name='MediaFile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('original_filename', models.CharField(max_length=255)),
                ('file_type', models.CharField(choices=[('image', 'Image'), ('document', 'Document'), ('audio', 'Audio'), ('video', 'Video'), ('archive', 'Archive'), ('other', 'Other')], max_length=10)),
                ('mime_type', models.CharField(max_length=100)),
                ('file_size', models.BigIntegerField()),
                ('encrypted_file_path', models.CharField(max_length=500)),
                ('encrypted_thumbnail_path', models.CharField(blank=True, max_length=500, null=True)),
                ('wrapped_file_key', models.TextField()),
                ('file_nonce', models.TextField()),
                ('thumbnail_nonce', models.TextField(blank=True, null=True)),
                ('processing_status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=15)),
                ('processing_error', models.TextField(blank=True, null=True)),
                ('encrypted_metadata', models.TextField(blank=True, null=True)),
                ('virus_scan_status', models.CharField(default='pending', max_length=20)),
                ('virus_scan_result', models.TextField(blank=True, null=True)),
                ('virus_scan_hash', models.CharField(blank=True, max_length=64, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='media_files', to='messaging.message')),
                ('uploader', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='uploaded_media', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'media_files',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MediaDownload',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('download_token', models.CharField(max_length=100, unique=True)),
                ('expires_at', models.DateTimeField()),
                ('download_count', models.IntegerField(default=0)),
                ('max_downloads', models.IntegerField(default=10)),
                ('last_downloaded_at', models.DateTimeField(blank=True, null=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('downloaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='media_downloads', to=settings.AUTH_USER_MODEL)),
                ('media_file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='downloads', to='media.mediafile')),
            ],
            options={
                'db_table': 'media_downloads',
            },
        ),
        migrations.CreateModel(
            name='MediaProcessingJob',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('job_type', models.CharField(choices=[('virus_scan', 'Virus Scanning'), ('thumbnail', 'Thumbnail Generation'), ('metadata_extraction', 'Metadata Extraction'), ('compression', 'File Compression')], max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=15)),
                ('priority', models.IntegerField(default=5)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('result_data', models.JSONField(default=dict)),
                ('retry_count', models.IntegerField(default=0)),
                ('max_retries', models.IntegerField(default=3)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('media_file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='processing_jobs', to='media.mediafile')),
            ],
            options={
                'db_table': 'media_processing_jobs',
                'ordering': ['priority', 'created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='mediafile',
            index=models.Index(fields=['message', 'file_type'], name='media_files_message_349291_idx'),
        ),
        migrations.AddIndex(
            model_name='mediafile',
            index=models.Index(fields=['uploader', 'created_at'], name='media_files_uploade_9fbe4e_idx'),
        ),
        migrations.AddIndex(
            model_name='mediafile',
            index=models.Index(fields=['virus_scan_status'], name='media_files_virus_s_60f726_idx'),
        ),
        migrations.AddIndex(
            model_name='mediadownload',
            index=models.Index(fields=['download_token', 'expires_at'], name='media_downl_downloa_28e66f_idx'),
        ),
        migrations.AddIndex(
            model_name='mediaprocessingjob',
            index=models.Index(fields=['status', 'priority'], name='media_proce_status_857309_idx'),
        ),
        migrations.AddIndex(
            model_name='mediaprocessingjob',
            index=models.Index(fields=['job_type', 'status'], name='media_proce_job_typ_4491c0_idx'),
        ),
    ]
