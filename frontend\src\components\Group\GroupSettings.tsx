import React, { useState } from 'react';
import { X, Users, Settings, UserPlus, UserMinus, Crown, Shield, Search } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { useSearchUsersQuery, useUpdateGroupInfoMutation, useAddGroupMemberMutation, useRemoveGroupMemberMutation } from '../../services';
import { useDebounce } from '../../hooks/useDebounce';

interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
}

interface Participant {
  id: string;
  user: User;
  role: 'admin' | 'moderator' | 'member' | 'restricted';
  joinedAt: string;
  canAddMembers: boolean;
  canRemoveMembers: boolean;
  canEditGroupInfo: boolean;
  canPinMessages: boolean;
  canDeleteMessages: boolean;
}

interface Group {
  id: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  createdBy: User;
  participants: Participant[];
  maxParticipants: number;
  isPublic: boolean;
  createdAt: string;
}

interface GroupSettingsProps {
  open: boolean;
  onClose: () => void;
  group: Group;
  currentUser: User;
}

const GroupSettings: React.FC<GroupSettingsProps> = ({
  open,
  onClose,
  group,
  currentUser,
}) => {
  const [groupName, setGroupName] = useState(group?.name || '');
  const [groupDescription, setGroupDescription] = useState(group?.description || '');
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showAddMember, setShowAddMember] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedNewMembers, setSelectedNewMembers] = useState<User[]>([]);

  // Debounce the search query
  const debouncedQuery = useDebounce(searchQuery, 300);

  // Search for users to add
  const { data: searchData, isLoading: searchLoading } = useSearchUsersQuery(
    { query: debouncedQuery, limit: 10 },
    { skip: !debouncedQuery || debouncedQuery.length < 2 }
  );

  // API mutations
  const [updateGroupInfo] = useUpdateGroupInfoMutation();
  const [addGroupMember] = useAddGroupMemberMutation();
  const [removeGroupMember] = useRemoveGroupMemberMutation();

  const currentUserParticipant = group?.participants?.find(
    (p) => p.user.id === currentUser.id
  );

  const canManageGroup = currentUserParticipant?.canEditGroupInfo;
  const canManageMembers = currentUserParticipant?.canAddMembers || currentUserParticipant?.canRemoveMembers;

  const handleSaveGroupInfo = async () => {
    if (!canManageGroup || !group?.id) return;

    setLoading(true);
    try {
      await updateGroupInfo({
        conversationId: group.id,
        name: groupName,
        description: groupDescription,
      }).unwrap();
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update group info:', error);
      alert('Failed to update group information. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveMember = async (userId: string) => {
    if (!canManageMembers || !group?.id) return;

    const confirmRemove = window.confirm('Are you sure you want to remove this member from the group?');
    if (!confirmRemove) return;

    setLoading(true);
    try {
      await removeGroupMember({
        conversationId: group.id,
        userId: userId,
      }).unwrap();
    } catch (error) {
      console.error('Failed to remove member:', error);
      alert('Failed to remove member. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddMembers = async () => {
    if (!canManageMembers || selectedNewMembers.length === 0 || !group?.id) return;

    setLoading(true);
    try {
      // Add each selected member
      for (const member of selectedNewMembers) {
        await addGroupMember({
          conversationId: group.id,
          userId: member.id,
          role: 'member',
        }).unwrap();
      }

      // Reset state
      setSelectedNewMembers([]);
      setSearchQuery('');
      setShowAddMember(false);
    } catch (error) {
      console.error('Failed to add members:', error);
      alert('Failed to add members. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectMember = (user: User) => {
    // Check if user is already a member
    const isAlreadyMember = group?.participants?.some(p => p.user.id === user.id);
    if (isAlreadyMember) {
      alert('This user is already a member of the group.');
      return;
    }

    // Check if user is already selected
    const isAlreadySelected = selectedNewMembers.some(m => m.id === user.id);
    if (isAlreadySelected) {
      setSelectedNewMembers(selectedNewMembers.filter(m => m.id !== user.id));
    } else {
      setSelectedNewMembers([...selectedNewMembers, user]);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'moderator':
        return <Shield className="w-4 h-4 text-blue-500" />;
      default:
        return null;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'moderator':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'restricted':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Settings className="w-6 h-6 text-gray-600" />
            <h2 className="text-xl font-semibold text-gray-900">Group Settings</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Group Information */}
          <div className="mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Group Information</h3>
            
            {isEditing ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Group Name
                  </label>
                  <Input
                    value={groupName}
                    onChange={(e) => setGroupName(e.target.value)}
                    placeholder="Enter group name"
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={groupDescription}
                    onChange={(e) => setGroupDescription(e.target.value)}
                    placeholder="Enter group description"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div>
                  <span className="text-sm font-medium text-gray-500">Name:</span>
                  <p className="text-gray-900">{group?.name}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-500">Description:</span>
                  <p className="text-gray-900">{group?.description || 'No description'}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-500">Created by:</span>
                  <p className="text-gray-900">
                    {group?.createdBy?.firstName} {group?.createdBy?.lastName} (@{group?.createdBy?.username})
                  </p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-500">Members:</span>
                  <p className="text-gray-900">{group?.participants?.length} / {group?.maxParticipants}</p>
                </div>
              </div>
            )}
          </div>

          {/* Members List */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Members ({group?.participants?.length})
              </h3>
              {canManageMembers && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAddMember(true)}
                  className="flex items-center space-x-1"
                >
                  <UserPlus className="w-4 h-4" />
                  <span>Add Member</span>
                </Button>
              )}
            </div>
            
            <div className="space-y-3">
              {group?.participants?.map((participant) => (
                <div
                  key={participant.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                      {participant.user.profilePicture ? (
                        <img
                          src={participant.user.profilePicture}
                          alt={participant.user.firstName}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-gray-600 font-medium">
                          {participant.user.firstName[0]}
                        </span>
                      )}
                    </div>
                    
                    <div>
                      <p className="font-medium text-gray-900">
                        {participant.user.firstName} {participant.user.lastName}
                      </p>
                      <p className="text-sm text-gray-500">@{participant.user.username}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <div className={`px-2 py-1 rounded-full text-xs font-medium border flex items-center space-x-1 ${getRoleColor(participant.role)}`}>
                      {getRoleIcon(participant.role)}
                      <span className="capitalize">{participant.role}</span>
                    </div>
                    
                    {canManageMembers && participant.user.id !== currentUser.id && (
                      <button
                        onClick={() => handleRemoveMember(participant.user.id)}
                        disabled={loading}
                        className="text-red-500 hover:text-red-700 transition-colors disabled:opacity-50"
                        title="Remove member"
                      >
                        <UserMinus className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
          {isEditing ? (
            <>
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditing(false);
                  setGroupName(group?.name || '');
                  setGroupDescription(group?.description || '');
                }}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveGroupInfo}
                disabled={loading || !groupName.trim()}
                loading={loading}
              >
                Save Changes
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
              {canManageGroup && (
                <Button onClick={() => setIsEditing(true)}>
                  Edit Group
                </Button>
              )}
            </>
          )}
        </div>
      </div>

      {/* Add Member Dialog */}
      {showAddMember && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold text-gray-900">Add Members</h3>
              <button
                onClick={() => {
                  setShowAddMember(false);
                  setSearchQuery('');
                  setSelectedNewMembers([]);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="p-4">
              {/* Search Input */}
              <div className="relative mb-4">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder="Search users..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Selected Members */}
              {selectedNewMembers.length > 0 && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-2">Selected:</p>
                  <div className="flex flex-wrap gap-2">
                    {selectedNewMembers.map((member) => (
                      <div
                        key={member.id}
                        className="flex items-center space-x-2 bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm"
                      >
                        <span>{member.firstName} {member.lastName}</span>
                        <button
                          onClick={() => handleSelectMember(member)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Search Results */}
              <div className="max-h-60 overflow-y-auto">
                {searchLoading && (
                  <div className="flex items-center justify-center py-4">
                    <LoadingSpinner size="sm" />
                  </div>
                )}

                {searchData?.results && searchData.results.length > 0 && (
                  <div className="space-y-2">
                    {searchData.results
                      .filter(user => user.id !== currentUser.id) // Don't show current user
                      .map((user) => {
                        const isAlreadyMember = group?.participants?.some(p => p.user.id === user.id);
                        const isSelected = selectedNewMembers.some(m => m.id === user.id);

                        return (
                          <div
                            key={user.id}
                            className={`flex items-center justify-between p-2 rounded-lg cursor-pointer transition-colors ${
                              isAlreadyMember
                                ? 'bg-gray-100 cursor-not-allowed opacity-50'
                                : isSelected
                                ? 'bg-blue-50 border border-blue-200'
                                : 'hover:bg-gray-50'
                            }`}
                            onClick={() => !isAlreadyMember && handleSelectMember(user)}
                          >
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                {user.profile_picture ? (
                                  <img
                                    src={user.profile_picture}
                                    alt={user.first_name}
                                    className="w-8 h-8 rounded-full object-cover"
                                  />
                                ) : (
                                  <span className="text-gray-600 text-sm font-medium">
                                    {user.first_name[0]}
                                  </span>
                                )}
                              </div>
                              <div>
                                <p className="font-medium text-gray-900 text-sm">
                                  {user.first_name} {user.last_name}
                                </p>
                                <p className="text-xs text-gray-500">@{user.username}</p>
                              </div>
                            </div>

                            {isAlreadyMember && (
                              <span className="text-xs text-gray-500">Already a member</span>
                            )}
                            {isSelected && !isAlreadyMember && (
                              <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                                <X className="w-3 h-3 text-white" />
                              </div>
                            )}
                          </div>
                        );
                      })}
                  </div>
                )}

                {debouncedQuery && searchData?.results?.length === 0 && !searchLoading && (
                  <p className="text-center text-gray-500 py-4">No users found</p>
                )}
              </div>
            </div>

            {/* Dialog Footer */}
            <div className="flex items-center justify-end space-x-3 p-4 border-t bg-gray-50">
              <Button
                variant="outline"
                onClick={() => {
                  setShowAddMember(false);
                  setSearchQuery('');
                  setSelectedNewMembers([]);
                }}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddMembers}
                disabled={loading || selectedNewMembers.length === 0}
                loading={loading}
              >
                Add {selectedNewMembers.length} Member{selectedNewMembers.length !== 1 ? 's' : ''}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GroupSettings;
