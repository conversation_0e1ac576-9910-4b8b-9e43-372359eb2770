# Chat Application Services Makefile
# This Makefile provides commands to manage all services

.PHONY: help start stop clean install-deps check-deps backend frontend socket-server all

# Default target
help:
	@echo "Chat Application Service Management"
	@echo "==================================="
	@echo "Available commands:"
	@echo "  make start        - Start all services (Django, React, Socket Server)"
	@echo "  make backend      - Start Django backend only (port 6000)"
	@echo "  make frontend     - Start React frontend only (port 5000)"
	@echo "  make socket-server - Start Socket server only (port 7000)"
	@echo "  make stop         - Stop all running services"
	@echo "  make clean        - Clean up log files and temporary files"
	@echo "  make install-deps - Install all dependencies"
	@echo "  make check-deps   - Check if all dependencies are installed"
	@echo "  make all          - Install dependencies and start all services"
	@echo ""
	@echo "Services will run on:"
	@echo "  - Django Backend:  http://localhost:6000"
	@echo "  - React Frontend:  http://localhost:5000"
	@echo "  - Socket Server:   http://localhost:7000"
	@echo ""
	@echo "Press Ctrl+C to stop services"

# Check dependencies (Windows compatible)
check-deps:
	@echo Checking dependencies...
	@echo Checking Python virtual environment...
	@powershell -Command "if (!(Test-Path 'backend\venv')) { Write-Host '❌ Virtual environment not found in backend\venv' -ForegroundColor Red; Write-Host 'Please run: make install-deps' -ForegroundColor Yellow; exit 1 } else { Write-Host '✅ Python virtual environment found' -ForegroundColor Green }"
	@echo Checking pnpm...
	@powershell -Command "try { pnpm --version | Out-Null; Write-Host '✅ pnpm found' -ForegroundColor Green } catch { Write-Host '❌ pnpm is not installed' -ForegroundColor Red; Write-Host 'Please install pnpm: npm install -g pnpm' -ForegroundColor Yellow; exit 1 }"
	@echo Checking Node.js dependencies...
	@powershell -Command "if (!(Test-Path 'frontend\node_modules')) { Write-Host '❌ Frontend dependencies not installed' -ForegroundColor Red; Write-Host 'Please run: make install-deps' -ForegroundColor Yellow; exit 1 } else { Write-Host '✅ Frontend dependencies found' -ForegroundColor Green }"
	@powershell -Command "if (!(Test-Path 'socket-server\node_modules')) { Write-Host '❌ Socket server dependencies not installed' -ForegroundColor Red; Write-Host 'Please run: make install-deps' -ForegroundColor Yellow; exit 1 } else { Write-Host '✅ Socket server dependencies found' -ForegroundColor Green }"
	@echo ✅ All dependencies are installed

# Install all dependencies (Windows compatible)
install-deps:
	@echo Installing dependencies...
	@echo Setting up Python virtual environment...
	@powershell -Command "if (!(Test-Path 'backend\venv')) { cd backend; python -m venv venv; cd .. }"
	@echo Installing Python dependencies...
	@powershell -Command "cd backend; .\venv\Scripts\Activate.ps1; pip install -r requirements.txt; cd .."
	@echo Installing frontend dependencies...
	@cd frontend && pnpm install
	@echo Installing socket server dependencies...
	@cd socket-server && pnpm install
	@echo ✅ All dependencies installed successfully

# Start Django backend (Windows compatible)
backend:
	@echo Starting Django backend on port 6000...
	@powershell -Command "cd backend; .\venv\Scripts\Activate.ps1; python manage.py runserver 6000"

# Start React frontend
frontend:
	@echo Starting React frontend on port 5000...
	@cd frontend && pnpm run dev

# Start Socket server
socket-server:
	@echo Starting Socket server on port 7000...
	@cd socket-server && pnpm run dev

# Start all services in parallel (Windows compatible)
start: check-deps
	@echo 🚀 Starting Chat Application Services
	@echo ====================================
	@echo Starting all services in parallel...
	@echo Services will be available at:
	@echo   - Django Backend:  http://localhost:6000
	@echo   - React Frontend:  http://localhost:5000
	@echo   - Socket Server:   http://localhost:7000
	@echo.
	@echo Starting services in separate windows...
	@echo Press Ctrl+C in each window to stop services
	@echo.
	@start "Django Backend" cmd /k "cd backend && .\venv\Scripts\activate && python manage.py runserver 6000"
	@timeout /t 2 /nobreak >nul
	@start "React Frontend" cmd /k "cd frontend && pnpm run dev"
	@timeout /t 2 /nobreak >nul
	@start "Socket Server" cmd /k "cd socket-server && pnpm run dev"
	@echo ✅ All services started in separate command windows!
	@echo Check each window for service status and logs

# Stop all services (Windows compatible)
stop:
	@echo Stopping all services...
	@echo Please close the service windows manually or use Task Manager
	@echo to stop the following processes:
	@echo   - Django Backend (python.exe)
	@echo   - React Frontend (node.exe)
	@echo   - Socket Server (node.exe)
	@powershell -Command "Get-Process | Where-Object {$$_.ProcessName -eq 'python' -or $$_.ProcessName -eq 'node'} | Select-Object Id,ProcessName,CommandLine | Format-Table -AutoSize"

# Clean up logs and temporary files (Windows compatible)
clean:
	@echo Cleaning up...
	@powershell -Command "Remove-Item -Path '*.log' -Force -ErrorAction SilentlyContinue"
	@powershell -Command "Remove-Item -Path '*.pid' -Force -ErrorAction SilentlyContinue"
	@echo ✅ Cleanup completed

# Install dependencies and start all services
all: install-deps start

# Windows-specific targets (for cross-platform compatibility)
ifeq ($(OS),Windows_NT)
backend-win:
	@echo "Starting Django backend on port 6000..."
	@cd backend && venv\Scripts\activate && python manage.py runserver 6000

frontend-win:
	@echo "Starting React frontend on port 5000..."
	@cd frontend && pnpm run dev

socket-server-win:
	@echo "Starting Socket server on port 7000..."
	@cd socket-server && pnpm run dev

start-win: check-deps
	@echo "🚀 Starting Chat Application Services (Windows)"
	@echo "============================================="
	@echo "Starting services..."
	@start "Django Backend" cmd /k "cd backend && venv\Scripts\activate && python manage.py runserver 6000"
	@timeout /t 2 /nobreak >nul
	@start "React Frontend" cmd /k "cd frontend && pnpm run dev"
	@timeout /t 2 /nobreak >nul
	@start "Socket Server" cmd /k "cd socket-server && pnpm run dev"
	@echo "✅ All services started in separate windows"
	@echo "Services available at:"
	@echo "  - Django Backend:  http://localhost:6000"
	@echo "  - React Frontend:  http://localhost:5000"
	@echo "  - Socket Server:   http://localhost:7000"
endif