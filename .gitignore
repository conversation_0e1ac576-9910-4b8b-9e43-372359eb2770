# Chat Application .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
# media/media/*
staticfiles/

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# React Build
frontend/build/
frontend/dist/

# Socket Server Build
socket-server/dist/
socket-server/build/

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Prisma
socket-server/prisma/migrations/
socket-server/generated/

# Redis dump
dump.rdb

# Test files
test-results/
coverage/
*.lcov
.nyc_output/

# E2E Test Reports
e2e/reports/
e2e/playwright-report/
playwright-report/
frontend/playwright-report/

# Pytest cache
.pytest_cache/
__pycache__/

# Test and temporary files
test-file.txt
test.txt
response.txt
login.json
register.json
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Backup files
*.bak
*.backup

# Database files
*.db
*.sqlite
*.sqlite3

# Compiled TypeScript
*.js.map
*.d.ts

# Package lock files (choose one)
# package-lock.json
# yarn.lock

# Local configuration
config/local.json
config/development.json
config/production.json

# Generated documentation and reports
docs/generated/
reports/
*.html.report

# Temporary and cache directories
.cache/
.tmp/
tmp/
temp/

# Lock files and package manager artifacts
yarn.lock

# IDE and editor files
*.swp
*.swo
*~
.vscode/settings.json
.idea/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Vercel deployment
.vercel/

# Development artifacts
=41.0.0
run

# Test scripts and validation files
test_*.py
test_*.js
test_*.md
validate-*.sh
