// frontend/src/components/Chat/__tests__/MessageStatus.test.tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import MessageStatus from '../MessageStatus';
import type { MessageStatusType } from '../../../store/slices/messageSlice';

describe('MessageStatus', () => {
  const mockOnRetry = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('when not own message', () => {
    it('should not render anything', () => {
      const { container } = render(
        <MessageStatus
          status="DELIVERED"
          isOwnMessage={false}
        />
      );

      expect(container.firstChild).toBeNull();
    });
  });

  describe('when sending', () => {
    it('should show loading indicator', () => {
      render(
        <MessageStatus
          isOwnMessage={true}
          isSending={true}
        />
      );

      expect(screen.getByText('Sending...')).toBeInTheDocument();
      expect(screen.getByRole('img', { hidden: true })).toHaveClass('animate-spin');
    });
  });

  describe('status indicators', () => {
    it('should show gray check for SENT status', () => {
      render(
        <MessageStatus
          status="SENT"
          isOwnMessage={true}
        />
      );

      const icon = screen.getByRole('img', { hidden: true });
      expect(icon).toHaveClass('text-gray-400');
    });

    it('should show gray check for DELIVERED status', () => {
      render(
        <MessageStatus
          status="DELIVERED"
          isOwnMessage={true}
        />
      );

      const icon = screen.getByRole('img', { hidden: true });
      expect(icon).toHaveClass('text-gray-500');
    });

    it('should show green double check for READ status', () => {
      render(
        <MessageStatus
          status="READ"
          isOwnMessage={true}
        />
      );

      const icon = screen.getByRole('img', { hidden: true });
      expect(icon).toHaveClass('text-green-500');
    });

    it('should show retry button for FAILED status with onRetry', () => {
      render(
        <MessageStatus
          status="FAILED"
          isOwnMessage={true}
          onRetry={mockOnRetry}
        />
      );

      const retryButton = screen.getByRole('button');
      expect(retryButton).toBeInTheDocument();
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });

    it('should show error icon for FAILED status without onRetry', () => {
      render(
        <MessageStatus
          status="FAILED"
          isOwnMessage={true}
        />
      );

      const icon = screen.getByRole('img', { hidden: true });
      expect(icon).toHaveClass('text-red-500');
      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    it('should call onRetry when retry button is clicked', () => {
      render(
        <MessageStatus
          status="FAILED"
          isOwnMessage={true}
          onRetry={mockOnRetry}
        />
      );

      const retryButton = screen.getByRole('button');
      fireEvent.click(retryButton);

      expect(mockOnRetry).toHaveBeenCalledTimes(1);
    });

    it('should not render anything for undefined status', () => {
      const { container } = render(
        <MessageStatus
          isOwnMessage={true}
        />
      );

      expect(container.firstChild).toBeNull();
    });
  });

  describe('styling', () => {
    it('should apply custom className', () => {
      render(
        <MessageStatus
          status="DELIVERED"
          isOwnMessage={true}
          className="custom-class"
        />
      );

      const container = screen.getByRole('img', { hidden: true }).parentElement;
      expect(container).toHaveClass('custom-class');
    });

    it('should have proper hover styles for retry button', () => {
      render(
        <MessageStatus
          status="FAILED"
          isOwnMessage={true}
          onRetry={mockOnRetry}
        />
      );

      const retryButton = screen.getByRole('button');
      expect(retryButton).toHaveClass('hover:text-red-600');
    });
  });

  describe('accessibility', () => {
    it('should have proper title for retry button', () => {
      render(
        <MessageStatus
          status="FAILED"
          isOwnMessage={true}
          onRetry={mockOnRetry}
        />
      );

      const retryButton = screen.getByRole('button');
      expect(retryButton).toHaveAttribute('title', 'Retry sending message');
    });

    it('should have proper title for failed icon', () => {
      render(
        <MessageStatus
          status="FAILED"
          isOwnMessage={true}
        />
      );

      const icon = screen.getByRole('img', { hidden: true });
      expect(icon).toHaveAttribute('title', 'Failed to send');
    });
  });
});
