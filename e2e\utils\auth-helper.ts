import { Page } from '@playwright/test';

export class AuthHelper {
  async loginUser(page: Page, email: string, password: string): Promise<void> {
    await page.goto('http://localhost:5000');
    
    // Wait for login form to be visible
    await page.waitForSelector('form', { timeout: 10000 });
    
    // Fill login form
    await page.fill('[data-testid="email-input"]', email);
    await page.fill('[data-testid="password-input"]', password);
    
    // Submit login
    await page.click('[data-testid="login-button"]');
    
    // Wait for successful login (dashboard header should be visible)
    await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });
    
    // Wait for socket connection
    await page.waitForSelector('[data-testid="connection-status"]:has-text("Connected")', { timeout: 15000 });
  }
}
