#!/bin/bash

# Test runner script for the chat application
# This script runs tests for all components of the application

echo "🧪 Running Chat Application Tests"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results
DJANGO_TESTS_PASSED=false
INTEGRATION_TESTS_PASSED=false

echo -e "\n${YELLOW}1. Running Django Backend Tests...${NC}"
echo "-----------------------------------"

# Navigate to backend directory and run Django tests
cd backend
if source ../venv/bin/activate && python manage.py test; then
    echo -e "${GREEN}✅ Django tests passed${NC}"
    DJANGO_TESTS_PASSED=true
else
    echo -e "${RED}❌ Django tests failed${NC}"
fi

# Go back to root directory
cd ..

echo -e "\n${YELLOW}2. Running Integration Tests...${NC}"
echo "-------------------------------"

# Run integration tests
if python3 test_integration.py; then
    echo -e "${GREEN}✅ Integration tests passed${NC}"
    INTEGRATION_TESTS_PASSED=true
else
    echo -e "${RED}❌ Integration tests failed${NC}"
fi

# Summary
echo -e "\n${YELLOW}Test Summary${NC}"
echo "============"

if [ "$DJANGO_TESTS_PASSED" = true ]; then
    echo -e "${GREEN}✅ Django Backend Tests: PASSED${NC}"
else
    echo -e "${RED}❌ Django Backend Tests: FAILED${NC}"
fi

if [ "$INTEGRATION_TESTS_PASSED" = true ]; then
    echo -e "${GREEN}✅ Integration Tests: PASSED${NC}"
else
    echo -e "${RED}❌ Integration Tests: FAILED${NC}"
fi

# Overall result
if [ "$DJANGO_TESTS_PASSED" = true ] && [ "$INTEGRATION_TESTS_PASSED" = true ]; then
    echo -e "\n${GREEN}🎉 All tests passed! The application is ready for use.${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some tests failed. Please check the output above for details.${NC}"
    exit 1
fi
