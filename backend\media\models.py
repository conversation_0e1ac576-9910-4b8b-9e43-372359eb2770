# backend/media/models.py
import uuid
import os
from django.db import models
from django.contrib.auth import get_user_model
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
import json

User = get_user_model()

class MediaFile(models.Model):
    MEDIA_TYPES = [
        ('image', 'Image'),
        ('document', 'Document'),
        ('audio', 'Audio'),
        ('video', 'Video'),
        ('archive', 'Archive'),
        ('other', 'Other'),
    ]

    PROCESSING_STATUS = [
        ('temporary', 'Temporary'),
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey('messaging.Message', on_delete=models.CASCADE, related_name='media_files', null=True, blank=True)
    uploader = models.ForeignKey(User, on_delete=models.CASCADE, related_name='uploaded_media')

    # File information
    original_filename = models.CharField(max_length=255)
    file_type = models.CharField(max_length=10, choices=MEDIA_TYPES)
    mime_type = models.CharField(max_length=100)
    file_size = models.BigIntegerField()  # Size in bytes

    # Storage paths (all encrypted)
    encrypted_file_path = models.CharField(max_length=500)
    encrypted_thumbnail_path = models.CharField(max_length=500, blank=True, null=True)

    # E2EE Encryption (client-side encrypted, wrapped with conversation key)
    wrapped_file_key = models.TextField()  # File key encrypted with conversation key
    file_nonce = models.TextField()        # AES-GCM nonce for file encryption
    thumbnail_nonce = models.TextField(blank=True, null=True)  # Nonce for thumbnail

    # Processing
    processing_status = models.CharField(max_length=15, choices=PROCESSING_STATUS, default='pending')
    processing_error = models.TextField(blank=True, null=True)

    # Metadata (encrypted)
    encrypted_metadata = models.TextField(blank=True, null=True)  # JSON metadata, encrypted

    # Security
    virus_scan_status = models.CharField(max_length=20, default='pending')
    virus_scan_result = models.TextField(blank=True, null=True)
    virus_scan_hash = models.CharField(max_length=64, blank=True, null=True)  # SHA256 of original file

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'media_files'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['message', 'file_type']),
            models.Index(fields=['uploader', 'created_at']),
            models.Index(fields=['virus_scan_status']),
        ]

    def __str__(self):
        return f"{self.original_filename} ({self.file_type})"

    @property
    def file_extension(self):
        return os.path.splitext(self.original_filename)[1].lower()

    @property
    def is_image(self):
        return self.file_type == 'image'

    @property
    def is_video(self):
        return self.file_type == 'video'

    @property
    def has_thumbnail(self):
        return bool(self.encrypted_thumbnail_path)


class MediaDownload(models.Model):
    """Track media downloads for analytics and security - IMPROVED"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    media_file = models.ForeignKey(MediaFile, on_delete=models.CASCADE, related_name='downloads')
    downloaded_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='media_downloads')
    download_token = models.CharField(max_length=100, unique=True)
    expires_at = models.DateTimeField()

    # FIXED: Allow multiple downloads until expiry
    download_count = models.IntegerField(default=0)
    max_downloads = models.IntegerField(default=10)  # Prevent abuse
    last_downloaded_at = models.DateTimeField(null=True, blank=True)

    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'media_downloads'
        indexes = [
            models.Index(fields=['download_token', 'expires_at']),
        ]

    @property
    def is_expired(self):
        from django.utils import timezone
        return timezone.now() > self.expires_at

    @property
    def can_download(self):
        return not self.is_expired and self.download_count < self.max_downloads


class MediaChunk(models.Model):
    """Handle chunked uploads for large files"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    upload_session = models.CharField(max_length=100, db_index=True)
    chunk_number = models.IntegerField()
    total_chunks = models.IntegerField()
    chunk_data = models.BinaryField()  # Encrypted chunk data
    chunk_hash = models.CharField(max_length=64)  # SHA256 for integrity
    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'media_chunks'
        unique_together = ['upload_session', 'chunk_number']
        indexes = [
            models.Index(fields=['upload_session', 'chunk_number']),
        ]


class MediaProcessingJob(models.Model):
    """Track background media processing jobs - IMPROVED"""
    JOB_TYPES = [
        ('virus_scan', 'Virus Scanning'),
        ('thumbnail', 'Thumbnail Generation'),
        ('metadata_extraction', 'Metadata Extraction'),
        ('compression', 'File Compression'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    media_file = models.ForeignKey(MediaFile, on_delete=models.CASCADE, related_name='processing_jobs')
    job_type = models.CharField(max_length=20, choices=JOB_TYPES)
    status = models.CharField(max_length=15, choices=MediaFile.PROCESSING_STATUS, default='pending')
    priority = models.IntegerField(default=5)  # 1=highest, 10=lowest

    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True, null=True)
    result_data = models.JSONField(default=dict)
    retry_count = models.IntegerField(default=0)
    max_retries = models.IntegerField(default=3)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'media_processing_jobs'
        ordering = ['priority', 'created_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['job_type', 'status']),
        ]


# FIXED: Optimize Message media count updates with signals
@receiver(post_save, sender=MediaFile)
@receiver(post_delete, sender=MediaFile)
def update_message_media_count(sender, instance, **kwargs):
    """Update message media count when MediaFile is created/deleted"""
    if instance.message_id:
        from django.db.models import Count
        from messaging.models import Message

        try:
            message = Message.objects.get(id=instance.message_id)
            media_count = MediaFile.objects.filter(message=message).count()
            Message.objects.filter(id=message.id).update(
                has_media=media_count > 0,
                media_count=media_count
            )
        except Message.DoesNotExist:
            pass
