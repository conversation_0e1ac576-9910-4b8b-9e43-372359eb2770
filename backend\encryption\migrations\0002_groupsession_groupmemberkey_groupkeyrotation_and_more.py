# Generated by Django 5.2.4 on 2025-08-30 08:34

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('encryption', '0001_initial'),
        ('messaging', '0004_groupevent_groupinvite_conversation_avatar_url_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='GroupSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_id', models.CharField(max_length=100, unique=True)),
                ('current_epoch', models.IntegerField(default=0, help_text='Current key rotation epoch')),
                ('encrypted_group_key', models.TextField(help_text='AES-256-GCM group key encrypted with server master key')),
                ('group_key_iv', models.Char<PERSON>ield(help_text='IV for group key encryption (base64)', max_length=32)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('conversation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='group_session', to='messaging.conversation')),
            ],
            options={
                'verbose_name': 'Group Session',
                'verbose_name_plural': 'Group Sessions',
                'db_table': 'group_sessions',
            },
        ),
        migrations.CreateModel(
            name='GroupMemberKey',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('ephemeral_public_key', models.TextField(help_text='X25519 ephemeral public key (base64)')),
                ('encrypted_group_key', models.TextField(help_text='Group key encrypted with AES-256-GCM (base64)')),
                ('auth_tag', models.CharField(help_text='AES-GCM authentication tag (base64)', max_length=32)),
                ('iv', models.CharField(help_text='AES-GCM initialization vector (base64)', max_length=32)),
                ('epoch', models.IntegerField(default=0, help_text='Key rotation epoch')),
                ('is_claimed', models.BooleanField(default=False, help_text='Whether member has retrieved this key')),
                ('claimed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='group_member_keys', to=settings.AUTH_USER_MODEL)),
                ('group_session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='member_keys', to='encryption.groupsession')),
            ],
            options={
                'verbose_name': 'Group Member Key',
                'verbose_name_plural': 'Group Member Keys',
                'db_table': 'group_member_keys',
            },
        ),
        migrations.CreateModel(
            name='GroupKeyRotation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('old_epoch', models.IntegerField(help_text='Previous epoch number')),
                ('new_epoch', models.IntegerField(help_text='New epoch number')),
                ('reason', models.CharField(help_text="Reason for rotation: 'member_added', 'member_removed', 'scheduled', 'manual'", max_length=50)),
                ('old_key_hash', models.CharField(help_text='SHA-256 hash of old group key', max_length=64)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('rotated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='initiated_rotations', to=settings.AUTH_USER_MODEL)),
                ('group_session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='key_rotations', to='encryption.groupsession')),
            ],
            options={
                'verbose_name': 'Group Key Rotation',
                'verbose_name_plural': 'Group Key Rotations',
                'db_table': 'group_key_rotations',
            },
        ),
        migrations.CreateModel(
            name='GroupMessageSignature',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('sender_signature', models.TextField(help_text='ECDSA signature over message content + metadata (base64)')),
                ('signature_data', models.JSONField(help_text='Signed data: {content_hash, sender_id, timestamp, epoch}')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('message', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='group_signature', to='messaging.message')),
            ],
            options={
                'verbose_name': 'Group Message Signature',
                'verbose_name_plural': 'Group Message Signatures',
                'db_table': 'group_message_signatures',
                'indexes': [models.Index(fields=['message'], name='group_messa_message_511f3d_idx'), models.Index(fields=['created_at'], name='group_messa_created_a7fdc9_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='groupsession',
            index=models.Index(fields=['conversation'], name='group_sessi_convers_28b68a_idx'),
        ),
        migrations.AddIndex(
            model_name='groupsession',
            index=models.Index(fields=['current_epoch'], name='group_sessi_current_8cded8_idx'),
        ),
        migrations.AddIndex(
            model_name='groupsession',
            index=models.Index(fields=['session_id'], name='group_sessi_session_27e8bf_idx'),
        ),
        migrations.AddIndex(
            model_name='groupmemberkey',
            index=models.Index(fields=['group_session', 'member', 'is_claimed'], name='group_membe_group_s_0065a0_idx'),
        ),
        migrations.AddIndex(
            model_name='groupmemberkey',
            index=models.Index(fields=['epoch', 'created_at'], name='group_membe_epoch_fa7541_idx'),
        ),
        migrations.AddIndex(
            model_name='groupmemberkey',
            index=models.Index(fields=['is_claimed', 'created_at'], name='group_membe_is_clai_e556a7_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='groupmemberkey',
            unique_together={('group_session', 'member', 'epoch')},
        ),
        migrations.AddIndex(
            model_name='groupkeyrotation',
            index=models.Index(fields=['group_session', 'new_epoch'], name='group_key_r_group_s_a4a619_idx'),
        ),
        migrations.AddIndex(
            model_name='groupkeyrotation',
            index=models.Index(fields=['created_at'], name='group_key_r_created_45d4d5_idx'),
        ),
        migrations.AddIndex(
            model_name='groupkeyrotation',
            index=models.Index(fields=['rotated_by', 'created_at'], name='group_key_r_rotated_48c12f_idx'),
        ),
    ]
