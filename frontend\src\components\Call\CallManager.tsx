// frontend/src/components/Call/CallManager.tsx
import React from 'react';
import { useCalling } from '../../contexts/CallingContext';
import { IncomingCallModal } from './IncomingCallModal';
import { ActiveCallModal } from './ActiveCallModal';

export const CallManager: React.FC = () => {
  const { activeCall, error, clearError } = useCalling();

  // Show error notification if there's an error
  React.useEffect(() => {
    if (error) {
      // You can replace this with a proper toast notification
      console.error('Call error:', error);
      
      // Auto-clear error after 5 seconds
      const timeout = setTimeout(() => {
        clearError();
      }, 5000);

      return () => clearTimeout(timeout);
    }
  }, [error, clearError]);

  const isIncomingCall = activeCall?.isIncoming && 
    (activeCall.status === 'ringing' || activeCall.status === 'initiating');
  
  const isActiveCall = activeCall && 
    (activeCall.status === 'connecting' || activeCall.status === 'active');

  return (
    <>
      {/* Incoming call modal */}
      <IncomingCallModal isOpen={isIncomingCall} />
      
      {/* Active call modal */}
      <ActiveCallModal isOpen={isActiveCall} />
      
      {/* Error notification */}
      {error && (
        <div className="fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <button
              onClick={clearError}
              className="ml-2 text-white hover:text-gray-200"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </>
  );
};
