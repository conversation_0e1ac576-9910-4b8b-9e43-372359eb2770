# backend/encryption/schemas.py
from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List
from datetime import datetime
import uuid
import base64
import re


class KeyBundleUpload(BaseModel):
    """Schema for uploading a user's key bundle"""
    identity_public_key: str = Field(
        ..., 
        description="ECDSA P-256 identity public key (SPKI format, base64 encoded)"
    )
    signed_prekey_id: int = Field(
        ..., 
        ge=0,
        description="Signed pre-key identifier"
    )
    signed_prekey_public: str = Field(
        ..., 
        description="ECDH P-256 signed pre-key public key (SPKI format, base64 encoded)"
    )
    signed_prekey_signature: str = Field(
        ..., 
        description="ECDSA signature over signed pre-key SPKI bytes (base64 encoded)"
    )
    
    @validator('identity_public_key', 'signed_prekey_public', 'signed_prekey_signature')
    def validate_base64(cls, v):
        """Validate base64 encoding"""
        try:
            base64.b64decode(v)
            return v
        except Exception:
            raise ValueError('Invalid base64 encoding')
    
    @validator('identity_public_key', 'signed_prekey_public')
    def validate_spki_format(cls, v):
        """Basic validation of SPKI format (starts with correct header)"""
        try:
            decoded = base64.b64decode(v)
            # SPKI for P-256 should start with specific ASN.1 sequence
            if len(decoded) < 50:  # Minimum reasonable size
                raise ValueError('Key too short for SPKI format')
            return v
        except Exception:
            raise ValueError('Invalid SPKI key format')


class OneTimePreKeyUpload(BaseModel):
    """Schema for uploading one-time pre-keys"""
    key_id: int = Field(..., ge=0, description="One-time pre-key identifier")
    public_key: str = Field(
        ..., 
        description="ECDH P-256 public key (SPKI format, base64 encoded)"
    )
    
    @validator('public_key')
    def validate_public_key(cls, v):
        """Validate public key format"""
        try:
            decoded = base64.b64decode(v)
            if len(decoded) < 50:
                raise ValueError('Key too short for SPKI format')
            return v
        except Exception:
            raise ValueError('Invalid public key format')


class OneTimePreKeyBatch(BaseModel):
    """Schema for batch uploading one-time pre-keys"""
    prekeys: List[OneTimePreKeyUpload] = Field(
        ..., 
        min_items=1, 
        max_items=100,
        description="List of one-time pre-keys to upload"
    )
    
    @validator('prekeys')
    def validate_unique_key_ids(cls, v):
        """Ensure all key IDs are unique in the batch"""
        key_ids = [pk.key_id for pk in v]
        if len(key_ids) != len(set(key_ids)):
            raise ValueError('Duplicate key IDs in batch')
        return v


class KeyBundleResponse(BaseModel):
    """Schema for key bundle response"""
    identity_public_key: str
    signed_prekey: dict = Field(
        ...,
        description="Signed pre-key information"
    )
    one_time_prekey: Optional[dict] = Field(
        None,
        description="One-time pre-key (if available)"
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "identity_public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE...",
                "signed_prekey": {
                    "id": 1,
                    "public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE...",
                    "signature": "MEUCIQDxyz..."
                },
                "one_time_prekey": {
                    "id": 42,
                    "public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE..."
                }
            }
        }


class EncryptedMessageCreate(BaseModel):
    """Schema for creating encrypted messages"""
    conversation_id: uuid.UUID = Field(..., description="Target conversation ID")
    encrypted_content: str = Field(
        ..., 
        description="AES-GCM encrypted message content (base64 encoded)"
    )
    iv: str = Field(
        ..., 
        description="96-bit initialization vector (base64 encoded)"
    )
    sender_ratchet_key: str = Field(
        ..., 
        description="Sender's current DH ratchet public key (SPKI format, base64)"
    )
    message_number: int = Field(
        ..., 
        ge=0,
        description="Message counter in Double Ratchet (N)"
    )
    previous_chain_length: int = Field(
        ..., 
        ge=0,
        description="Previous chain length in Double Ratchet (PN)"
    )
    message_type: str = Field(
        default="TEXT",
        pattern="^(TEXT|IMAGE|FILE|SYSTEM)$",
        description="Message type"
    )
    
    @validator('encrypted_content', 'iv', 'sender_ratchet_key')
    def validate_base64_fields(cls, v):
        """Validate base64 encoding for encrypted fields"""
        try:
            base64.b64decode(v)
            return v
        except Exception:
            raise ValueError('Invalid base64 encoding')
    
    @validator('iv')
    def validate_iv_length(cls, v):
        """Validate IV is 96 bits (12 bytes)"""
        try:
            decoded = base64.b64decode(v)
            if len(decoded) != 12:
                raise ValueError('IV must be exactly 96 bits (12 bytes)')
            return v
        except Exception:
            raise ValueError('Invalid IV format')


class EncryptedMessageResponse(BaseModel):
    """Schema for encrypted message response"""
    id: uuid.UUID
    conversation_id: uuid.UUID
    sender_id: uuid.UUID
    encrypted_content: str
    iv: str
    sender_ratchet_key: str
    message_number: int
    previous_chain_length: int
    message_type: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class SessionInitRequest(BaseModel):
    """Schema for initializing a session with another user"""
    target_user_id: uuid.UUID = Field(..., description="User to establish session with")
    conversation_id: uuid.UUID = Field(..., description="Conversation ID")
    ephemeral_public_key: str = Field(
        ..., 
        description="Ephemeral ECDH public key for X3DH (SPKI format, base64)"
    )
    
    @validator('ephemeral_public_key')
    def validate_ephemeral_key(cls, v):
        """Validate ephemeral public key format"""
        try:
            decoded = base64.b64decode(v)
            if len(decoded) < 50:
                raise ValueError('Key too short for SPKI format')
            return v
        except Exception:
            raise ValueError('Invalid ephemeral key format')


class SessionInitResponse(BaseModel):
    """Schema for session initialization response"""
    session_id: uuid.UUID
    target_user_bundle: KeyBundleResponse
    success: bool
    message: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "550e8400-e29b-41d4-a716-************",
                "target_user_bundle": {
                    "identity_public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE...",
                    "signed_prekey": {
                        "id": 1,
                        "public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE...",
                        "signature": "MEUCIQDxyz..."
                    }
                },
                "success": True,
                "message": "Session initialized successfully"
            }
        }


class ErrorResponse(BaseModel):
    """Standard error response schema"""
    error: str = Field(..., description="Error message")
    details: Optional[dict] = Field(None, description="Additional error details")
    code: Optional[str] = Field(None, description="Error code")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": "Invalid signature",
                "details": {"field": "signed_prekey_signature"},
                "code": "SIGNATURE_INVALID"
            }
        }
