// frontend/src/components/Call/CallControls.tsx
import React, { useState, useEffect } from 'react';
import { Phone, Video, AlertCircle } from 'lucide-react';
import { useCalling } from '../../contexts/CallingContext';
import { useSocket } from '../../contexts/SocketContext';
import { WebRTCManager } from '../../utils/webrtc';

interface CallControlsProps {
  conversationId: string;
  otherParticipant: {
    id: string;
    username: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
  disabled?: boolean;
}

export const CallControls: React.FC<CallControlsProps> = ({
  conversationId,
  otherParticipant,
  disabled = false,
}) => {
  const { activeCall, initiateCall } = useCalling();
  const { socket } = useSocket();
  const [mediaAvailability, setMediaAvailability] = useState<{
    audio: boolean;
    video: boolean;
    error?: string;
    checking: boolean;
  }>({ audio: false, video: false, checking: true });

  // Skip media availability check for API testing
  useEffect(() => {
    // Set media as available for testing purposes
    setMediaAvailability({
      audio: true,
      video: true,
      checking: false
    });
  }, []);

  const handleAudioCall = async () => {
    if (disabled || activeCall) return;
    
    try {
      console.log('🎯 Testing audio call API without media permissions');
      await initiateCall(conversationId, 'audio', otherParticipant);
    } catch (error) {
      console.error('Failed to initiate audio call:', error);
    }
  };

  const handleVideoCall = async () => {
    if (disabled || activeCall) return;
    
    try {
      console.log('🎯 Testing video call API without media permissions');
      await initiateCall(conversationId, 'video', otherParticipant);
    } catch (error) {
      console.error('Failed to initiate video call:', error);
    }
  };

  const isCallInProgress = activeCall && activeCall.status !== 'idle';
  const { audio: hasAudio, video: hasVideo, error: mediaError, checking } = mediaAvailability;

  // Show loading state while checking media
  if (checking) {
    return (
      <div className="flex items-center space-x-2">
        <div className="animate-pulse bg-gray-200 rounded-lg w-9 h-9"></div>
        <div className="animate-pulse bg-gray-200 rounded-lg w-9 h-9"></div>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      {/* Audio call button */}
      <button
        onClick={handleAudioCall}
        disabled={disabled || isCallInProgress}
        className={`p-2 rounded-lg transition-colors ${
          disabled || isCallInProgress
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-gray-600 hover:text-green-600 hover:bg-green-50'
        }`}
        title="Start audio call (API test mode)"
      >
        <Phone className="w-5 h-5" />
      </button>

      {/* Video call button */}
      <button
        onClick={handleVideoCall}
        disabled={disabled || isCallInProgress}
        className={`p-2 rounded-lg transition-colors ${
          disabled || isCallInProgress
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
        }`}
        title="Start video call (API test mode)"
      >
        <Video className="w-5 h-5" />
      </button>
    </div>
  );
};
