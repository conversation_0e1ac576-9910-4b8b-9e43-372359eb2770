// frontend/src/components/Chat/MediaUpload.tsx
import React, { useState, useRef, useCallback } from 'react';
import { Upload, X, File, Image, Video, Music, Archive, FileText } from 'lucide-react';
import { mediaApi, type UploadProgress } from '../../services/mediaApi';
import { useSocket } from '../../contexts/SocketContext';

interface MediaUploadProps {
  conversationId: string;
  messageId: string;
  onUploadStart?: (file: File) => void;
  onUploadProgress?: (progress: UploadProgress) => void;
  onUploadComplete?: (mediaFile: any) => void;
  onUploadError?: (error: string) => void;
  disabled?: boolean;
}

interface FilePreview {
  file: File;
  id: string;
  preview?: string;
  uploading: boolean;
  progress: number;
  error?: string;
}

export const MediaUpload: React.FC<MediaUploadProps> = ({
  conversationId,
  messageId,
  onUploadStart,
  onUploadProgress,
  onUploadComplete,
  onUploadError,
  disabled = false
}) => {
  const [files, setFiles] = useState<FilePreview[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { socket } = useSocket();

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'image': return <Image className="w-8 h-8 text-blue-500" />;
      case 'video': return <Video className="w-8 h-8 text-purple-500" />;
      case 'audio': return <Music className="w-8 h-8 text-green-500" />;
      case 'archive': return <Archive className="w-8 h-8 text-orange-500" />;
      case 'document': return <FileText className="w-8 h-8 text-red-500" />;
      default: return <File className="w-8 h-8 text-gray-500" />;
    }
  };

  const createFilePreview = useCallback(async (file: File): Promise<FilePreview> => {
    const id = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    let preview: string | undefined;

    // Create preview for images
    if (file.type.startsWith('image/')) {
      preview = URL.createObjectURL(file);
    }

    return {
      file,
      id,
      preview,
      uploading: false,
      progress: 0
    };
  }, []);

  const handleFiles = useCallback(async (fileList: FileList) => {
    if (disabled) return;

    const newFiles: FilePreview[] = [];
    
    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      
      // Validate file
      const validation = mediaApi.validateFile(file);
      if (!validation.valid) {
        onUploadError?.(validation.error || 'Invalid file');
        continue;
      }

      const filePreview = await createFilePreview(file);
      newFiles.push(filePreview);
    }

    setFiles(prev => [...prev, ...newFiles]);
  }, [disabled, createFilePreview, onUploadError]);

  const uploadFile = useCallback(async (filePreview: FilePreview) => {
    try {
      // Update file state to uploading
      setFiles(prev => prev.map(f => 
        f.id === filePreview.id 
          ? { ...f, uploading: true, progress: 0, error: undefined }
          : f
      ));

      onUploadStart?.(filePreview.file);

      // Notify socket that upload started
      socket?.emit('media_upload_started', {
        conversationId,
        messageId,
        fileName: filePreview.file.name,
        fileSize: filePreview.file.size,
        fileType: mediaApi.getFileType(filePreview.file.type),
        tempId: filePreview.id
      });

      // Generate encryption key and nonce (placeholder - integrate with encryption context)
      const fileKey = crypto.getRandomValues(new Uint8Array(32));
      const nonce = crypto.getRandomValues(new Uint8Array(12));
      
      // Convert to base64 for API
      const wrappedFileKey = btoa(String.fromCharCode(...fileKey));
      const fileNonce = btoa(String.fromCharCode(...nonce));
      
      // Calculate file hash
      const fileBuffer = await filePreview.file.arrayBuffer();
      const hashBuffer = await crypto.subtle.digest('SHA-256', fileBuffer);
      const fileHash = Array.from(new Uint8Array(hashBuffer))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');

      const progressHandler = (progress: UploadProgress) => {
        setFiles(prev => prev.map(f => 
          f.id === filePreview.id 
            ? { ...f, progress: progress.percentage }
            : f
        ));

        onUploadProgress?.(progress);

        // Notify socket of progress
        socket?.emit('media_upload_progress', {
          conversationId,
          messageId,
          progress: progress.percentage,
          tempId: filePreview.id
        });
      };

      // Choose upload method based on file size
      let result;
      if (mediaApi.shouldUseChunkedUpload(filePreview.file)) {
        result = await mediaApi.uploadChunked(
          messageId,
          filePreview.file,
          wrappedFileKey,
          fileNonce,
          fileHash,
          progressHandler
        );
      } else {
        result = await mediaApi.uploadSimple(
          messageId,
          filePreview.file,
          wrappedFileKey,
          fileNonce,
          fileHash,
          progressHandler
        );
      }

      // Update file state to completed
      setFiles(prev => prev.map(f => 
        f.id === filePreview.id 
          ? { ...f, uploading: false, progress: 100 }
          : f
      ));

      onUploadComplete?.(result);

      // Notify socket that upload completed
      socket?.emit('media_upload_completed', {
        conversationId,
        messageId,
        mediaFileId: result.id,
        tempId: filePreview.id
      });

      // Remove file from preview after successful upload
      setTimeout(() => {
        setFiles(prev => prev.filter(f => f.id !== filePreview.id));
      }, 2000);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      
      // Update file state with error
      setFiles(prev => prev.map(f => 
        f.id === filePreview.id 
          ? { ...f, uploading: false, error: errorMessage }
          : f
      ));

      onUploadError?.(errorMessage);

      // Notify socket that upload failed
      socket?.emit('media_upload_failed', {
        conversationId,
        messageId,
        error: errorMessage,
        tempId: filePreview.id
      });
    }
  }, [conversationId, messageId, socket, onUploadStart, onUploadProgress, onUploadComplete, onUploadError]);

  const removeFile = useCallback((fileId: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === fileId);
      if (file?.preview) {
        URL.revokeObjectURL(file.preview);
      }
      return prev.filter(f => f.id !== fileId);
    });
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      handleFiles(droppedFiles);
    }
  }, [disabled, handleFiles]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      handleFiles(selectedFiles);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  }, [handleFiles]);

  const openFileDialog = useCallback(() => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  }, [disabled]);

  return (
    <div className="media-upload">
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        onChange={handleFileSelect}
        className="hidden"
        accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar,.7z"
      />

      {/* Upload button */}
      <button
        onClick={openFileDialog}
        disabled={disabled}
        className={`p-2 rounded-lg transition-colors ${
          disabled 
            ? 'text-gray-400 cursor-not-allowed' 
            : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
        }`}
        title="Upload file"
      >
        <Upload className="w-5 h-5" />
      </button>

      {/* Drag and drop overlay */}
      {isDragOver && (
        <div
          className="fixed inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center z-50"
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="bg-white rounded-lg p-8 shadow-lg border-2 border-dashed border-blue-500">
            <Upload className="w-12 h-12 text-blue-500 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-700">Drop files here to upload</p>
          </div>
        </div>
      )}

      {/* File previews */}
      {files.length > 0 && (
        <div className="absolute bottom-full left-0 right-0 mb-2 bg-white border rounded-lg shadow-lg p-4 max-h-64 overflow-y-auto z-10">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Uploading files</h4>
          <div className="space-y-3">
            {files.map((filePreview) => (
              <div key={filePreview.id} className="flex items-center space-x-3">
                {/* File icon or preview */}
                <div className="flex-shrink-0">
                  {filePreview.preview ? (
                    <img 
                      src={filePreview.preview} 
                      alt={filePreview.file.name}
                      className="w-10 h-10 object-cover rounded"
                    />
                  ) : (
                    getFileIcon(mediaApi.getFileType(filePreview.file.type))
                  )}
                </div>

                {/* File info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-700 truncate">
                    {filePreview.file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {mediaApi.formatFileSize(filePreview.file.size)}
                  </p>
                  
                  {/* Progress bar */}
                  {filePreview.uploading && (
                    <div className="mt-1">
                      <div className="bg-gray-200 rounded-full h-1">
                        <div 
                          className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${filePreview.progress}%` }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {filePreview.progress}% uploaded
                      </p>
                    </div>
                  )}

                  {/* Error message */}
                  {filePreview.error && (
                    <p className="text-xs text-red-500 mt-1">{filePreview.error}</p>
                  )}
                </div>

                {/* Actions */}
                <div className="flex-shrink-0 flex space-x-2">
                  {!filePreview.uploading && !filePreview.error && (
                    <button
                      onClick={() => uploadFile(filePreview)}
                      className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                    >
                      Upload
                    </button>
                  )}
                  <button
                    onClick={() => removeFile(filePreview.id)}
                    className="text-gray-400 hover:text-red-500"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaUpload;
