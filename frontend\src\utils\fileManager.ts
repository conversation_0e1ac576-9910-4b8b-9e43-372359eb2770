// frontend/src/utils/fileManager.ts

/**
 * File Manager utility for handling File objects in Redux state
 * Since File objects are not serializable, we store them separately and reference them by ID
 */

class FileManager {
  private files: Map<string, File> = new Map();
  private previews: Map<string, string> = new Map();

  /**
   * Store a file and return a unique file ID
   * @param file - The File object to store
   * @param preferredId - Optional preferred ID, will generate one if not provided or if ID already exists
   * @returns The file ID that can be used to retrieve the file later
   */
  storeFile(file: File, preferredId?: string): string {
    let fileId = preferredId;

    // Generate a unique ID if none provided or if the preferred ID already exists
    if (!fileId || this.files.has(fileId)) {
      fileId = this.generateFileId();
    }

    this.files.set(fileId, file);
    return fileId;
  }

  /**
   * Store multiple files and return their IDs
   * @param files - Array of File objects to store
   * @returns Array of file IDs
   */
  storeFiles(files: File[]): string[] {
    return files.map(file => this.storeFile(file));
  }

  /**
   * Retrieve a file by its ID
   * @param fileId - The file ID
   * @returns The File object or undefined if not found
   */
  getFile(fileId: string): File | undefined {
    return this.files.get(fileId);
  }

  /**
   * Remove a file from storage
   * @param fileId - The file ID to remove
   * @returns True if the file was removed, false if it didn't exist
   */
  removeFile(fileId: string): boolean {
    return this.files.delete(fileId);
  }

  /**
   * Get all stored file IDs
   * @returns Array of all file IDs
   */
  getAllFileIds(): string[] {
    return Array.from(this.files.keys());
  }

  /**
   * Get the number of stored files
   * @returns Number of files in storage
   */
  getFileCount(): number {
    return this.files.size;
  }

  /**
   * Store a preview URL for a file
   * @param fileId - The file ID
   * @param previewUrl - The preview URL to store
   */
  storePreview(fileId: string, previewUrl: string): void {
    // Revoke existing preview if it exists
    const existingPreview = this.previews.get(fileId);
    if (existingPreview) {
      URL.revokeObjectURL(existingPreview);
    }
    this.previews.set(fileId, previewUrl);
  }

  /**
   * Get a preview URL for a file
   * @param fileId - The file ID
   * @returns The preview URL or undefined if not found
   */
  getPreview(fileId: string): string | undefined {
    return this.previews.get(fileId);
  }

  /**
   * Create a preview URL for a file (if it's an image or video)
   * @param fileId - The file ID
   * @returns The preview URL or undefined if preview cannot be created
   */
  createPreview(fileId: string): string | undefined {
    const file = this.getFile(fileId);
    if (!file) return undefined;

    if (file.type.startsWith('image/') || file.type.startsWith('video/')) {
      try {
        const previewUrl = URL.createObjectURL(file);
        this.storePreview(fileId, previewUrl);
        return previewUrl;
      } catch (error) {
        console.warn('Failed to create preview for file:', file.name, error);
        return undefined;
      }
    }

    return undefined;
  }

  /**
   * Clear all stored files
   */
  clear(): void {
    // Revoke all preview URLs to prevent memory leaks
    this.previews.forEach(preview => URL.revokeObjectURL(preview));
    this.files.clear();
    this.previews.clear();
  }

  /**
   * Check if a file exists
   * @param fileId - The file ID to check
   * @returns True if the file exists
   */
  hasFile(fileId: string): boolean {
    return this.files.has(fileId);
  }

  /**
   * Generate a unique file ID
   * @returns A unique file ID
   */
  private generateFileId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    return `file_${timestamp}_${random}`;
  }
}

// Export a singleton instance
export const fileManager = new FileManager();

// Export the class for testing purposes
export { FileManager };

// Export types for better TypeScript support
export interface FileManagerInterface {
  storeFile(file: File, preferredId?: string): string;
  getFile(fileId: string): File | undefined;
  removeFile(fileId: string): boolean;
  getAllFileIds(): string[];
  getFileCount(): number;
  clear(): void;
  hasFile(fileId: string): boolean;
}