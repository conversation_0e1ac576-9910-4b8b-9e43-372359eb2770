// frontend/src/types/encryption.ts
/**
 * TypeScript types for Phase 3 end-to-end encryption.
 * These types match the backend Pydantic schemas and socket event schemas.
 */

// ============================================================================
// Core Encryption Types
// ============================================================================

export interface KeyPair {
  publicKey: CryptoKey;
  privateKey: CryptoKey;
}

export interface SerializedKeyPair {
  publicKey: string; // Base64 encoded SPKI format
  privateKey: string; // Base64 encoded PKCS#8 format
}

export interface IdentityKeyPair extends KeyPair {
  // ECDSA P-256 key pair for signing
}

export interface PreKeyPair extends KeyPair {
  // ECDH P-256 key pair for key exchange
}

// ============================================================================
// Key Bundle Types (matching backend schemas)
// ============================================================================

export interface KeyBundleUpload {
  identity_public_key: string; // Base64 encoded SPKI format
  signed_prekey_id: number;
  signed_prekey_public: string; // Base64 encoded SPKI format
  signed_prekey_signature: string; // Base64 encoded signature
}

export interface OneTimePreKeyUpload {
  key_id: number;
  public_key: string; // Base64 encoded SPKI format
}

export interface OneTimePreKeyBatch {
  prekeys: OneTimePreKeyUpload[];
}

export interface KeyBundleResponse {
  identity_public_key: string;
  signed_prekey: {
    id: number;
    public_key: string;
    signature: string;
  };
  one_time_prekey?: {
    id: number;
    public_key: string;
  };
}

export interface PreKeyCountResponse {
  available_count: number;
  total_count: number;
  used_count: number;
}

// ============================================================================
// Socket Event Types
// ============================================================================

export interface KeyExchangeRequest {
  targetUserId: string;
  conversationId: string;
  ephemeralPublicKey: string; // Base64 encoded SPKI format
}

export interface KeyExchangeResponse {
  success: boolean;
  keyBundle?: KeyBundleResponse;
  error?: string;
}

export interface EncryptionStatusRequest {
  conversationId: string;
}

export interface EncryptionStatusResponse {
  conversationId: string;
  isEncrypted: boolean;
  participants: Array<{
    id: string;
    username: string;
    hasEncryption: boolean;
  }>;
}

// ============================================================================
// Encrypted Message Types
// ============================================================================

export interface EncryptedMessagePayload {
  conversationId: string;
  encryptedContent: string; // Base64 encoded encrypted content
  iv: string; // Base64 encoded IV (96 bits)
  senderRatchetKey: string; // Base64 encoded SPKI format
  messageNumber: number;
  previousChainLength: number;
  messageType: 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM';
}

export interface EncryptedMessage {
  id: string;
  conversationId: string;
  senderId: string;
  encryptedContent: string;
  iv: string;
  senderRatchetKey: string;
  messageNumber: number;
  previousChainLength: number;
  messageType: string;
  createdAt: string;
  updatedAt: string;
}

// ============================================================================
// Local Storage Types
// ============================================================================

export interface StoredIdentityKeys {
  publicKey: string; // Base64 encoded SPKI
  privateKey: string; // Base64 encoded PKCS#8
  createdAt: string;
}

export interface StoredSignedPreKey {
  id: number;
  publicKey: string; // Base64 encoded SPKI
  privateKey: string; // Base64 encoded PKCS#8
  signature: string; // Base64 encoded signature
  createdAt: string;
}

export interface StoredOneTimePreKey {
  id: number;
  publicKey: string; // Base64 encoded SPKI
  privateKey: string; // Base64 encoded PKCS#8
  uploaded: boolean;
  used: boolean;
  createdAt: string;
}

export interface ConversationSession {
  conversationId: string;
  participantId: string;
  rootKey: string; // Base64 encoded
  chainKeySend?: string; // Base64 encoded
  chainKeyReceive?: string; // Base64 encoded
  messageNumberSend: number;
  messageNumberReceive: number;
  previousChainLength: number;
  sessionState: Record<string, any>; // JSON object for additional state
  createdAt: string;
  updatedAt: string;
}

// ============================================================================
// Crypto Utility Types
// ============================================================================

export interface CryptoConfig {
  identityKeyAlgorithm: EcKeyGenParams;
  preKeyAlgorithm: EcKeyGenParams;
  signatureAlgorithm: EcdsaParams;
  derivationAlgorithm: EcdhKeyDeriveParams;
}

export interface EncryptionResult {
  ciphertext: ArrayBuffer;
  iv: ArrayBuffer;
}

export interface DecryptionParams {
  ciphertext: ArrayBuffer;
  iv: ArrayBuffer;
  key: CryptoKey;
}

// ============================================================================
// Error Types
// ============================================================================

export interface EncryptionError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export type EncryptionErrorCode =
  | 'KEY_GENERATION_FAILED'
  | 'KEY_IMPORT_FAILED'
  | 'KEY_EXPORT_FAILED'
  | 'SIGNATURE_FAILED'
  | 'SIGNATURE_VERIFICATION_FAILED'
  | 'ENCRYPTION_FAILED'
  | 'DECRYPTION_FAILED'
  | 'KEY_DERIVATION_FAILED'
  | 'STORAGE_FAILED'
  | 'INVALID_KEY_FORMAT'
  | 'MISSING_KEY_BUNDLE'
  | 'SESSION_NOT_FOUND'
  | 'NETWORK_ERROR';

// ============================================================================
// State Management Types
// ============================================================================

export interface EncryptionState {
  // Key management
  identityKeys: StoredIdentityKeys | null;
  signedPreKey: StoredSignedPreKey | null;
  oneTimePreKeys: StoredOneTimePreKey[];
  
  // Session management
  sessions: Record<string, ConversationSession>; // conversationId -> session
  
  // UI state
  isInitialized: boolean;
  isGeneratingKeys: boolean;
  isUploadingKeys: boolean;
  keyGenerationProgress: number;
  
  // Error state
  error: EncryptionError | null;
  
  // Statistics
  preKeyCount: PreKeyCountResponse | null;
  lastKeyUpload: string | null; // ISO timestamp
}

// ============================================================================
// API Response Types
// ============================================================================

export interface EncryptionApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

// ============================================================================
// Utility Types
// ============================================================================

export type Base64String = string;
export type HexString = string;
export type ISOTimestamp = string;

// Type guards
export const isEncryptedMessage = (message: any): message is EncryptedMessage => {
  return message && 
    typeof message.encryptedContent === 'string' &&
    typeof message.iv === 'string' &&
    typeof message.senderRatchetKey === 'string' &&
    typeof message.messageNumber === 'number';
};

export const isKeyBundleResponse = (data: any): data is KeyBundleResponse => {
  return data &&
    typeof data.identity_public_key === 'string' &&
    data.signed_prekey &&
    typeof data.signed_prekey.id === 'number' &&
    typeof data.signed_prekey.public_key === 'string' &&
    typeof data.signed_prekey.signature === 'string';
};
