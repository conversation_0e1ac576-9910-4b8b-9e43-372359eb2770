# Phase 6: WebRTC Audio/Video Calling

**Duration**: 4-5 weeks | **Priority**: Medium

## Overview
This phase implements real-time audio and video calling capabilities using WebRTC technology, including call management, signaling server, and call quality optimization.

## Prerequisites
- Phase 5 completed successfully
- Media sharing functionality working
- Socket.io server stable
- Understanding of WebRTC concepts
- STUN/TURN server access

## WebRTC Architecture

### Components
- **Signaling Server**: Handles call initiation and WebRTC signaling
- **STUN Server**: Helps with NAT traversal
- **TURN Server**: Relays media when direct connection fails
- **Media Server**: Optional for group calls (future enhancement)

### Call Flow
1. Caller initiates call request
2. Signaling server notifies callee
3. WebRTC peer connection establishment
4. ICE candidate exchange
5. Media stream negotiation
6. Call establishment or termination

## Database Schema Updates

### Step 1: Call Management Models

```python
# backend/apps/calling/models.py
import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

class Call(models.Model):
    CALL_TYPES = [
        ('audio', 'Audio Call'),
        ('video', 'Video Call'),
    ]
    
    CALL_STATUS = [
        ('initiated', 'Initiated'),
        ('ringing', 'Ringing'),
        ('answered', 'Answered'),
        ('active', 'Active'),
        ('ended', 'Ended'),
        ('missed', 'Missed'),
        ('declined', 'Declined'),
        ('failed', 'Failed'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey('messaging.Conversation', on_delete=models.CASCADE, related_name='calls')
    caller = models.ForeignKey(User, on_delete=models.CASCADE, related_name='initiated_calls')
    callee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_calls')
    
    call_type = models.CharField(max_length=10, choices=CALL_TYPES)
    status = models.CharField(max_length=15, choices=CALL_STATUS, default='initiated')
    
    # Timing
    initiated_at = models.DateTimeField(auto_now_add=True)
    answered_at = models.DateTimeField(null=True, blank=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    duration = models.DurationField(null=True, blank=True)  # Calculated field
    
    # WebRTC session data
    session_id = models.CharField(max_length=100, unique=True)
    caller_sdp = models.TextField(blank=True, null=True)
    callee_sdp = models.TextField(blank=True, null=True)
    
    # Call quality metrics
    quality_rating = models.IntegerField(null=True, blank=True)  # 1-5 rating
    quality_issues = models.JSONField(default=list)  # List of reported issues
    
    # Metadata
    metadata = models.JSONField(default=dict)  # Additional call data
    
    class Meta:
        db_table = 'calls'
        ordering = ['-initiated_at']
    
    def save(self, *args, **kwargs):
        # Calculate duration if call ended
        if self.ended_at and self.answered_at:
            self.duration = self.ended_at - self.answered_at
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.call_type.title()} call from {self.caller.username} to {self.callee.username}"

class CallParticipant(models.Model):
    """For future group calling support"""
    PARTICIPANT_STATUS = [
        ('invited', 'Invited'),
        ('joined', 'Joined'),
        ('left', 'Left'),
        ('declined', 'Declined'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    call = models.ForeignKey(Call, on_delete=models.CASCADE, related_name='participants')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='call_participations')
    status = models.CharField(max_length=15, choices=PARTICIPANT_STATUS, default='invited')
    
    joined_at = models.DateTimeField(null=True, blank=True)
    left_at = models.DateTimeField(null=True, blank=True)
    
    # Media state
    audio_enabled = models.BooleanField(default=True)
    video_enabled = models.BooleanField(default=True)
    screen_sharing = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'call_participants'
        unique_together = ['call', 'user']

class CallEvent(models.Model):
    """Track call events for debugging and analytics"""
    EVENT_TYPES = [
        ('call_initiated', 'Call Initiated'),
        ('call_ringing', 'Call Ringing'),
        ('call_answered', 'Call Answered'),
        ('call_ended', 'Call Ended'),
        ('call_declined', 'Call Declined'),
        ('call_failed', 'Call Failed'),
        ('ice_candidate', 'ICE Candidate'),
        ('media_toggle', 'Media Toggle'),
        ('quality_issue', 'Quality Issue'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    call = models.ForeignKey(Call, on_delete=models.CASCADE, related_name='events')
    event_type = models.CharField(max_length=20, choices=EVENT_TYPES)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='call_events')
    event_data = models.JSONField(default=dict)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'call_events'
        ordering = ['timestamp']

class CallQualityMetric(models.Model):
    """Store call quality metrics"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    call = models.ForeignKey(Call, on_delete=models.CASCADE, related_name='quality_metrics')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='call_quality_reports')
    
    # Network metrics
    packet_loss = models.FloatField(null=True, blank=True)  # Percentage
    jitter = models.FloatField(null=True, blank=True)  # Milliseconds
    round_trip_time = models.FloatField(null=True, blank=True)  # Milliseconds
    bandwidth_upload = models.IntegerField(null=True, blank=True)  # Kbps
    bandwidth_download = models.IntegerField(null=True, blank=True)  # Kbps
    
    # Audio metrics
    audio_level = models.FloatField(null=True, blank=True)
    audio_quality_score = models.FloatField(null=True, blank=True)
    
    # Video metrics
    video_resolution = models.CharField(max_length=20, blank=True, null=True)  # e.g., "1280x720"
    video_framerate = models.FloatField(null=True, blank=True)
    video_quality_score = models.FloatField(null=True, blank=True)
    
    recorded_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'call_quality_metrics'
```

### Step 2: Call API Implementation

```python
# backend/apps/calling/views.py
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction

from .models import Call, CallEvent, CallQualityMetric
from .serializers import CallSerializer, CallEventSerializer
from messaging.models import Conversation

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def initiate_call(request):
    """Initiate a new call"""
    conversation_id = request.data.get('conversation_id')
    call_type = request.data.get('call_type', 'audio')
    
    if call_type not in ['audio', 'video']:
        return Response({'error': 'Invalid call type'}, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        conversation = Conversation.objects.get(id=conversation_id, type='direct')
        
        # Check if user is participant
        if not conversation.participants.filter(user=request.user, is_active=True).exists():
            return Response({'error': 'Not a participant in this conversation'}, 
                          status=status.HTTP_403_FORBIDDEN)
        
        # Get the other participant (callee)
        callee = conversation.participants.exclude(user=request.user).first().user
        
        # Check if there's already an active call
        active_call = Call.objects.filter(
            conversation=conversation,
            status__in=['initiated', 'ringing', 'answered', 'active']
        ).first()
        
        if active_call:
            return Response({'error': 'Call already in progress'}, 
                          status=status.HTTP_409_CONFLICT)
        
        with transaction.atomic():
            # Create call record
            call = Call.objects.create(
                conversation=conversation,
                caller=request.user,
                callee=callee,
                call_type=call_type,
                session_id=f"call_{conversation.id}_{int(timezone.now().timestamp())}"
            )
            
            # Create call event
            CallEvent.objects.create(
                call=call,
                event_type='call_initiated',
                user=request.user,
                event_data={'call_type': call_type}
            )
            
            # Notify callee via WebSocket
            from .socket_handlers import notify_incoming_call
            notify_incoming_call(call)
        
        return Response(CallSerializer(call).data, status=status.HTTP_201_CREATED)
        
    except Conversation.DoesNotExist:
        return Response({'error': 'Conversation not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def answer_call(request, call_id):
    """Answer an incoming call"""
    try:
        call = Call.objects.get(id=call_id, callee=request.user, status='ringing')
        
        with transaction.atomic():
            call.status = 'answered'
            call.answered_at = timezone.now()
            call.save()
            
            # Create call event
            CallEvent.objects.create(
                call=call,
                event_type='call_answered',
                user=request.user
            )
            
            # Notify caller via WebSocket
            from .socket_handlers import notify_call_answered
            notify_call_answered(call)
        
        return Response(CallSerializer(call).data)
        
    except Call.DoesNotExist:
        return Response({'error': 'Call not found or cannot be answered'}, 
                       status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def decline_call(request, call_id):
    """Decline an incoming call"""
    try:
        call = Call.objects.get(id=call_id, callee=request.user, status__in=['initiated', 'ringing'])
        
        with transaction.atomic():
            call.status = 'declined'
            call.ended_at = timezone.now()
            call.save()
            
            # Create call event
            CallEvent.objects.create(
                call=call,
                event_type='call_declined',
                user=request.user
            )
            
            # Notify caller via WebSocket
            from .socket_handlers import notify_call_declined
            notify_call_declined(call)
        
        return Response({'message': 'Call declined'})
        
    except Call.DoesNotExist:
        return Response({'error': 'Call not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def end_call(request, call_id):
    """End an active call"""
    try:
        call = Call.objects.get(
            id=call_id,
            status__in=['answered', 'active'],
            participants__user=request.user
        )
        
        with transaction.atomic():
            call.status = 'ended'
            call.ended_at = timezone.now()
            call.save()
            
            # Create call event
            CallEvent.objects.create(
                call=call,
                event_type='call_ended',
                user=request.user
            )
            
            # Notify other participant via WebSocket
            from .socket_handlers import notify_call_ended
            notify_call_ended(call, request.user)
        
        return Response({'message': 'Call ended'})
        
    except Call.DoesNotExist:
        return Response({'error': 'Call not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def update_call_sdp(request, call_id):
    """Update SDP offer/answer for WebRTC negotiation"""
    try:
        call = Call.objects.get(id=call_id)
        
        # Check if user is participant
        if request.user not in [call.caller, call.callee]:
            return Response({'error': 'Not a participant in this call'}, 
                          status=status.HTTP_403_FORBIDDEN)
        
        sdp_type = request.data.get('type')  # 'offer' or 'answer'
        sdp_data = request.data.get('sdp')
        
        if not sdp_type or not sdp_data:
            return Response({'error': 'SDP type and data required'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        # Update appropriate SDP field
        if request.user == call.caller:
            call.caller_sdp = sdp_data
        else:
            call.callee_sdp = sdp_data
        
        call.save()
        
        # Notify other participant via WebSocket
        from .socket_handlers import notify_sdp_update
        notify_sdp_update(call, request.user, sdp_type, sdp_data)
        
        return Response({'message': 'SDP updated'})
        
    except Call.DoesNotExist:
        return Response({'error': 'Call not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_calls([permissions.IsAuthenticated])
def report_call_quality(request, call_id):
    """Report call quality metrics"""
    try:
        call = Call.objects.get(id=call_id)
        
        # Check if user is participant
        if request.user not in [call.caller, call.callee]:
            return Response({'error': 'Not a participant in this call'}, 
                          status=status.HTTP_403_FORBIDDEN)
        
        # Create quality metric record
        CallQualityMetric.objects.create(
            call=call,
            user=request.user,
            packet_loss=request.data.get('packet_loss'),
            jitter=request.data.get('jitter'),
            round_trip_time=request.data.get('round_trip_time'),
            bandwidth_upload=request.data.get('bandwidth_upload'),
            bandwidth_download=request.data.get('bandwidth_download'),
            audio_level=request.data.get('audio_level'),
            audio_quality_score=request.data.get('audio_quality_score'),
            video_resolution=request.data.get('video_resolution'),
            video_framerate=request.data.get('video_framerate'),
            video_quality_score=request.data.get('video_quality_score')
        )
        
        return Response({'message': 'Quality metrics recorded'})
        
    except Call.DoesNotExist:
        return Response({'error': 'Call not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_call_history(request):
    """Get user's call history"""
    calls = Call.objects.filter(
        models.Q(caller=request.user) | models.Q(callee=request.user)
    ).order_by('-initiated_at')[:50]  # Last 50 calls
    
    return Response(CallSerializer(calls, many=True).data)
```

### Step 3: WebRTC Socket Handlers

```javascript
// socket-server/src/handlers/callHandlers.js
class CallHandler {
  constructor(io, redisClient) {
    this.io = io;
    this.redisClient = redisClient;
    this.activeCalls = new Map(); // Store active call sessions
  }

  handleConnection(socket) {
    // Handle call initiation
    socket.on('initiate_call', async (data) => {
      try {
        const { callId, calleeId, callType } = data;
        
        // Store call session
        this.activeCalls.set(callId, {
          callerId: socket.userId,
          calleeId,
          callType,
          status: 'initiated',
          callerSocketId: socket.id
        });
        
        // Notify callee
        socket.to(`user_${calleeId}`).emit('incoming_call', {
          callId,
          callerId: socket.userId,
          callType,
          timestamp: new Date().toISOString()
        });
        
        // Update call status to ringing
        setTimeout(() => {
          const call = this.activeCalls.get(callId);
          if (call && call.status === 'initiated') {
            call.status = 'ringing';
            this.io.to(`user_${call.callerId}`).emit('call_ringing', { callId });
          }
        }, 1000);
        
      } catch (error) {
        socket.emit('call_error', { message: 'Failed to initiate call' });
      }
    });

    // Handle call answer
    socket.on('answer_call', async (data) => {
      try {
        const { callId } = data;
        const call = this.activeCalls.get(callId);
        
        if (!call || call.calleeId !== socket.userId) {
          socket.emit('call_error', { message: 'Call not found or unauthorized' });
          return;
        }
        
        call.status = 'answered';
        call.calleeSocketId = socket.id;
        
        // Notify caller that call was answered
        this.io.to(call.callerSocketId).emit('call_answered', {
          callId,
          calleeId: socket.userId
        });
        
        // Both participants join the call room
        socket.join(`call_${callId}`);
        this.io.sockets.sockets.get(call.callerSocketId)?.join(`call_${callId}`);
        
      } catch (error) {
        socket.emit('call_error', { message: 'Failed to answer call' });
      }
    });

    // Handle call decline
    socket.on('decline_call', async (data) => {
      try {
        const { callId } = data;
        const call = this.activeCalls.get(callId);
        
        if (!call || call.calleeId !== socket.userId) {
          socket.emit('call_error', { message: 'Call not found or unauthorized' });
          return;
        }
        
        // Notify caller that call was declined
        this.io.to(call.callerSocketId).emit('call_declined', { callId });
        
        // Clean up call session
        this.activeCalls.delete(callId);
        
      } catch (error) {
        socket.emit('call_error', { message: 'Failed to decline call' });
      }
    });

    // Handle call end
    socket.on('end_call', async (data) => {
      try {
        const { callId } = data;
        const call = this.activeCalls.get(callId);
        
        if (!call) {
          socket.emit('call_error', { message: 'Call not found' });
          return;
        }
        
        // Notify other participant
        socket.to(`call_${callId}`).emit('call_ended', {
          callId,
          endedBy: socket.userId
        });
        
        // Clean up call session
        this.activeCalls.delete(callId);
        
        // Remove participants from call room
        this.io.in(`call_${callId}`).socketsLeave(`call_${callId}`);
        
      } catch (error) {
        socket.emit('call_error', { message: 'Failed to end call' });
      }
    });

    // Handle WebRTC signaling
    socket.on('webrtc_offer', (data) => {
      const { callId, offer } = data;
      socket.to(`call_${callId}`).emit('webrtc_offer', {
        callId,
        offer,
        from: socket.userId
      });
    });

    socket.on('webrtc_answer', (data) => {
      const { callId, answer } = data;
      socket.to(`call_${callId}`).emit('webrtc_answer', {
        callId,
        answer,
        from: socket.userId
      });
    });

    socket.on('webrtc_ice_candidate', (data) => {
      const { callId, candidate } = data;
      socket.to(`call_${callId}`).emit('webrtc_ice_candidate', {
        callId,
        candidate,
        from: socket.userId
      });
    });

    // Handle media state changes
    socket.on('toggle_audio', (data) => {
      const { callId, enabled } = data;
      socket.to(`call_${callId}`).emit('participant_audio_toggle', {
        userId: socket.userId,
        enabled
      });
    });

    socket.on('toggle_video', (data) => {
      const { callId, enabled } = data;
      socket.to(`call_${callId}`).emit('participant_video_toggle', {
        userId: socket.userId,
        enabled
      });
    });

    // Handle call quality reports
    socket.on('call_quality_report', (data) => {
      const { callId, metrics } = data;
      // Store metrics or forward to analytics service
      console.log(`Call quality report for ${callId}:`, metrics);
    });

    // Handle disconnection during call
    socket.on('disconnect', () => {
      // Find and clean up any active calls for this user
      for (const [callId, call] of this.activeCalls.entries()) {
        if (call.callerSocketId === socket.id || call.calleeSocketId === socket.id) {
          // Notify other participant about disconnection
          socket.to(`call_${callId}`).emit('call_ended', {
            callId,
            reason: 'participant_disconnected',
            endedBy: socket.userId
          });
          
          // Clean up
          this.activeCalls.delete(callId);
          this.io.in(`call_${callId}`).socketsLeave(`call_${callId}`);
        }
      }
    });
  }

  // Helper method to notify about incoming calls
  notifyIncomingCall(call) {
    this.io.to(`user_${call.callee.id}`).emit('incoming_call', {
      callId: call.id,
      caller: {
        id: call.caller.id,
        username: call.caller.username,
        first_name: call.caller.first_name,
        last_name: call.caller.last_name,
        profile_picture: call.caller.profile_picture
      },
      callType: call.call_type,
      timestamp: call.initiated_at
    });
  }
}

module.exports = CallHandler;
```

### Step 4: Frontend WebRTC Implementation

```typescript
// frontend/src/utils/webrtc.ts
export class WebRTCManager {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private socket: any;
  private callId: string | null = null;

  // STUN/TURN server configuration
  private readonly rtcConfiguration: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      // Add TURN servers for production
      // {
      //   urls: 'turn:your-turn-server.com:3478',
      //   username: 'username',
      //   credential: 'password'
      // }
    ],
    iceCandidatePoolSize: 10,
  };

  constructor(socket: any) {
    this.socket = socket;
    this.setupSocketListeners();
  }

  private setupSocketListeners() {
    this.socket.on('webrtc_offer', this.handleOffer.bind(this));
    this.socket.on('webrtc_answer', this.handleAnswer.bind(this));
    this.socket.on('webrtc_ice_candidate', this.handleIceCandidate.bind(this));
  }

  async initializeCall(callId: string, isVideo: boolean = false): Promise<void> {
    this.callId = callId;
    
    try {
      // Get user media
      this.localStream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: isVideo
      });

      // Create peer connection
      this.peerConnection = new RTCPeerConnection(this.rtcConfiguration);
      
      // Add local stream to peer connection
      this.localStream.getTracks().forEach(track => {
        if (this.peerConnection && this.localStream) {
          this.peerConnection.addTrack(track, this.localStream);
        }
      });

      // Handle remote stream
      this.peerConnection.ontrack = (event) => {
        this.remoteStream = event.streams[0];
        this.onRemoteStream?.(this.remoteStream);
      };

      // Handle ICE candidates
      this.peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          this.socket.emit('webrtc_ice_candidate', {
            callId: this.callId,
            candidate: event.candidate
          });
        }
      };

      // Handle connection state changes
      this.peerConnection.onconnectionstatechange = () => {
        const state = this.peerConnection?.connectionState;
        this.onConnectionStateChange?.(state || 'unknown');
        
        if (state === 'connected') {
          this.startQualityMonitoring();
        }
      };

    } catch (error) {
      console.error('Failed to initialize call:', error);
      throw error;
    }
  }

  async createOffer(): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      const offer = await this.peerConnection.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: true
      });

      await this.peerConnection.setLocalDescription(offer);

      this.socket.emit('webrtc_offer', {
        callId: this.callId,
        offer: offer
      });

    } catch (error) {
      console.error('Failed to create offer:', error);
      throw error;
    }
  }

  private async handleOffer(data: any): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      await this.peerConnection.setRemoteDescription(data.offer);
      
      const answer = await this.peerConnection.createAnswer();
      await this.peerConnection.setLocalDescription(answer);

      this.socket.emit('webrtc_answer', {
        callId: data.callId,
        answer: answer
      });

    } catch (error) {
      console.error('Failed to handle offer:', error);
      throw error;
    }
  }

  private async handleAnswer(data: any): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      await this.peerConnection.setRemoteDescription(data.answer);
    } catch (error) {
      console.error('Failed to handle answer:', error);
      throw error;
    }
  }

  private async handleIceCandidate(data: any): Promise<void> {
    if (!this.peerConnection) {
      return;
    }

    try {
      await this.peerConnection.addIceCandidate(data.candidate);
    } catch (error) {
      console.error('Failed to add ICE candidate:', error);
    }
  }

  toggleAudio(): boolean {
    if (!this.localStream) return false;
    
    const audioTrack = this.localStream.getAudioTracks()[0];
    if (audioTrack) {
      audioTrack.enabled = !audioTrack.enabled;
      
      this.socket.emit('toggle_audio', {
        callId: this.callId,
        enabled: audioTrack.enabled
      });
      
      return audioTrack.enabled;
    }
    return false;
  }

  toggleVideo(): boolean {
    if (!this.localStream) return false;
    
    const videoTrack = this.localStream.getVideoTracks()[0];
    if (videoTrack) {
      videoTrack.enabled = !videoTrack.enabled;
      
      this.socket.emit('toggle_video', {
        callId: this.callId,
        enabled: videoTrack.enabled
      });
      
      return videoTrack.enabled;
    }
    return false;
  }

  private startQualityMonitoring(): void {
    if (!this.peerConnection) return;

    const interval = setInterval(async () => {
      if (!this.peerConnection || this.peerConnection.connectionState !== 'connected') {
        clearInterval(interval);
        return;
      }

      try {
        const stats = await this.peerConnection.getStats();
        const metrics = this.parseStats(stats);
        
        this.socket.emit('call_quality_report', {
          callId: this.callId,
          metrics
        });

        this.onQualityUpdate?.(metrics);
        
      } catch (error) {
        console.error('Failed to get call stats:', error);
      }
    }, 5000); // Report every 5 seconds
  }

  private parseStats(stats: RTCStatsReport): any {
    const metrics: any = {};
    
    stats.forEach((report) => {
      if (report.type === 'inbound-rtp' && report.mediaType === 'audio') {
        metrics.audioPacketsLost = report.packetsLost;
        metrics.audioJitter = report.jitter;
      } else if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
        metrics.videoPacketsLost = report.packetsLost;
        metrics.videoFramesDecoded = report.framesDecoded;
        metrics.videoFrameWidth = report.frameWidth;
        metrics.videoFrameHeight = report.frameHeight;
      } else if (report.type === 'candidate-pair' && report.state === 'succeeded') {
        metrics.roundTripTime = report.currentRoundTripTime;
      }
    });
    
    return metrics;
  }

  endCall(): void {
    // Stop local stream
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    // Close peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    // Reset state
    this.remoteStream = null;
    this.callId = null;
  }

  // Event handlers (to be set by the calling component)
  onRemoteStream?: (stream: MediaStream) => void;
  onConnectionStateChange?: (state: string) => void;
  onQualityUpdate?: (metrics: any) => void;
}
```

## Integration Points

### Socket.io Integration
- Call signaling through WebSocket
- Real-time call state updates
- WebRTC offer/answer exchange

### Frontend Integration
- Call UI components
- Media stream handling
- Call quality monitoring

## Acceptance Criteria

### Phase 6 Completion Checklist
- [ ] Audio calling functional
- [ ] Video calling working
- [ ] Call management (answer/decline/end)
- [ ] WebRTC signaling implemented
- [ ] Call quality monitoring
- [ ] Call history tracking
- [ ] Media controls (mute/video toggle)
- [ ] Connection state handling

### Testing Requirements
- [ ] Call initiation/termination tests
- [ ] WebRTC signaling tests
- [ ] Media stream handling tests
- [ ] Call quality metric tests
- [ ] Cross-browser compatibility tests

## Common Issues & Troubleshooting

### WebRTC Connection Issues
- Check STUN/TURN server configuration
- Verify firewall and NAT settings
- Test ICE candidate gathering

### Media Access Problems
- Ensure browser permissions for camera/microphone
- Handle device availability gracefully
- Test on different devices and browsers

### Call Quality Issues
- Monitor network conditions
- Implement adaptive bitrate
- Provide quality feedback to users

## Next Phase Dependencies
- Audio/video calling fully functional
- Call quality acceptable
- WebRTC signaling stable
- UI responsive for call operations

This phase enables real-time communication. Ensure thorough testing across different network conditions before proceeding to Phase 7 (Advanced Features).
