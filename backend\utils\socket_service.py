# backend/utils/socket_service.py
import requests
import json
import logging
from django.conf import settings
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class SocketService:
    """
    Service for emitting socket events to the Node.js socket server.
    This allows Django backend to trigger real-time notifications.
    """
    
    def __init__(self):
        # Socket server URL - should be configurable via settings
        self.socket_server_url = getattr(settings, 'SOCKET_SERVER_URL', 'http://localhost:7000')
        self.emit_endpoint = f"{self.socket_server_url}/api/emit"
        
    def emit_message_created(self, message, conversation_participants: list) -> bool:
        """
        Emit a new_message event to all participants in a conversation.
        
        Args:
            message: Django Message model instance
            conversation_participants: List of user IDs who should receive the message
            
        Returns:
            bool: True if emission was successful, False otherwise
        """
        try:
            from messaging.schemas import MessageResponse
            
            # Serialize message using the same schema as the API
            message_data = MessageResponse.model_validate(message).model_dump(by_alias=True)
            
            payload = {
                'event': 'new_message',
                'room': f'conversation_{message.conversation.id}',
                'data': {
                    'message': message_data
                },
                'recipients': conversation_participants  # List of user IDs
            }
            
            response = requests.post(
                self.emit_endpoint,
                json=payload,
                timeout=5,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                logger.info(f"Successfully emitted new_message event for message {message.id}")
                return True
            else:
                logger.error(f"Failed to emit socket event: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error emitting socket event for message {message.id}: {str(e)}")
            return False
    
    def emit_to_user(self, user_id: str, event: str, data: Dict[str, Any]) -> bool:
        """
        Emit an event to a specific user.
        
        Args:
            user_id: ID of the user to send the event to
            event: Event name
            data: Event data
            
        Returns:
            bool: True if emission was successful, False otherwise
        """
        try:
            payload = {
                'event': event,
                'room': f'user:{user_id}',
                'data': data,
                'recipients': [user_id]
            }
            
            response = requests.post(
                self.emit_endpoint,
                json=payload,
                timeout=5,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                logger.info(f"Successfully emitted {event} event to user {user_id}")
                return True
            else:
                logger.error(f"Failed to emit socket event: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error emitting socket event {event} to user {user_id}: {str(e)}")
            return False
    
    def emit_message_status_update(self, message_id: str, user_id: str, status: str, conversation_id: str) -> bool:
        """
        Emit a message status update event.
        
        Args:
            message_id: ID of the message
            user_id: ID of the user whose status changed
            status: New status (DELIVERED, READ, etc.)
            conversation_id: ID of the conversation
            
        Returns:
            bool: True if emission was successful, False otherwise
        """
        try:
            payload = {
                'event': 'message_status_updated',
                'room': f'conversation_{conversation_id}',
                'data': {
                    'messageId': message_id,
                    'userId': user_id,
                    'status': status
                }
            }
            
            response = requests.post(
                self.emit_endpoint,
                json=payload,
                timeout=5,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                logger.info(f"Successfully emitted message_status_updated event for message {message_id}")
                return True
            else:
                logger.error(f"Failed to emit socket event: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error emitting socket status event: {str(e)}")
            return False

# Global instance
socket_service = SocketService()