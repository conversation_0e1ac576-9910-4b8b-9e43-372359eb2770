# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Architecture Overview

This is a comprehensive full-stack chat application with end-to-end encryption, real-time messaging, WebRTC calling, and media sharing capabilities. The application follows a microservices architecture with three main components:

### Core Services
1. **Django Backend** (`/backend`) - REST API with JWT authentication, PostgreSQL database
2. **React Frontend** (`/frontend`) - TypeScript React app with Vite, Redux Toolkit, TailwindCSS
3. **Socket.io Server** (`/socket-server`) - Real-time messaging with Prisma ORM (referenced but not present in current directory structure)

### Key Technologies
- **Backend**: Django 5.2.4, DRF, PostgreSQL, Redis, JWT authentication, Celery
- **Frontend**: React 19, TypeScript, Vite, Redux Toolkit, TailwindCSS, Socket.io-client
- **Testing**: Pytest (backend with 100% coverage requirement), <PERSON><PERSON><PERSON> (frontend with 100% coverage requirement), <PERSON><PERSON> (E2E)
- **Encryption**: Web Crypto API, Signal Protocol implementation
- **Real-time**: Socket.io with Redis for session management

## Development Commands

### Service Management (Use Makefile)
```bash
# Start all services in separate windows (Windows)
make start

# Start individual services
make backend      # Django on port 6000
make frontend     # React on port 5000
make socket-server # Socket server on port 7000

# Install all dependencies
make install-deps

# Check dependencies
make check-deps
```

### Backend (Django)
```bash
cd backend

# Activate virtual environment (Windows)
.\venv\Scripts\activate

# Run development server
python manage.py runserver 6000

# Database migrations (use migrate.ps1 instead of manage.py)
.\migrate.ps1  # Syncs both Django and Prisma schemas

# Create superuser
python manage.py createsuperuser

# Run tests with coverage
pytest
pytest -m "unit"          # Unit tests only
pytest -m "integration"   # Integration tests only
pytest --cov-report=html  # Generate HTML coverage report

# Create test users
python create_test_users.py
```

### Frontend (React/Vite)
```bash
cd frontend

# Development server (port 5000)
pnpm run dev

# Build for production
pnpm run build

# Run tests
pnpm run test           # Vitest in watch mode
pnpm run test:run       # Run tests once
pnpm run test:coverage  # Run with coverage
pnpm run test:ui        # Vitest UI mode

# Linting
pnpm run lint
```

### End-to-End Testing (Playwright)
```bash
cd e2e

# Install Playwright browsers
npx playwright install

# Run E2E tests (ensure all services are running first)
npm run test:e2e
npm run test:e2e:headed  # With browser UI
npm run test:e2e:debug   # Debug mode
npm run test:e2e:ui      # Playwright UI mode

# Run specific test categories
npm run test:e2e -- tests/auth/
npm run test:e2e -- tests/real-time/

# View test reports
npm run test:e2e:report
```

## Code Architecture

### Backend Structure (`/backend`)
- **`chatapp/`** - Main Django project with settings, URLs, ASGI/WSGI
- **`authentication/`** - Custom JWT authentication with email backend
- **`users/`** - Custom User model and user management
- **`core/`** - Shared utilities and base classes
- **`messaging/`** - Message handling, conversations, real-time events
- **`encryption/`** - End-to-end encryption implementation (Signal Protocol)
- **`media/`** - File upload, media processing, encrypted storage
- **`calling/`** - WebRTC calling infrastructure and signaling

### Frontend Structure (`/frontend/src`)
- **`contexts/`** - React contexts for Auth, Encryption, Socket, Calling
- **`components/`** - Reusable UI components with TailwindCSS
- **`pages/`** - Main application pages (Login, Register, Dashboard)
- **`services/`** - API clients and service abstractions
- **`hooks/`** - Custom React hooks for API, encryption, real-time sync
- **`crypto/`** - Web Crypto API implementation, key management
- **`store/`** - Redux Toolkit store configuration

### Key Patterns

#### Multi-Context Architecture
The frontend uses a nested context architecture for separation of concerns:
```typescript
<AuthProvider>
  <EncryptionProvider>
    <SocketProvider>
      <CallingProvider>
        <App />
      </CallingProvider>
    </SocketProvider>
  </EncryptionProvider>
</AuthProvider>
```

#### Encryption Flow
- Client-side key generation using Web Crypto API
- Signal Protocol implementation for key exchange
- AES-256-GCM for message encryption
- Double Ratchet algorithm for forward secrecy
- Encrypted storage with key rotation

#### Real-time Synchronization
- Socket.io for real-time events
- Redux store sync with socket events via `useSocketCacheSync`
- Automatic reconnection and state recovery
- Typing indicators and presence management

## Testing Standards

### Coverage Requirements
- **Backend**: 100% test coverage enforced via pytest configuration
- **Frontend**: 100% test coverage enforced via Vitest configuration
- **E2E**: 150+ test scenarios covering all user workflows

### Test Categories
- **Unit Tests**: Individual components/functions (`*_tests.py`, `*.test.tsx`)
- **Integration Tests**: API endpoints, database operations
- **E2E Tests**: Complete user workflows across all browsers
- **Security Tests**: XSS, CSRF, input validation, encryption

### Test Execution
Tests run automatically in CI/CD but can be executed locally:
- Backend tests use pytest with Django settings
- Frontend tests use Vitest with jsdom environment
- E2E tests require all services running (use Makefile to start)

## Development Phases

This project follows a structured 7-phase development plan:

1. **Phase 1**: Foundation & Infrastructure ✅ COMPLETE
2. **Phase 2**: Core Messaging Infrastructure ✅ COMPLETE  
3. **Phase 3**: End-to-End Encryption 🚧 IN PROGRESS
4. **Phase 4**: Group Chat Functionality
5. **Phase 5**: Media Sharing & File Handling
6. **Phase 6**: Audio/Video Calling (WebRTC)
7. **Phase 7**: Advanced Features & Polish

Current implementation includes complete authentication, real-time messaging, and comprehensive testing infrastructure.

## Database Management

### Migration Workflow
- Use `migrate.ps1` instead of `manage.py migrate` - this syncs both Django and Prisma schemas
- Django models in each app's `models.py`
- Prisma schema synchronization for Socket.io server
- PostgreSQL with Redis for caching/sessions

### Key Models
- **User**: Custom user model with encryption key storage
- **Conversation**: Direct and group conversations
- **Message**: Encrypted messages with metadata
- **MediaFile**: Encrypted file storage with thumbnails
- **UserKeys**: Signal Protocol key management

## Security Considerations

### Authentication
- JWT tokens with refresh rotation
- Email-based authentication backend
- Session management via Redis
- Custom User model with encryption capabilities

### Encryption
- Client-side encryption before transmission
- Signal Protocol for key exchange
- AES-256-GCM for message encryption
- Forward secrecy implementation
- Secure key storage and rotation

### File Handling
- Encrypted file storage
- File type validation and virus scanning
- Chunked uploads for large files
- Secure download tokens with expiration

## Environment Setup

### Required Services
- PostgreSQL database
- Redis server
- Python 3.12+ with virtual environment
- Node.js 18+ with pnpm
- Playwright browsers for E2E testing

### Environment Variables
Create `.env` files in `/backend` and `/socket-server` with database URLs, JWT secrets, and Redis configuration as specified in `SETUP_INSTRUCTIONS.md`.

## WebRTC Testing (No Media Devices Required)

The application includes a comprehensive testing system for WebRTC calling without requiring camera/microphone:

### Enable Mock Mode
```bash
# Method 1: URL parameter
http://localhost:5002/?mock=true

# Method 2: Browser console
localStorage.setItem('webrtc_mock_mode', 'true');
# Then reload page

# Method 3: Environment variable
echo "VITE_WEBRTC_MOCK_MODE=true" >> frontend/.env
```

### WebRTC Testing Panel
- **Location**: Settings button (⚙️) in bottom-right corner
- **Features**: Mock mode toggle, real-time socket event monitoring
- **Socket Events**: View WebRTC signaling events (offer/answer/ICE candidates)
- **Visual**: Mock video streams with "Testing Mode" overlay

### Testing Workflow
1. Enable mock mode via testing panel
2. Try audio/video calls between users
3. Monitor socket events in real-time
4. Check console logs for detailed WebRTC flow
5. Test error scenarios (missing devices, permissions)

See `WEBRTC_TESTING.md` for complete testing guide.

## Common Development Tasks

### Adding New API Endpoints
1. Create views in appropriate Django app
2. Add URL patterns to app's `urls.py`
3. Update main `chatapp/urls.py` if needed
4. Add corresponding frontend API service
5. Write tests for both backend and frontend

### Real-time Features
1. Add socket event handlers in Socket.io server
2. Update frontend SocketContext
3. Add Redux actions/reducers if state management needed
4. Implement UI components for real-time updates

### Encryption Features
1. Implement crypto operations in `/frontend/src/crypto`
2. Add key management in EncryptionContext
3. Update message handling for encryption/decryption
4. Add backend endpoints for key exchange

### WebRTC/Calling Features
1. Use mock mode for development without media devices
2. Test socket events using the WebRTC testing panel
3. Add new calling events to both frontend and socket server
4. Update WebRTC manager for new functionality
5. Test real device scenarios before production

## CI/CD Integration

- GitHub Actions workflow for multi-browser E2E testing
- Automated test execution on PRs
- Coverage reporting and enforcement
- Service orchestration for testing environment
- Cross-platform compatibility (Windows focus with PowerShell scripts)
