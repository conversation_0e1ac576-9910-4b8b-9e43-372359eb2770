// frontend/src/store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import messageReducer from './slices/messageSlice';
import conversationReducer from './slices/conversationSlice';
import encryptionReducer from './slices/encryptionSlice';
import mediaUploadReducer from './slices/mediaUploadSlice';
import { api } from '../services';
import { callingApi } from '../services/callingApi';

export const store = configureStore({
  reducer: {
    messages: messageReducer,
    conversations: conversationReducer,
    encryption: encryptionReducer,
    mediaUpload: mediaUploadReducer,
    // Add the RTK Query API reducers
    [api.reducerPath]: api.reducer,
    [callingApi.reducerPath]: callingApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    })
    // Add the RTK Query middleware
    .concat(api.middleware)
    .concat(callingApi.middleware),
});

// Enable listener behavior for the store
setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Re-export memoized selectors for easy access
export {
  selectMessagesByConversation,
  selectSortedMessagesByConversation,
  selectTypingUsersByConversation,
  selectSendingMessages,
  selectMessageStatuses,
  selectFailedMessages,
  selectTypingUsers
} from './slices/messageSlice';
