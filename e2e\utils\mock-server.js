#!/usr/bin/env node

/**
 * Mock Frontend Server for E2E Testing
 * 
 * This server provides a minimal frontend interface for E2E tests
 * when the actual frontend development server is not available.
 */

const http = require('http');
const path = require('path');
const fs = require('fs');

const PORT = 5173;

// Mock HTML pages
const mockPages = {
  '/': `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat App - Mock</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 400px; margin: 0 auto; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; }
        input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .link { color: #007bff; text-decoration: none; }
        .link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chat Application</h1>
        <p>Welcome to the Chat App. Please <a href="/login" class="link">login</a> or <a href="/register" class="link">register</a>.</p>
    </div>
    <script>
        // Redirect to login by default
        if (window.location.pathname === '/') {
            window.location.href = '/login';
        }
    </script>
</body>
</html>`,

  '/login': `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Chat App</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 400px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .link { color: #007bff; text-decoration: none; }
        .link:hover { text-decoration: underline; }
        .error { color: #dc3545; margin-top: 10px; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; }
        .loading { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Login</h1>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" data-testid="email-input" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" data-testid="password-input" required>
            </div>
            <button type="submit" id="loginButton" data-testid="login-button">Login</button>
            <div class="loading" data-testid="loading-spinner">Logging in...</div>
            <div id="error" class="error" data-testid="error-message" style="display: none;"></div>
        </form>
        <p style="text-align: center; margin-top: 20px;">
            Don't have an account? <a href="/register" class="link">Register here</a>
        </p>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('error');
            const loadingDiv = document.querySelector('.loading');
            const submitButton = document.getElementById('loginButton');
            
            // Show loading state
            loadingDiv.style.display = 'block';
            submitButton.disabled = true;
            errorDiv.style.display = 'none';
            
            // Simulate API call
            setTimeout(() => {
                if (email && password) {
                    // Simulate successful login
                    window.location.href = '/dashboard';
                } else {
                    // Show error
                    errorDiv.textContent = 'Invalid credentials';
                    errorDiv.style.display = 'block';
                    loadingDiv.style.display = 'none';
                    submitButton.disabled = false;
                }
            }, 1000);
        });
    </script>
</body>
</html>`,

  '/register': `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Chat App</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 400px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { width: 100%; padding: 12px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        button:hover { background: #218838; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .link { color: #007bff; text-decoration: none; }
        .link:hover { text-decoration: underline; }
        .error { color: #dc3545; margin-top: 10px; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; }
        .loading { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Register</h1>
        <form id="registerForm">
            <div class="form-group">
                <label for="firstName">First Name:</label>
                <input type="text" id="firstName" name="firstName" data-testid="firstName-input" required>
            </div>
            <div class="form-group">
                <label for="lastName">Last Name:</label>
                <input type="text" id="lastName" name="lastName" data-testid="lastName-input" required>
            </div>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" data-testid="email-input" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" data-testid="password-input" required>
            </div>
            <button type="submit" id="registerButton" data-testid="register-button">Register</button>
            <div class="loading" data-testid="loading-spinner">Creating account...</div>
            <div id="error" class="error" data-testid="error-message" style="display: none;"></div>
        </form>
        <p style="text-align: center; margin-top: 20px;">
            Already have an account? <a href="/login" class="link">Login here</a>
        </p>
    </div>

    <script>
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('error');
            const loadingDiv = document.querySelector('.loading');
            const submitButton = document.getElementById('registerButton');
            
            // Show loading state
            loadingDiv.style.display = 'block';
            submitButton.disabled = true;
            errorDiv.style.display = 'none';
            
            // Simulate API call
            setTimeout(() => {
                if (firstName && lastName && email && password) {
                    // Simulate successful registration
                    window.location.href = '/dashboard';
                } else {
                    // Show error
                    errorDiv.textContent = 'All fields are required';
                    errorDiv.style.display = 'block';
                    loadingDiv.style.display = 'none';
                    submitButton.disabled = false;
                }
            }, 1000);
        });
    </script>
</body>
</html>`,

  '/dashboard': `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Chat App</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background-color: #f5f5f5; }
        .header { background: #007bff; color: white; padding: 20px; }
        .container { max-width: 800px; margin: 20px auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .chat-area { padding: 20px; min-height: 400px; }
        .connection-status { padding: 10px; background: #d4edda; color: #155724; border: 1px solid #c3e6cb; border-radius: 4px; margin-bottom: 20px; }
        .message-input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px; }
        .send-button { padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .send-button:hover { background: #0056b3; }
        .message-list { border: 1px solid #ddd; border-radius: 4px; padding: 20px; margin-bottom: 20px; min-height: 200px; }
        .typing-indicator { font-style: italic; color: #666; display: none; }
    </style>
</head>
<body>
    <div class="header" data-testid="dashboard-header">
        <h1>Chat Dashboard</h1>
        <button onclick="logout()" data-testid="logout-button" style="float: right; background: transparent; border: 1px solid white; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Logout</button>
    </div>
    
    <div class="container">
        <div class="chat-area" data-testid="chat-area">
            <div class="connection-status" data-testid="connection-status">Connected</div>
            
            <div class="message-list" data-testid="message-list">
                <p>Welcome to the chat! Start typing to send messages.</p>
            </div>
            
            <div class="typing-indicator" data-testid="typing-indicator">Someone is typing...</div>
            
            <textarea class="message-input" data-testid="message-input" placeholder="Type your message here..." rows="3"></textarea>
            <button class="send-button" data-testid="send-button" onclick="sendMessage()">Send Message</button>
        </div>
    </div>

    <script>
        function logout() {
            window.location.href = '/login';
        }
        
        function sendMessage() {
            const input = document.querySelector('[data-testid="message-input"]');
            const messageList = document.querySelector('[data-testid="message-list"]');
            
            if (input.value.trim()) {
                const messageDiv = document.createElement('div');
                messageDiv.textContent = input.value;
                messageDiv.style.marginBottom = '10px';
                messageDiv.style.padding = '10px';
                messageDiv.style.background = '#e3f2fd';
                messageDiv.style.borderRadius = '4px';
                messageDiv.className = 'message';
                messageDiv.setAttribute('data-testid', 'message');
                
                messageList.appendChild(messageDiv);
                input.value = '';
                
                // Scroll to bottom
                messageList.scrollTop = messageList.scrollHeight;
            }
        }
        
        // Handle Enter key
        document.querySelector('[data-testid="message-input"]').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>
</html>`
};

// Create HTTP server
const server = http.createServer((req, res) => {
  const url = req.url;
  
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // Serve mock pages
  if (mockPages[url]) {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(mockPages[url]);
  } else if (url === '/api/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ status: 'ok', service: 'mock-frontend' }));
  } else {
    // Redirect unknown routes to login
    res.writeHead(302, { 'Location': '/login' });
    res.end();
  }
});

server.listen(PORT, () => {
  console.log(`Mock frontend server running on http://localhost:${PORT}`);
  console.log('Available routes:');
  console.log('  - / (redirects to /login)');
  console.log('  - /login');
  console.log('  - /register');
  console.log('  - /dashboard');
  console.log('  - /api/health');
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down mock server...');
  server.close(() => {
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('Shutting down mock server...');
  server.close(() => {
    process.exit(0);
  });
});
