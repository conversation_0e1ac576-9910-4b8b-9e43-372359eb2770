// frontend/src/components/Chat/ChatRoom.tsx
import React, { useEffect } from 'react';
import { useSocket } from '../../contexts/SocketContext';
import { useGetMessagesQuery } from '../../services/messageApi';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import { ChatHeader } from './ChatHeader';
import { useAuth } from '../../contexts/AuthContext';

interface ChatRoomProps {
  conversationId: string;
}

const ChatRoom: React.FC<ChatRoomProps> = ({ conversationId }) => {
  const { joinConversation, isConnected } = useSocket();
  const { user } = useAuth();

  // Check if this is a draft conversation
  const isDraftConversation = conversationId?.startsWith('draft-');

  // Prefetch messages using RTK Query - skip for draft conversations
  useGetMessagesQuery({ conversationId }, {
    skip: !conversationId || isDraftConversation
  });

  useEffect(() => {
    if (conversationId && isConnected && !isDraftConversation) {
      // Join the conversation room for real-time updates
      // Skip for draft conversations since they don't exist on the server yet
      joinConversation(conversationId);
    }
  }, [conversationId, joinConversation, isConnected, isDraftConversation]);

  if (!user) {
    return <div>Please log in to access chat</div>;
  }

  return (
    <div className="flex flex-col h-full">
      {/* Chat header */}
      {!isDraftConversation && (
        <ChatHeader conversationId={conversationId} currentUserId={user.id} />
      )}

      {/* Connection status */}
      {!isConnected && (
        <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-2">
          <div className="flex items-center space-x-2 text-yellow-800 text-sm">
            <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
            <span>Reconnecting...</span>
          </div>
        </div>
      )}

      {/* Messages */}
      <MessageList
        conversationId={conversationId}
        currentUserId={user.id}
      />

      {/* Message input */}
      <MessageInput
        conversationId={conversationId}
        disabled={!isConnected}
      />
    </div>
  );
};

export default ChatRoom;
