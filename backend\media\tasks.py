# backend/media/tasks.py - Background Processing with E2<PERSON>
from celery import shared_task
from django.utils import timezone
from .models import MediaFile, MediaProcessingJob
import logging
import json

logger = logging.getLogger(__name__)

@shared_task(bind=True, max_retries=3)
def process_media_file_e2ee(self, media_file_id):
    """Process uploaded media file with E2EE support"""
    try:
        media_file = MediaFile.objects.get(id=media_file_id)
        media_file.processing_status = 'processing'
        media_file.save()
        
        # Create processing jobs with priority
        jobs = []
        
        # Generate thumbnail for images and videos (high priority)
        if media_file.file_type in ['image', 'video']:
            jobs.append(create_processing_job(
                media_file, 'thumbnail', priority=1
            ))
        
        # Extract metadata (medium priority)
        jobs.append(create_processing_job(
            media_file, 'metadata_extraction', priority=3
        ))
        
        # Process jobs in priority order
        all_successful = True
        for job in sorted(jobs, key=lambda x: x.priority):
            try:
                success = process_job_e2ee(job)
                if not success:
                    all_successful = False
            except Exception as e:
                logger.error(f"Job {job.id} failed: {str(e)}")
                all_successful = False
        
        # Update final status
        media_file.processing_status = 'completed' if all_successful else 'failed'
        media_file.save()
        
        # Notify clients via WebSocket (implement later)
        # from messaging.socket_handlers import notify_media_processed
        # notify_media_processed(media_file)
        
    except MediaFile.DoesNotExist:
        logger.error(f"Media file {media_file_id} not found")
    except Exception as e:
        logger.error(f"Error processing media file {media_file_id}: {str(e)}")
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            raise self.retry(
                countdown=60 * (2 ** self.request.retries),
                exc=e
            )
        
        # Mark as failed after max retries
        try:
            media_file = MediaFile.objects.get(id=media_file_id)
            media_file.processing_status = 'failed'
            media_file.processing_error = str(e)
            media_file.save()
        except:
            pass

def create_processing_job(media_file, job_type, priority=5):
    """Create a processing job with priority"""
    return MediaProcessingJob.objects.create(
        media_file=media_file,
        job_type=job_type,
        status='pending',
        priority=priority
    )

def process_job_e2ee(job):
    """Process a single job with E2EE support"""
    try:
        job.status = 'processing'
        job.started_at = timezone.now()
        job.save()
        
        if job.job_type == 'thumbnail':
            result = generate_thumbnail_encrypted(job.media_file)
        elif job.job_type == 'metadata_extraction':
            result = extract_metadata_encrypted(job.media_file)
        else:
            raise ValueError(f"Unknown job type: {job.job_type}")
        
        job.status = 'completed'
        job.completed_at = timezone.now()
        job.result_data = result
        job.save()
        
        return True
        
    except Exception as e:
        job.status = 'failed'
        job.completed_at = timezone.now()
        job.error_message = str(e)
        job.retry_count += 1
        job.save()
        
        logger.error(f"Job {job.id} failed: {str(e)}")
        
        # Retry if under limit
        if job.retry_count < job.max_retries:
            job.status = 'pending'
            job.save()
            
            # Re-queue with delay
            from celery import current_app
            current_app.send_task(
                'media.tasks.retry_job',
                args=[str(job.id)],
                countdown=60 * job.retry_count
            )
        
        return False

@shared_task
def retry_job(job_id):
    """Retry a failed processing job"""
    try:
        job = MediaProcessingJob.objects.get(id=job_id, status='pending')
        process_job_e2ee(job)
    except MediaProcessingJob.DoesNotExist:
        pass

@shared_task
def cleanup_expired_downloads():
    """Clean up expired download tokens"""
    from .models import MediaDownload
    expired_count = MediaDownload.objects.filter(
        expires_at__lt=timezone.now()
    ).delete()[0]
    
    logger.info(f"Cleaned up {expired_count} expired download tokens")

@shared_task
def cleanup_orphaned_chunks():
    """Clean up orphaned upload chunks (older than 24 hours)"""
    from .models import MediaChunk
    from datetime import timedelta
    
    cutoff_time = timezone.now() - timedelta(hours=24)
    orphaned_count = MediaChunk.objects.filter(
        uploaded_at__lt=cutoff_time
    ).delete()[0]
    
    logger.info(f"Cleaned up {orphaned_count} orphaned upload chunks")

# Import utility functions
from .utils import generate_thumbnail_encrypted, extract_metadata_encrypted
