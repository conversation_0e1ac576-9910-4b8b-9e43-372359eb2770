#!/bin/bash
# Comprehensive Service Setup Script for Chat Application (Unix/Linux/macOS)
# This script starts Django backend, React frontend, and Node.js socket server in parallel

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Service configuration
declare -A services
services["Django Backend"]="backend:6000:python manage.py runserver 6000:venv/bin/activate:django"
services["React Frontend"]="frontend:5000:pnpm run dev::node"
services["Socket Server"]="socket-server:7000:pnpm run dev::node"

# Array to store background process IDs
declare -a pids=()
declare -A service_status

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if port is available
check_port() {
    local port=$1
    if command -v nc >/dev/null 2>&1; then
        ! nc -z localhost $port >/dev/null 2>&1
    elif command -v telnet >/dev/null 2>&1; then
        ! timeout 1 telnet localhost $port >/dev/null 2>&1
    else
        # Fallback: assume port is available
        return 0
    fi
}

# Function to start a service
start_service() {
    local service_name="$1"
    local config="${services[$service_name]}"
    
    IFS=':' read -r directory port command venv_path service_type <<< "$config"
    
    print_color $YELLOW "Starting $service_name on port $port..."
    
    # Check if port is available
    if ! check_port $port; then
        print_color $RED "❌ Port $port is already in use. Cannot start $service_name."
        service_status["$service_name"]="Failed - Port in use"
        return 1
    fi
    
    # Check if directory exists
    if [ ! -d "$directory" ]; then
        print_color $RED "❌ Directory '$directory' not found. Cannot start $service_name."
        service_status["$service_name"]="Failed - Directory not found"
        return 1
    fi
    
    # Change to service directory
    cd "$directory" || {
        print_color $RED "❌ Cannot change to directory '$directory'"
        service_status["$service_name"]="Failed - Cannot access directory"
        return 1
    }
    
    # Service-specific setup
    if [ "$service_type" = "django" ]; then
        # Check if virtual environment exists
        if [ ! -f "$venv_path" ]; then
            print_color $RED "❌ Virtual environment not found at '$venv_path'. Cannot start $service_name."
            service_status["$service_name"]="Failed - Virtual environment not found"
            cd ..
            return 1
        fi
        
        # Activate virtual environment and start Django
        {
            source "$venv_path"
            exec $command
        } > "../${service_name// /_}.log" 2>&1 &
        
    elif [ "$service_type" = "node" ]; then
        # Check if package.json exists
        if [ ! -f "package.json" ]; then
            print_color $RED "❌ package.json not found in '$directory'. Cannot start $service_name."
            service_status["$service_name"]="Failed - package.json not found"
            cd ..
            return 1
        fi
        
        # Check if pnpm is installed
        if ! command -v pnpm >/dev/null 2>&1; then
            print_color $RED "❌ pnpm is not installed or not in PATH. Cannot start $service_name."
            service_status["$service_name"]="Failed - pnpm not found"
            cd ..
            return 1
        fi
        
        # Start Node.js service
        exec $command > "../${service_name// /_}.log" 2>&1 &
    fi
    
    local pid=$!
    pids+=($pid)
    
    # Wait a moment to check if the process started successfully
    sleep 2
    
    if kill -0 $pid 2>/dev/null; then
        print_color $GREEN "✅ $service_name started successfully (PID: $pid)"
        service_status["$service_name"]="Running"
        cd ..
        return 0
    else
        print_color $RED "❌ Failed to start $service_name"
        service_status["$service_name"]="Failed to start"
        cd ..
        return 1
    fi
}

# Function to display service status
show_service_status() {
    print_color $CYAN "\n=== Service Status ==="
    for service_name in "${!services[@]}"; do
        local config="${services[$service_name]}"
        IFS=':' read -r directory port command venv_path service_type <<< "$config"
        local status="${service_status[$service_name]}"
        
        if [ "$status" = "Running" ]; then
            print_color $GREEN "$service_name (Port $port): $status"
        else
            print_color $RED "$service_name (Port $port): $status"
        fi
    done
    print_color $CYAN "=====================\n"
}

# Function to cleanup processes
cleanup() {
    print_color $YELLOW "\nStopping all services..."
    
    for pid in "${pids[@]}"; do
        if kill -0 $pid 2>/dev/null; then
            print_color $YELLOW "Stopping process $pid..."
            kill -TERM $pid 2>/dev/null
            
            # Wait for graceful shutdown
            local count=0
            while kill -0 $pid 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                ((count++))
            done
            
            # Force kill if still running
            if kill -0 $pid 2>/dev/null; then
                print_color $RED "Force killing process $pid..."
                kill -KILL $pid 2>/dev/null
            fi
            
            print_color $GREEN "✅ Stopped process $pid"
        fi
    done
    
    print_color $GREEN "All services stopped."
    exit 0
}

# Trap signals for graceful shutdown
trap cleanup SIGINT SIGTERM EXIT

# Main execution
print_color $CYAN "🚀 Chat Application Service Setup"
print_color $CYAN "================================="

# Initialize service status
for service_name in "${!services[@]}"; do
    service_status["$service_name"]="Not started"
done

# Start all services
running_count=0
for service_name in "${!services[@]}"; do
    if start_service "$service_name"; then
        ((running_count++))
    fi
    sleep 1  # Brief pause between service starts
done

# Display initial status
show_service_status

# Check if any services are running
if [ $running_count -eq 0 ]; then
    print_color $RED "❌ No services started successfully. Exiting..."
    exit 1
fi

total_services=${#services[@]}
print_color $GREEN "✅ $running_count out of $total_services services started successfully."
print_color $CYAN "Press Ctrl+C to stop all services and exit."
print_color $CYAN "Service logs are available in the root directory.\n"

# Display service URLs
print_color $BLUE "Services available at:"
for service_name in "${!services[@]}"; do
    local config="${services[$service_name]}"
    IFS=':' read -r directory port command venv_path service_type <<< "$config"
    if [ "${service_status[$service_name]}" = "Running" ]; then
        print_color $BLUE "  - $service_name: http://localhost:$port"
    fi
done
echo

# Keep the script running and monitor services
while true; do
    sleep 10
    
    # Check if any processes have died
    active_pids=()
    for pid in "${pids[@]}"; do
        if kill -0 $pid 2>/dev/null; then
            active_pids+=($pid)
        else
            print_color $YELLOW "⚠️  Process $pid has stopped"
        fi
    done
    
    if [ ${#active_pids[@]} -lt $running_count ]; then
        print_color $YELLOW "⚠️  Some services may have stopped. Current status:"
        show_service_status
        running_count=${#active_pids[@]}
    fi
    
    # Exit if all services have stopped
    if [ ${#active_pids[@]} -eq 0 ]; then
        print_color $RED "❌ All services have stopped. Exiting..."
        exit 1
    fi
done