// frontend/src/components/ui/Icon.tsx
import React from 'react';
import {
  User,
  MessageCircle,
  Send,
  Settings,
  LogOut,
  Eye,
  EyeOff,
  Search,
  Plus,
  X,
  Check,
  AlertCircle,
  Loader2
} from 'lucide-react';

// Icon component mapping for consistent usage
export const Icons = {
  user: User,
  message: MessageCircle,
  send: Send,
  settings: Settings,
  logout: LogOut,
  eye: Eye,
  eyeOff: EyeOff,
  search: Search,
  plus: Plus,
  close: X,
  check: Check,
  alert: AlertCircle,
  spinner: Loader2,
} as const;

interface IconProps {
  name: keyof typeof Icons;
  size?: number;
  className?: string;
}

export const Icon: React.FC<IconProps> = ({ name, size = 20, className = '' }) => {
  const IconComponent = Icons[name];
  return <IconComponent size={size} className={className} />;
};
