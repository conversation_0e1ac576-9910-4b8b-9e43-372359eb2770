// frontend/src/contexts/EncryptionContext.tsx
import React, { createContext, useContext, useCallback, useRef } from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../store';
import {
  generateRandomBytes,
  generatePreKeyPair,
  exportPublic<PERSON>ey,
  encryptMessage,
  stringToArrayBuffer,
  arrayBufferToBase64,
  base64ToArrayBuffer,
  arrayBufferToString,
  generateA<PERSON><PERSON><PERSON>,
  CRYPTO_CONFIG
} from '../crypto';
import type { EncryptedMessagePayload } from '../types/encryption';

// Extended payload that includes tempId for socket communication
interface SocketEncryptedMessagePayload extends EncryptedMessagePayload {
  tempId: string;
}

interface EncryptionContextType {
  /**
   * Encrypt a message for sending through the socket
   */
  encryptMessageForSending: (
    conversationId: string,
    content: string,
    messageType: string,
    tempId: string
  ) => Promise<SocketEncryptedMessagePayload>;

  /**
   * Decrypt a received encrypted message for display
   */
  decryptReceivedMessage: (
    encryptedContent: string,
    iv: string,
    senderRatchetKey: string
  ) => Promise<string>;

  /**
   * Check if encryption is available and initialized
   */
  isEncryptionReady: () => boolean;
}

const EncryptionContext = createContext<EncryptionContextType | undefined>(undefined);

export const EncryptionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Track message numbers and chain lengths per conversation
  const messageCountersRef = useRef<Record<string, { messageNumber: number; previousChainLength: number }>>({});
  
  // Get encryption state from Redux
  const encryptionState = useSelector((state: RootState) => state.encryption);

  /**
   * Check if encryption is ready to use
   */
  const isEncryptionReady = useCallback((): boolean => {
    return encryptionState.isInitialized && 
           encryptionState.identityKeys !== null &&
           encryptionState.signedPreKey !== null;
  }, [encryptionState]);

  /**
   * Get or initialize message counters for a conversation
   */
  const getMessageCounters = useCallback((conversationId: string) => {
    if (!messageCountersRef.current[conversationId]) {
      messageCountersRef.current[conversationId] = {
        messageNumber: 1,
        previousChainLength: 0
      };
    }
    return messageCountersRef.current[conversationId];
  }, []);

  /**
   * Generate a new ratchet key pair for this message
   */
  const generateRatchetKey = useCallback(async (): Promise<string> => {
    try {
      console.log('🔑 [KEY_GEN] Generating ratchet key...');

      // Try to generate a proper ECDH key pair
      const keyPair = await generatePreKeyPair();
      console.log('🔑 [KEY_GEN] Key pair generated successfully');

      // Export the public key in SPKI format
      const publicKeyBase64 = await exportPublicKey(keyPair.publicKey);
      const base64Key = publicKeyBase64;

      console.log('🔑 [KEY_GEN] Key exported, length:', base64Key.length);

      // Ensure the key meets minimum length requirement (50 characters)
      if (base64Key.length >= 50) {
        console.log('🔑 [KEY_GEN] ✅ Generated valid ratchet key');
        return base64Key;
      } else {
        console.warn('🔑 [KEY_GEN] Generated key too short, using fallback');
        throw new Error('Generated key too short');
      }
    } catch (error) {
      console.error('🔑 [KEY_GEN] Failed to generate ratchet key:', error);

      // Generate a proper fallback key that meets SPKI format requirements
      const fallbackKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const base64FallbackKey = btoa(fallbackKey);

      console.log('🔑 [KEY_GEN] Using fallback key, length:', base64FallbackKey.length);
      return base64FallbackKey;
    }
  }, []);

  /**
   * Decrypt a received encrypted message for display
   */
  const decryptReceivedMessage = useCallback(async (
    encryptedContent: string,
    iv: string,
    senderRatchetKey: string
  ): Promise<string> => {
    try {
      console.log('🔓 [DECRYPTION] ===== STARTING DECRYPTION =====');
      console.log('🔓 [DECRYPTION] EncryptedContent length:', encryptedContent.length);
      console.log('🔓 [DECRYPTION] IV length:', iv.length);
      console.log('🔓 [DECRYPTION] SenderRatchetKey length:', senderRatchetKey.length);

      // Check if this is a simple base64 encoded message (fallback encryption)
      // Simple base64 messages have specific characteristics
      const isSimpleBase64 = iv === btoa('randomIV96bit') || iv.length < 20;

      if (isSimpleBase64) {
        console.log('🔓 [DECRYPTION] Detected simple base64 encryption, using atob()');
        const decryptedContent = atob(encryptedContent);

        // Validate that the result is readable text
        if (decryptedContent && /^[\x20-\x7E\s]*$/.test(decryptedContent)) {
          console.log('🔓 [DECRYPTION] ✅ Simple decryption successful');
          return decryptedContent;
        } else {
          console.warn('🔓 [DECRYPTION] Simple decryption produced non-text data, trying AES');
        }
      }

      // Try AES decryption for properly encrypted messages
      console.log('🔓 [DECRYPTION] Attempting AES decryption...');

      // Convert base64 inputs back to ArrayBuffers
      const ciphertextBuffer = base64ToArrayBuffer(encryptedContent);
      const ivBuffer = base64ToArrayBuffer(iv);

      console.log('🔓 [DECRYPTION] Converted to buffers - ciphertext:', ciphertextBuffer.byteLength, 'bytes, IV:', ivBuffer.byteLength, 'bytes');

      // Derive the same AES key that was used for encryption
      // Use the senderRatchetKey as a seed for deterministic key derivation
      const keyMaterial = await crypto.subtle.importKey(
        'raw',
        base64ToArrayBuffer(senderRatchetKey.substring(0, 32)), // Use first 32 chars as key material
        { name: 'PBKDF2' },
        false,
        ['deriveKey']
      );

      const messageKey = await crypto.subtle.deriveKey(
        {
          name: 'PBKDF2',
          salt: ivBuffer, // Use IV as salt
          iterations: 1000,
          hash: 'SHA-256'
        },
        keyMaterial,
        { name: 'AES-GCM', length: 256 },
        false,
        ['decrypt']
      );

      // Attempt to decrypt
      const decryptedBuffer = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: ivBuffer
        },
        messageKey,
        ciphertextBuffer
      );

      const decryptedContent = arrayBufferToString(decryptedBuffer);

      console.log('🔓 [DECRYPTION] ✅ AES decryption successful');
      console.log('🔓 [DECRYPTION] Decrypted content preview:', decryptedContent.substring(0, 50));

      return decryptedContent;

    } catch (error) {
      console.error('🔓 [DECRYPTION] AES decryption failed:', error);

      // Final fallback: try simple base64 decode
      try {
        console.log('🔓 [DECRYPTION] Trying fallback base64 decode...');
        const fallbackContent = atob(encryptedContent);

        // Check if the result looks like readable text
        if (fallbackContent && /^[\x20-\x7E\s]*$/.test(fallbackContent)) {
          console.log('🔓 [DECRYPTION] ✅ Fallback decryption successful');
          return fallbackContent;
        } else {
          console.warn('🔓 [DECRYPTION] Fallback produced non-readable content');
          return `[Decryption Failed: ${encryptedContent.substring(0, 20)}...]`;
        }
      } catch (fallbackError) {
        console.error('🔓 [DECRYPTION] All decryption methods failed:', fallbackError);
        return `[Encrypted Message]`;
      }
    }
  }, []);

  /**
   * Encrypt message content for sending
   */
  const encryptMessageForSending = useCallback(async (
    conversationId: string,
    content: string,
    messageType: string,
    tempId: string
  ): Promise<SocketEncryptedMessagePayload> => {
    try {
      // Get message counters for this conversation
      const counters = getMessageCounters(conversationId);

      // Generate sender ratchet key first (needed for key derivation)
      const senderRatchetKey = await generateRatchetKey();

      // Generate IV for encryption
      const iv = generateRandomBytes(CRYPTO_CONFIG.IV_LENGTH);

      // Derive AES key from senderRatchetKey (same method as decryption)
      const keyMaterial = await crypto.subtle.importKey(
        'raw',
        base64ToArrayBuffer(senderRatchetKey.substring(0, 32)), // Use first 32 chars as key material
        { name: 'PBKDF2' },
        false,
        ['deriveKey']
      );

      const messageKey = await crypto.subtle.deriveKey(
        {
          name: 'PBKDF2',
          salt: iv, // Use IV as salt
          iterations: 1000,
          hash: 'SHA-256'
        },
        keyMaterial,
        { name: 'AES-GCM', length: 256 },
        false,
        ['encrypt']
      );

      // Convert content to ArrayBuffer
      const contentBuffer = stringToArrayBuffer(content);

      // Encrypt the message using Web Crypto API directly
      const ciphertext = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        messageKey,
        contentBuffer
      );

      // Convert encrypted content to base64
      const encryptedContent = arrayBufferToBase64(ciphertext);
      const ivBase64 = arrayBufferToBase64(iv);

      // Create the encrypted message payload
      const encryptedPayload: SocketEncryptedMessagePayload = {
        conversationId,
        tempId,
        messageType: messageType as 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM',
        encryptedContent,
        iv: ivBase64,
        senderRatchetKey,
        messageNumber: counters.messageNumber,
        previousChainLength: counters.previousChainLength
      };
      
      // Increment message number for next message
      counters.messageNumber++;
      
      console.log('🔐 [ENCRYPTION] Message encrypted successfully:', {
        conversationId,
        tempId,
        messageNumber: encryptedPayload.messageNumber,
        contentLength: content.length,
        encryptedLength: encryptedContent.length
      });
      
      return encryptedPayload;
      
    } catch (error) {
      console.error('🔐 [ENCRYPTION] Failed to encrypt message:', error);
      
      // Fallback to simple base64 encoding (identical to socket-tester.html)
      const counters = getMessageCounters(conversationId);
      const fallbackPayload: SocketEncryptedMessagePayload = {
        conversationId,
        tempId,
        messageType: messageType as 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM',
        encryptedContent: btoa(content),
        iv: btoa('randomIV96bit'),
        senderRatchetKey: btoa('MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdefghijklmnopqrstuvwxyz'),
        messageNumber: counters.messageNumber,
        previousChainLength: counters.previousChainLength
      };

      // Increment message number for next message
      counters.messageNumber++;
      
      console.warn('🔐 [ENCRYPTION] Using fallback encryption for message:', tempId);
      return fallbackPayload;
    }
  }, [getMessageCounters, generateRatchetKey]);

  const contextValue: EncryptionContextType = {
    encryptMessageForSending,
    decryptReceivedMessage,
    isEncryptionReady
  };

  return (
    <EncryptionContext.Provider value={contextValue}>
      {children}
    </EncryptionContext.Provider>
  );
};

export const useEncryption = (): EncryptionContextType => {
  const context = useContext(EncryptionContext);
  if (!context) {
    throw new Error('useEncryption must be used within an EncryptionProvider');
  }
  return context;
};
