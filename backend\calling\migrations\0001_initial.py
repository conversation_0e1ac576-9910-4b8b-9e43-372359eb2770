# Generated by Django 5.2.4 on 2025-09-06 11:57

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('messaging', '0005_message_has_media_message_media_count_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Call',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('call_type', models.CharField(choices=[('audio', 'Audio Call'), ('video', 'Video Call')], max_length=10)),
                ('status', models.CharField(choices=[('initiated', 'Initiated'), ('ringing', 'Ringing'), ('answered', 'Answered'), ('active', 'Active'), ('ended', 'Ended'), ('missed', 'Missed'), ('declined', 'Declined'), ('failed', 'Failed')], default='initiated', max_length=15)),
                ('initiated_at', models.DateTimeField(auto_now_add=True)),
                ('answered_at', models.DateTimeField(blank=True, null=True)),
                ('ended_at', models.DateTimeField(blank=True, null=True)),
                ('duration', models.DurationField(blank=True, null=True)),
                ('session_id', models.CharField(max_length=100, unique=True)),
                ('caller_sdp', models.TextField(blank=True, null=True)),
                ('callee_sdp', models.TextField(blank=True, null=True)),
                ('quality_rating', models.IntegerField(blank=True, null=True)),
                ('quality_issues', models.JSONField(default=list)),
                ('metadata', models.JSONField(default=dict)),
                ('callee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_calls', to=settings.AUTH_USER_MODEL)),
                ('caller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='initiated_calls', to=settings.AUTH_USER_MODEL)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calls', to='messaging.conversation')),
            ],
            options={
                'db_table': 'calls',
                'ordering': ['-initiated_at'],
            },
        ),
        migrations.CreateModel(
            name='CallEvent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('event_type', models.CharField(choices=[('call_initiated', 'Call Initiated'), ('call_ringing', 'Call Ringing'), ('call_answered', 'Call Answered'), ('call_ended', 'Call Ended'), ('call_declined', 'Call Declined'), ('call_failed', 'Call Failed'), ('ice_candidate', 'ICE Candidate'), ('media_toggle', 'Media Toggle'), ('quality_issue', 'Quality Issue')], max_length=20)),
                ('event_data', models.JSONField(default=dict)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('call', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='events', to='calling.call')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='call_events', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'call_events',
                'ordering': ['timestamp'],
            },
        ),
        migrations.CreateModel(
            name='CallParticipant',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('invited', 'Invited'), ('joined', 'Joined'), ('left', 'Left'), ('declined', 'Declined')], default='invited', max_length=15)),
                ('joined_at', models.DateTimeField(blank=True, null=True)),
                ('left_at', models.DateTimeField(blank=True, null=True)),
                ('audio_enabled', models.BooleanField(default=True)),
                ('video_enabled', models.BooleanField(default=True)),
                ('screen_sharing', models.BooleanField(default=False)),
                ('call', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='participants', to='calling.call')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='call_participations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'call_participants',
            },
        ),
        migrations.CreateModel(
            name='CallQualityMetric',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('packet_loss', models.FloatField(blank=True, null=True)),
                ('jitter', models.FloatField(blank=True, null=True)),
                ('round_trip_time', models.FloatField(blank=True, null=True)),
                ('bandwidth_upload', models.IntegerField(blank=True, null=True)),
                ('bandwidth_download', models.IntegerField(blank=True, null=True)),
                ('audio_level', models.FloatField(blank=True, null=True)),
                ('audio_quality_score', models.FloatField(blank=True, null=True)),
                ('video_resolution', models.CharField(blank=True, max_length=20, null=True)),
                ('video_framerate', models.FloatField(blank=True, null=True)),
                ('video_quality_score', models.FloatField(blank=True, null=True)),
                ('recorded_at', models.DateTimeField(auto_now_add=True)),
                ('call', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quality_metrics', to='calling.call')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='call_quality_reports', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'call_quality_metrics',
            },
        ),
        migrations.AddIndex(
            model_name='call',
            index=models.Index(fields=['conversation', 'status'], name='calls_convers_72e827_idx'),
        ),
        migrations.AddIndex(
            model_name='call',
            index=models.Index(fields=['caller', 'initiated_at'], name='calls_caller__0860d2_idx'),
        ),
        migrations.AddIndex(
            model_name='call',
            index=models.Index(fields=['callee', 'initiated_at'], name='calls_callee__e66bec_idx'),
        ),
        migrations.AddIndex(
            model_name='call',
            index=models.Index(fields=['session_id'], name='calls_session_02b3bf_idx'),
        ),
        migrations.AddIndex(
            model_name='callevent',
            index=models.Index(fields=['call', 'timestamp'], name='call_events_call_id_70a3f4_idx'),
        ),
        migrations.AddIndex(
            model_name='callevent',
            index=models.Index(fields=['event_type', 'timestamp'], name='call_events_event_t_adf59e_idx'),
        ),
        migrations.AddIndex(
            model_name='callparticipant',
            index=models.Index(fields=['call', 'status'], name='call_partic_call_id_d59434_idx'),
        ),
        migrations.AddIndex(
            model_name='callparticipant',
            index=models.Index(fields=['user', 'joined_at'], name='call_partic_user_id_028821_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='callparticipant',
            unique_together={('call', 'user')},
        ),
        migrations.AddIndex(
            model_name='callqualitymetric',
            index=models.Index(fields=['call', 'recorded_at'], name='call_qualit_call_id_309dd2_idx'),
        ),
        migrations.AddIndex(
            model_name='callqualitymetric',
            index=models.Index(fields=['user', 'recorded_at'], name='call_qualit_user_id_0df7c8_idx'),
        ),
    ]
