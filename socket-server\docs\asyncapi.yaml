asyncapi: 3.0.0
info:
  title: ChatApp Socket Server API
  version: 3.0.0
  description: |
    Real-time messaging API with end-to-end encryption support.
    This API handles both encrypted and plaintext messages for backward compatibility.
    
    ## Authentication
    All connections require JWT authentication via the `auth.token` field during connection.
    
    ## Encryption Support
    - Messages can be sent as encrypted (Phase 3) or plaintext (Phase 2 compatibility)
    - Server routes encrypted messages without decryption
    - Key exchange coordination for session establishment
    - Encryption status checking for conversations
    
  contact:
    name: ChatApp Development Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  development:
    host: localhost:3001
    protocol: ws
    description: Development server
  production:
    host: api.chatapp.com
    protocol: wss
    description: Production server

channels:
  connection:
    address: /
    messages:
      connect:
        $ref: '#/components/messages/Connect'
      disconnect:
        $ref: '#/components/messages/Disconnect'
      error:
        $ref: '#/components/messages/Error'

  messaging:
    address: /messaging
    messages:
      send_message:
        $ref: '#/components/messages/SendMessage'
      new_message:
        $ref: '#/components/messages/NewMessage'
      message_sent:
        $ref: '#/components/messages/MessageSent'
      message_failed:
        $ref: '#/components/messages/MessageFailed'

  conversations:
    address: /conversations
    messages:
      join_conversation:
        $ref: '#/components/messages/JoinConversation'
      leave_conversation:
        $ref: '#/components/messages/LeaveConversation'

  typing:
    address: /typing
    messages:
      typing_start:
        $ref: '#/components/messages/TypingStart'
      typing_stop:
        $ref: '#/components/messages/TypingStop'
      typing_indicator:
        $ref: '#/components/messages/TypingIndicator'

  encryption:
    address: /encryption
    messages:
      key_exchange_request:
        $ref: '#/components/messages/KeyExchangeRequest'
      key_exchange_response:
        $ref: '#/components/messages/KeyExchangeResponse'
      encryption_status_check:
        $ref: '#/components/messages/EncryptionStatusCheck'
      encryption_status_response:
        $ref: '#/components/messages/EncryptionStatusResponse'

operations:
  sendMessage:
    action: send
    channel:
      $ref: '#/channels/messaging'
    messages:
      - $ref: '#/components/messages/SendMessage'
    reply:
      channel:
        $ref: '#/channels/messaging'
      messages:
        - $ref: '#/components/messages/MessageSent'
        - $ref: '#/components/messages/MessageFailed'

  receiveMessage:
    action: receive
    channel:
      $ref: '#/channels/messaging'
    messages:
      - $ref: '#/components/messages/NewMessage'

  joinConversation:
    action: send
    channel:
      $ref: '#/channels/conversations'
    messages:
      - $ref: '#/components/messages/JoinConversation'

  requestKeyExchange:
    action: send
    channel:
      $ref: '#/channels/encryption'
    messages:
      - $ref: '#/components/messages/KeyExchangeRequest'
    reply:
      channel:
        $ref: '#/channels/encryption'
      messages:
        - $ref: '#/components/messages/KeyExchangeResponse'

components:
  messages:
    Connect:
      name: connect
      title: Connection Event
      summary: Client connects to socket server
      payload:
        $ref: '#/components/schemas/AuthPayload'

    Disconnect:
      name: disconnect
      title: Disconnection Event
      summary: Client disconnects from socket server

    Error:
      name: error
      title: Error Event
      summary: Server error response
      payload:
        $ref: '#/components/schemas/ErrorResponse'

    SendMessage:
      name: send_message
      title: Send Message
      summary: Send a message (encrypted or plaintext)
      payload:
        $ref: '#/components/schemas/SendMessagePayload'

    NewMessage:
      name: new_message
      title: New Message
      summary: Broadcast new message to conversation participants
      payload:
        $ref: '#/components/schemas/NewMessagePayload'

    MessageSent:
      name: message_sent
      title: Message Sent Confirmation
      summary: Confirmation that message was sent successfully
      payload:
        $ref: '#/components/schemas/MessageSentPayload'

    MessageFailed:
      name: message_failed
      title: Message Failed
      summary: Notification that message sending failed
      payload:
        $ref: '#/components/schemas/MessageFailedPayload'

    JoinConversation:
      name: join_conversation
      title: Join Conversation
      summary: Join a conversation room
      payload:
        $ref: '#/components/schemas/JoinConversationPayload'

    LeaveConversation:
      name: leave_conversation
      title: Leave Conversation
      summary: Leave a conversation room
      payload:
        $ref: '#/components/schemas/LeaveConversationPayload'

    TypingStart:
      name: typing_start
      title: Start Typing
      summary: Indicate user started typing
      payload:
        $ref: '#/components/schemas/TypingPayload'

    TypingStop:
      name: typing_stop
      title: Stop Typing
      summary: Indicate user stopped typing
      payload:
        $ref: '#/components/schemas/TypingPayload'

    TypingIndicator:
      name: typing_indicator
      title: Typing Indicator
      summary: Broadcast typing status to conversation participants
      payload:
        $ref: '#/components/schemas/TypingIndicatorPayload'

    KeyExchangeRequest:
      name: key_exchange_request
      title: Key Exchange Request
      summary: Request another user's key bundle for encryption
      payload:
        $ref: '#/components/schemas/KeyExchangeRequestPayload'

    KeyExchangeResponse:
      name: key_exchange_response
      title: Key Exchange Response
      summary: Response with key bundle or error
      payload:
        $ref: '#/components/schemas/KeyExchangeResponsePayload'

    EncryptionStatusCheck:
      name: encryption_status_check
      title: Encryption Status Check
      summary: Check encryption status for a conversation
      payload:
        $ref: '#/components/schemas/EncryptionStatusCheckPayload'

    EncryptionStatusResponse:
      name: encryption_status_response
      title: Encryption Status Response
      summary: Response with conversation encryption status
      payload:
        $ref: '#/components/schemas/EncryptionStatusResponsePayload'

  schemas:
    AuthPayload:
      type: object
      properties:
        token:
          type: string
          description: JWT authentication token
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      required:
        - token

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error message
        code:
          type: string
          description: Error code
        details:
          type: object
          description: Additional error details
      required:
        - error
      example:
        error: "Invalid message data"
        code: "VALIDATION_ERROR"
        details:
          field: "encryptedContent"

    SendMessagePayload:
      type: object
      properties:
        conversationId:
          type: string
          format: uuid
          description: Target conversation ID
        tempId:
          type: string
          description: Temporary ID for optimistic UI updates
        messageType:
          type: string
          enum: [TEXT, IMAGE, FILE, SYSTEM]
          default: TEXT
        # Plaintext fields (Phase 2 compatibility)
        content:
          type: string
          description: Message content (for plaintext messages)
        # Encryption fields (Phase 3)
        encryptedContent:
          type: string
          format: base64
          description: AES-GCM encrypted message content
        iv:
          type: string
          format: base64
          description: 96-bit initialization vector
        senderRatchetKey:
          type: string
          format: base64
          description: Sender's DH ratchet public key (SPKI format)
        messageNumber:
          type: integer
          minimum: 0
          description: Message counter in Double Ratchet
        previousChainLength:
          type: integer
          minimum: 0
          description: Previous chain length in Double Ratchet
      required:
        - conversationId
      example:
        conversationId: "550e8400-e29b-41d4-a716-************"
        tempId: "temp-123"
        encryptedContent: "dGhpcyBpcyBmYWtlIGVuY3J5cHRlZCBjb250ZW50"
        iv: "cmFuZG9tSVY5NmJpdA=="
        senderRatchetKey: "ZmFrZVJhdGNoZXRLZXlTUEtJRm9ybWF0"
        messageNumber: 1
        previousChainLength: 0
        messageType: "TEXT"

    NewMessagePayload:
      type: object
      properties:
        id:
          type: string
          format: uuid
        conversationId:
          type: string
          format: uuid
        sender:
          $ref: '#/components/schemas/UserInfo'
        messageType:
          type: string
          enum: [TEXT, IMAGE, FILE, SYSTEM]
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        status:
          type: string
          enum: [SENT, DELIVERED, READ, FAILED]
        isEncrypted:
          type: boolean
          description: Whether this message is encrypted
        # Conditional fields based on encryption status
        content:
          type: string
          description: Message content (only for plaintext messages)
        encryptedContent:
          type: string
          format: base64
          description: Encrypted content (only for encrypted messages)
        iv:
          type: string
          format: base64
          description: Initialization vector (only for encrypted messages)
        senderRatchetKey:
          type: string
          format: base64
          description: Sender's ratchet key (only for encrypted messages)
        messageNumber:
          type: integer
          description: Message number (only for encrypted messages)
        previousChainLength:
          type: integer
          description: Previous chain length (only for encrypted messages)
      required:
        - id
        - conversationId
        - sender
        - messageType
        - createdAt
        - status
        - isEncrypted

    MessageSentPayload:
      type: object
      properties:
        tempId:
          type: string
          description: Original temporary ID
        messageId:
          type: string
          format: uuid
          description: Assigned message ID
        status:
          type: string
          enum: [DELIVERED]
        isEncrypted:
          type: boolean
          description: Whether the message was encrypted
      required:
        - messageId
        - status
        - isEncrypted

    MessageFailedPayload:
      type: object
      properties:
        tempId:
          type: string
          description: Original temporary ID
        error:
          type: string
          description: Failure reason
      required:
        - error

    JoinConversationPayload:
      type: object
      properties:
        conversationId:
          type: string
          format: uuid
      required:
        - conversationId

    LeaveConversationPayload:
      type: object
      properties:
        conversationId:
          type: string
          format: uuid
      required:
        - conversationId

    TypingPayload:
      type: object
      properties:
        conversationId:
          type: string
          format: uuid
      required:
        - conversationId

    TypingIndicatorPayload:
      type: object
      properties:
        conversationId:
          type: string
          format: uuid
        userId:
          type: string
          format: uuid
        username:
          type: string
        isTyping:
          type: boolean
      required:
        - conversationId
        - userId
        - username
        - isTyping

    KeyExchangeRequestPayload:
      type: object
      properties:
        targetUserId:
          type: string
          format: uuid
          description: User ID to request key bundle from
        conversationId:
          type: string
          format: uuid
          description: Conversation context
        ephemeralPublicKey:
          type: string
          format: base64
          description: Ephemeral ECDH public key for X3DH
      required:
        - targetUserId
        - conversationId
        - ephemeralPublicKey

    KeyExchangeResponsePayload:
      oneOf:
        - type: object
          properties:
            success:
              type: boolean
              const: true
            keyBundle:
              $ref: '#/components/schemas/KeyBundle'
          required:
            - success
            - keyBundle
        - type: object
          properties:
            error:
              type: string
            code:
              type: string
          required:
            - error

    KeyBundle:
      type: object
      properties:
        identityPublicKey:
          type: string
          format: base64
          description: ECDSA P-256 identity public key
        signedPrekey:
          type: object
          properties:
            id:
              type: integer
            publicKey:
              type: string
              format: base64
            signature:
              type: string
              format: base64
          required:
            - id
            - publicKey
            - signature
        oneTimePrekey:
          type: object
          properties:
            id:
              type: integer
            publicKey:
              type: string
              format: base64
          required:
            - id
            - publicKey
      required:
        - identityPublicKey
        - signedPrekey

    EncryptionStatusCheckPayload:
      type: object
      properties:
        conversationId:
          type: string
          format: uuid
      required:
        - conversationId

    EncryptionStatusResponsePayload:
      type: object
      properties:
        conversationId:
          type: string
          format: uuid
        isEncrypted:
          type: boolean
          description: Whether all participants have encryption enabled
        participants:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                format: uuid
              username:
                type: string
              hasEncryption:
                type: boolean
            required:
              - id
              - username
              - hasEncryption
      required:
        - conversationId
        - isEncrypted
        - participants

    UserInfo:
      type: object
      properties:
        id:
          type: string
          format: uuid
        username:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        profilePicture:
          type: string
          nullable: true
      required:
        - id
        - username
