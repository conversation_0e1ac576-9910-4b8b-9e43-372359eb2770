// frontend/src/components/Chat/SimpleMediaUpload.tsx
import React, { useState, useRef } from 'react';
import { Plus, X, Send, File, Image, Camera, Video, Music } from 'lucide-react';
import { mediaApi, type UploadProgress } from '../../services/mediaApi';
import { useSocket } from '../../contexts/SocketContext';
import { useMediaEncryption } from '../../hooks/useMediaEncryption';

interface SimpleMediaUploadProps {
  conversationId: string;
  onUploadComplete?: (files: any[]) => void;
  onUploadError?: (error: string) => void;
  disabled?: boolean;
}

interface SelectedFile {
  id: string;
  file: File;
  preview?: string;
  type: 'image' | 'video' | 'audio' | 'document' | 'other';
  messageId?: string;
  uploadProgress?: number;
  uploadError?: string;
}

const SimpleMediaUpload: React.FC<SimpleMediaUploadProps> = ({
  conversationId,
  onUploadComplete,
  onUploadError,
  disabled = false,
}) => {
  const [showDialog, setShowDialog] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<SelectedFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  // Get required services
  const { sendMessage } = useSocket();
  // Temporarily disable encryption requirement for testing
  // const { encryptFile, isReady: encryptionReady } = useMediaEncryption(conversationId);
  const encryptionReady = true; // Temporary bypass for testing

  const determineFileType = (mimeType: string): SelectedFile['type'] => {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('text')) {
      return 'document';
    }
    return 'other';
  };

  const handleFileSelect = (files: File[], source: string) => {
    console.log('🔍 Files selected:', files.length, 'from', source);
    
    const processedFiles: SelectedFile[] = files.map((file, index) => {
      const fileId = `file_${Date.now()}_${index}`;
      const fileType = determineFileType(file.type);
      
      let preview: string | undefined;
      if (file.type.startsWith('image/') || file.type.startsWith('video/')) {
        try {
          preview = URL.createObjectURL(file);
        } catch (error) {
          console.warn('Failed to create preview for:', file.name);
        }
      }

      return {
        id: fileId,
        file,
        preview,
        type: fileType,
      };
    });

    setSelectedFiles(processedFiles);
    setShowDialog(false);
    setShowPreview(true);
    console.log('📁 Files processed and preview shown');
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>, source: string) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      handleFileSelect(files, source);
    }
    // Reset input value to allow selecting the same file again
    event.target.value = '';
  };

  const handleRemoveFile = (fileId: string) => {
    setSelectedFiles(prev => {
      const updated = prev.filter(f => f.id !== fileId);
      // Revoke preview URL to prevent memory leaks
      const fileToRemove = prev.find(f => f.id === fileId);
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      return updated;
    });
  };

  const handleSend = async () => {
    console.log('🔍 handleSend called with:', {
      selectedFilesLength: selectedFiles.length,
      uploading,
      encryptionReady,
      conversationId
    });

    if (selectedFiles.length === 0) {
      console.log('❌ No files selected');
      return;
    }

    if (uploading) {
      console.log('❌ Already uploading');
      return;
    }

    if (!encryptionReady) {
      console.log('❌ Encryption not ready');
      return;
    }

    console.log('🚀 Starting real upload for', selectedFiles.length, 'files');
    setUploading(true);
    setUploadProgress(0);

    try {
      const uploadResults = [];
      let totalProgress = 0;

      for (let i = 0; i < selectedFiles.length; i++) {
        const selectedFile = selectedFiles[i];
        const baseProgress = (i / selectedFiles.length) * 100;

        console.log(`📤 Processing file ${i + 1}/${selectedFiles.length}: ${selectedFile.file.name}`);

        // Step 1: Prepare file for upload (skip encryption for now)
        console.log('📁 Preparing file for upload...');

        // For testing, we'll upload the file directly without encryption
        // TODO: Re-enable encryption once the session management is fixed
        const fileToUpload = selectedFile.file;
        const fileNonce = 'test-nonce'; // Placeholder
        const wrappedFileKey = 'test-wrapped-key'; // Placeholder
        const fileHash = 'test-hash'; // Placeholder

        // Step 2: Upload file to backend (backend will auto-create message)
        console.log('📡 Uploading file to backend...');

        const uploadProgressHandler = (progress: UploadProgress) => {
          const uploadProgress = baseProgress + 20 + (progress.percentage * 0.8); // 80% of total for upload
          setUploadProgress(uploadProgress);

          setSelectedFiles(prev => prev.map(f =>
            f.id === selectedFile.id ? { ...f, uploadProgress } : f
          ));
        };

        // Choose upload method based on file size
        let uploadResponse;
        if (mediaApi.shouldUseChunkedUpload(fileToUpload)) {
          uploadResponse = await mediaApi.uploadChunked(
            conversationId,
            fileToUpload,
            wrappedFileKey,
            fileNonce,
            fileHash,
            uploadProgressHandler
          );
        } else {
          uploadResponse = await mediaApi.uploadSimple(
            conversationId,
            fileToUpload,
            wrappedFileKey,
            fileNonce,
            fileHash,
            uploadProgressHandler
          );
        }

        console.log('✅ File uploaded successfully:', uploadResponse);

        // Update file with message ID from response
        setSelectedFiles(prev => prev.map(f =>
          f.id === selectedFile.id ? { ...f, messageId: uploadResponse.message_id } : f
        ));

        uploadResults.push({
          id: uploadResponse.media_id,
          messageId: uploadResponse.message_id,
          name: selectedFile.file.name,
          size: selectedFile.file.size,
          type: selectedFile.file.type,
          uploadResponse,
        });

        totalProgress = ((i + 1) / selectedFiles.length) * 100;
        setUploadProgress(totalProgress);
      }

      console.log('🎉 All files uploaded successfully:', uploadResults);
      onUploadComplete?.(uploadResults);

      // Clean up
      selectedFiles.forEach(file => {
        if (file.preview) {
          URL.revokeObjectURL(file.preview);
        }
      });

      setSelectedFiles([]);
      setShowPreview(false);
      setUploading(false);
      setUploadProgress(0);

    } catch (error) {
      console.error('❌ Upload failed:', error);
      onUploadError?.(error instanceof Error ? error.message : 'Upload failed');
      setUploading(false);
      setUploadProgress(0);

      // Update failed files
      setSelectedFiles(prev => prev.map(f => ({
        ...f,
        uploadError: error instanceof Error ? error.message : 'Upload failed'
      })));
    }
  };

  const handleClosePreview = () => {
    // Clean up preview URLs
    selectedFiles.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
    });
    setSelectedFiles([]);
    setShowPreview(false);
  };

  const getFileIcon = (type: SelectedFile['type']) => {
    switch (type) {
      case 'image': return <Image className="w-8 h-8 text-blue-500" />;
      case 'video': return <Video className="w-8 h-8 text-purple-500" />;
      case 'audio': return <Music className="w-8 h-8 text-green-500" />;
      case 'document': return <File className="w-8 h-8 text-red-500" />;
      default: return <File className="w-8 h-8 text-gray-500" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <>
      {/* Plus Button */}
      <button
        onClick={() => setShowDialog(true)}
        disabled={disabled}
        className={`p-2 rounded-lg transition-colors ${
          disabled 
            ? 'text-gray-400 cursor-not-allowed' 
            : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
        }`}
        title="Share media"
        data-testid="media-upload-button"
      >
        <Plus className="w-5 h-5" />
      </button>

      {/* Upload Dialog */}
      {showDialog && (
        <>
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setShowDialog(false)}
          />
          <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Share Media</h2>
                <button
                  onClick={() => setShowDialog(false)}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X className="w-5 h-5 text-gray-500" />
                </button>
              </div>
              <div className="p-4 space-y-2">
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="w-full flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors text-left"
                >
                  <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <File className="w-5 h-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">Upload file from device</div>
                    <div className="text-sm text-gray-500">Documents and other files</div>
                  </div>
                </button>
                <button
                  onClick={() => imageInputRef.current?.click()}
                  className="w-full flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors text-left"
                >
                  <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <Image className="w-5 h-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">Upload image from device</div>
                    <div className="text-sm text-gray-500">Photos and images</div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Preview Modal */}
      {showPreview && (
        <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl max-h-[90vh] w-full mx-4 flex flex-col">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                Preview Media ({selectedFiles.length} file{selectedFiles.length !== 1 ? 's' : ''})
              </h2>
              <button
                onClick={handleClosePreview}
                disabled={uploading}
                className="p-1 hover:bg-gray-100 rounded-full transition-colors disabled:opacity-50"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            <div className="flex-1 overflow-y-auto p-6">
              <div className="grid gap-4">
                {selectedFiles.map((file) => (
                  <div key={file.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        {getFileIcon(file.type)}
                        <div>
                          <div className="font-medium text-gray-900">{file.file.name}</div>
                          <div className="text-sm text-gray-500">{formatFileSize(file.file.size)}</div>
                        </div>
                      </div>
                      <button
                        onClick={() => handleRemoveFile(file.id)}
                        disabled={uploading}
                        className="text-red-600 hover:text-red-700 disabled:opacity-50"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                    {file.preview && (
                      <div className="mt-2">
                        {file.type === 'image' ? (
                          <img src={file.preview} alt={file.file.name} className="max-w-full h-32 object-cover rounded" />
                        ) : file.type === 'video' ? (
                          <video src={file.preview} className="max-w-full h-32 rounded" controls />
                        ) : null}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {uploading && (
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-blue-900">Uploading files...</span>
                    <span className="text-sm text-blue-700">{uploadProgress}%</span>
                  </div>
                  <div className="w-full bg-blue-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center justify-between p-4 border-t border-gray-200">
              <div className="text-sm text-gray-500">
                {selectedFiles.length} file{selectedFiles.length !== 1 ? 's' : ''} selected
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={handleClosePreview}
                  disabled={uploading}
                  className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSend}
                  disabled={uploading || selectedFiles.length === 0}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
                >
                  <Send className="w-4 h-4" />
                  <span>{uploading ? 'Sending...' : 'Send'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Hidden file inputs */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        className="hidden"
        accept=".pdf,.doc,.docx,.txt,.zip,.rar,.7z"
        onChange={(e) => handleFileInputChange(e, 'file')}
      />
      <input
        ref={imageInputRef}
        type="file"
        multiple
        className="hidden"
        accept="image/*"
        onChange={(e) => handleFileInputChange(e, 'image')}
      />
    </>
  );
};

export default SimpleMediaUpload;
