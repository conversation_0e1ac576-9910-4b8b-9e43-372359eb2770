# backend/messaging/urls.py
from django.urls import path
from . import views

urlpatterns = [
    path('conversations/', views.list_conversations, name='conversation-list'),
    path('conversations/create/', views.create_conversation, name='create-conversation'),
    path('conversations/<uuid:conversation_id>/messages/', views.get_conversation_messages, name='conversation-messages'),
    path('conversations/<uuid:conversation_id>/send/', views.send_message, name='send-message'),
    path('conversations/<uuid:conversation_id>/encryption-status/', views.get_conversation_encryption_status, name='conversation-encryption-status'),
    path('users/search/', views.search_users, name='search-users'),
    path('users/<uuid:user_id>/', views.get_user_profile, name='get-user-profile'),

    # PHASE 4: Group Management URLs
    path('groups/create/', views.create_group, name='create-group'),
    path('groups/<uuid:conversation_id>/add-member/', views.add_group_member, name='add-group-member'),
    path('groups/<uuid:conversation_id>/remove-member/<uuid:user_id>/', views.remove_group_member, name='remove-group-member'),
    path('groups/<uuid:conversation_id>/update/', views.update_group_info, name='update-group-info'),
    path('groups/<uuid:conversation_id>/leave/', views.leave_group, name='leave-group'),

    # PHASE 4: Group Encryption URLs
    path('groups/<uuid:conversation_id>/keys/', views.get_group_keys, name='get-group-keys'),
    path('groups/<uuid:conversation_id>/claim-key/', views.claim_group_key_endpoint, name='claim-group-key'),
    path('groups/<uuid:conversation_id>/send-message/', views.send_group_message, name='send-group-message'),
    path('messages/<uuid:message_id>/verify-signature/', views.verify_message_signature, name='verify-message-signature'),
    path('groups/<uuid:conversation_id>/rotate-keys/', views.rotate_group_keys_manual, name='rotate-group-keys-manual'),
]
