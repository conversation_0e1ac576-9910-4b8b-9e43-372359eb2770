# CI/CD Integration Guide for E2E Tests

## Overview

This guide explains how to integrate the Playwright E2E tests into various CI/CD pipelines and provides configuration examples for different platforms.

## GitHub Actions (Included)

The project includes a comprehensive GitHub Actions workflow at `.github/workflows/e2e-tests.yml`.

### Features
- ✅ Multi-browser testing (Chrome, Firefox, Safari)
- ✅ Service orchestration (Backend, Socket Server, Frontend)
- ✅ Test artifact collection
- ✅ Pull request comments with results
- ✅ Preview environment deployment
- ✅ Performance testing integration

### Workflow Triggers
```yaml
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM UTC
```

### Matrix Strategy
```yaml
strategy:
  fail-fast: false
  matrix:
    browser: [chromium, firefox, webkit]
```

## GitLab CI/CD

### `.gitlab-ci.yml`
```yaml
stages:
  - test
  - deploy

variables:
  NODE_VERSION: "18"
  PYTHON_VERSION: "3.11"

services:
  - postgres:13

e2e-tests:
  stage: test
  image: mcr.microsoft.com/playwright:v1.40.0-focal
  
  variables:
    POSTGRES_DB: chatapp_test
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: postgres
    DATABASE_URL: ******************************************/chatapp_test
  
  before_script:
    # Install Node.js dependencies
    - npm ci
    
    # Install Python dependencies
    - cd backend && pip install -r requirements.txt && cd ..
    
    # Setup database
    - cd backend
    - python manage.py migrate --settings=chatapp.settings.test
    - python manage.py collectstatic --noinput --settings=chatapp.settings.test
    - cd ..
    
    # Build frontend
    - cd frontend && npm ci && npm run build && cd ..
  
  script:
    # Start services
    - cd backend && python manage.py runserver 8000 --settings=chatapp.settings.test &
    - cd socket-server && npm run start &
    - cd frontend && npm run preview -- --port 5173 &
    
    # Wait for services
    - timeout 60 bash -c 'until curl -f http://localhost:8000/api/health/ 2>/dev/null; do sleep 1; done'
    - timeout 60 bash -c 'until curl -f http://localhost:7000/health 2>/dev/null; do sleep 1; done'
    - timeout 60 bash -c 'until curl -f http://localhost:5173 2>/dev/null; do sleep 1; done'
    
    # Run tests
    - npm run test:e2e
  
  artifacts:
    when: always
    paths:
      - e2e/reports/
      - e2e/test-results/
    expire_in: 30 days
    reports:
      junit: e2e/reports/results.xml
  
  parallel:
    matrix:
      - BROWSER: chromium
      - BROWSER: firefox
      - BROWSER: webkit
  
  only:
    - merge_requests
    - main
    - develop
```

## Jenkins Pipeline

### `Jenkinsfile`
```groovy
pipeline {
    agent any
    
    environment {
        NODE_VERSION = '18'
        PYTHON_VERSION = '3.11'
        DATABASE_URL = 'postgres://postgres:postgres@localhost:5432/chatapp_test'
    }
    
    stages {
        stage('Setup') {
            parallel {
                stage('Node.js Setup') {
                    steps {
                        sh 'npm ci'
                        sh 'npx playwright install --with-deps'
                    }
                }
                
                stage('Python Setup') {
                    steps {
                        sh 'cd backend && pip install -r requirements.txt'
                    }
                }
                
                stage('Database Setup') {
                    steps {
                        sh 'cd backend && python manage.py migrate --settings=chatapp.settings.test'
                        sh 'cd backend && python manage.py collectstatic --noinput --settings=chatapp.settings.test'
                    }
                }
            }
        }
        
        stage('Build') {
            steps {
                sh 'cd frontend && npm ci && npm run build'
            }
        }
        
        stage('Start Services') {
            parallel {
                stage('Backend') {
                    steps {
                        sh 'cd backend && python manage.py runserver 8000 --settings=chatapp.settings.test &'
                    }
                }
                
                stage('Socket Server') {
                    steps {
                        sh 'cd socket-server && npm run start &'
                    }
                }
                
                stage('Frontend') {
                    steps {
                        sh 'cd frontend && npm run preview -- --port 5173 &'
                    }
                }
            }
        }
        
        stage('Wait for Services') {
            steps {
                sh 'timeout 60 bash -c "until curl -f http://localhost:8000/api/health/ 2>/dev/null; do sleep 1; done"'
                sh 'timeout 60 bash -c "until curl -f http://localhost:7000/health 2>/dev/null; do sleep 1; done"'
                sh 'timeout 60 bash -c "until curl -f http://localhost:5173 2>/dev/null; do sleep 1; done"'
            }
        }
        
        stage('E2E Tests') {
            parallel {
                stage('Chromium') {
                    steps {
                        sh 'npm run test:e2e -- --project=chromium'
                    }
                }
                
                stage('Firefox') {
                    steps {
                        sh 'npm run test:e2e -- --project=firefox'
                    }
                }
                
                stage('WebKit') {
                    steps {
                        sh 'npm run test:e2e -- --project=webkit'
                    }
                }
            }
        }
    }
    
    post {
        always {
            // Collect test artifacts
            archiveArtifacts artifacts: 'e2e/reports/**/*', allowEmptyArchive: true
            archiveArtifacts artifacts: 'e2e/test-results/**/*', allowEmptyArchive: true
            
            // Publish test results
            publishHTML([
                allowMissing: false,
                alwaysLinkToLastBuild: true,
                keepAll: true,
                reportDir: 'e2e/reports/html',
                reportFiles: 'index.html',
                reportName: 'E2E Test Report'
            ])
            
            // Publish JUnit results
            publishTestResults testResultsPattern: 'e2e/reports/results.xml'
        }
        
        failure {
            // Send notifications on failure
            emailext (
                subject: "E2E Tests Failed: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "E2E tests failed. Check the build at ${env.BUILD_URL}",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
    }
}
```

## Azure DevOps

### `azure-pipelines.yml`
```yaml
trigger:
  branches:
    include:
      - main
      - develop

pr:
  branches:
    include:
      - main
      - develop

pool:
  vmImage: 'ubuntu-latest'

variables:
  nodeVersion: '18'
  pythonVersion: '3.11'

stages:
- stage: E2ETests
  displayName: 'E2E Tests'
  jobs:
  - job: TestMatrix
    displayName: 'Test Matrix'
    strategy:
      matrix:
        Chromium:
          browserName: 'chromium'
        Firefox:
          browserName: 'firefox'
        WebKit:
          browserName: 'webkit'
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: chatapp_test
        ports:
          - 5432:5432
    
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: $(nodeVersion)
      displayName: 'Install Node.js'
    
    - task: UsePythonVersion@0
      inputs:
        versionSpec: $(pythonVersion)
      displayName: 'Install Python'
    
    - script: |
        npm ci
        npx playwright install --with-deps
      displayName: 'Install Node.js dependencies'
    
    - script: |
        cd backend
        pip install -r requirements.txt
        python manage.py migrate --settings=chatapp.settings.test
        python manage.py collectstatic --noinput --settings=chatapp.settings.test
      displayName: 'Setup Python and Database'
      env:
        DATABASE_URL: postgres://postgres:postgres@localhost:5432/chatapp_test
    
    - script: |
        cd frontend
        npm ci
        npm run build
      displayName: 'Build Frontend'
    
    - script: |
        # Start services
        cd backend && python manage.py runserver 8000 --settings=chatapp.settings.test &
        cd socket-server && npm run start &
        cd frontend && npm run preview -- --port 5173 &
        
        # Wait for services
        timeout 60 bash -c 'until curl -f http://localhost:8000/api/health/ 2>/dev/null; do sleep 1; done'
        timeout 60 bash -c 'until curl -f http://localhost:7000/health 2>/dev/null; do sleep 1; done'
        timeout 60 bash -c 'until curl -f http://localhost:5173 2>/dev/null; do sleep 1; done'
        
        # Run tests
        npm run test:e2e -- --project=$(browserName)
      displayName: 'Run E2E Tests'
      env:
        DATABASE_URL: postgres://postgres:postgres@localhost:5432/chatapp_test
    
    - task: PublishTestResults@2
      condition: always()
      inputs:
        testResultsFormat: 'JUnit'
        testResultsFiles: 'e2e/reports/results.xml'
        testRunTitle: 'E2E Tests - $(browserName)'
    
    - task: PublishHtmlReport@1
      condition: always()
      inputs:
        reportDir: 'e2e/reports/html'
        tabName: 'E2E Test Report - $(browserName)'
```

## CircleCI

### `.circleci/config.yml`
```yaml
version: 2.1

orbs:
  node: circleci/node@5.0.0
  python: circleci/python@2.0.0

executors:
  playwright-executor:
    docker:
      - image: mcr.microsoft.com/playwright:v1.40.0-focal
      - image: postgres:13
        environment:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: chatapp_test

jobs:
  e2e-tests:
    executor: playwright-executor
    parallelism: 3
    parameters:
      browser:
        type: string
    
    environment:
      DATABASE_URL: postgres://postgres:postgres@localhost:5432/chatapp_test
    
    steps:
      - checkout
      
      - node/install:
          node-version: '18'
      
      - python/install:
          version: '3.11'
      
      - run:
          name: Install dependencies
          command: |
            npm ci
            cd backend && pip install -r requirements.txt
      
      - run:
          name: Setup database
          command: |
            cd backend
            python manage.py migrate --settings=chatapp.settings.test
            python manage.py collectstatic --noinput --settings=chatapp.settings.test
      
      - run:
          name: Build frontend
          command: |
            cd frontend
            npm ci
            npm run build
      
      - run:
          name: Start services and run tests
          command: |
            # Start services
            cd backend && python manage.py runserver 8000 --settings=chatapp.settings.test &
            cd socket-server && npm run start &
            cd frontend && npm run preview -- --port 5173 &
            
            # Wait for services
            timeout 60 bash -c 'until curl -f http://localhost:8000/api/health/ 2>/dev/null; do sleep 1; done'
            timeout 60 bash -c 'until curl -f http://localhost:7000/health 2>/dev/null; do sleep 1; done'
            timeout 60 bash -c 'until curl -f http://localhost:5173 2>/dev/null; do sleep 1; done'
            
            # Run tests
            npm run test:e2e -- --project=<< parameters.browser >>
      
      - store_test_results:
          path: e2e/reports
      
      - store_artifacts:
          path: e2e/reports
      
      - store_artifacts:
          path: e2e/test-results

workflows:
  e2e-testing:
    jobs:
      - e2e-tests:
          matrix:
            parameters:
              browser: ["chromium", "firefox", "webkit"]
```

## Docker Integration

### `Dockerfile.e2e`
```dockerfile
FROM mcr.microsoft.com/playwright:v1.40.0-focal

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY frontend/package*.json ./frontend/
COPY socket-server/package*.json ./socket-server/
COPY backend/requirements.txt ./backend/

# Install dependencies
RUN npm ci
RUN cd frontend && npm ci
RUN cd socket-server && npm ci
RUN cd backend && pip install -r requirements.txt

# Copy source code
COPY . .

# Build frontend
RUN cd frontend && npm run build

# Expose ports
EXPOSE 8000 7000 5173

# Run tests
CMD ["npm", "run", "test:e2e"]
```

### `docker-compose.e2e.yml`
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: chatapp_test
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  e2e-tests:
    build:
      context: .
      dockerfile: Dockerfile.e2e
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      DATABASE_URL: ******************************************/chatapp_test
      NODE_ENV: test
    volumes:
      - ./e2e/reports:/app/e2e/reports
      - ./e2e/test-results:/app/e2e/test-results
    command: |
      bash -c "
        # Start services
        cd backend && python manage.py runserver 8000 --settings=chatapp.settings.test &
        cd socket-server && npm run start &
        cd frontend && npm run preview -- --port 5173 &
        
        # Wait for services
        timeout 60 bash -c 'until curl -f http://localhost:8000/api/health/ 2>/dev/null; do sleep 1; done'
        timeout 60 bash -c 'until curl -f http://localhost:7000/health 2>/dev/null; do sleep 1; done'
        timeout 60 bash -c 'until curl -f http://localhost:5173 2>/dev/null; do sleep 1; done'
        
        # Run tests
        npm run test:e2e
      "
```

## Best Practices for CI/CD

### 1. Service Management
- Use health checks to ensure services are ready
- Implement proper service startup order
- Use background processes with proper cleanup

### 2. Test Isolation
- Use separate test databases
- Clean up test data between runs
- Avoid shared state between tests

### 3. Artifact Management
- Collect test reports and screenshots
- Store artifacts for debugging
- Set appropriate retention policies

### 4. Performance Optimization
- Use parallel execution when possible
- Cache dependencies between runs
- Optimize Docker layers for faster builds

### 5. Error Handling
- Implement proper retry mechanisms
- Collect debug information on failures
- Send notifications for critical failures

### 6. Security
- Use secure environment variables
- Avoid exposing sensitive data in logs
- Implement proper access controls

## Monitoring and Alerting

### Test Result Monitoring
```yaml
# Example monitoring configuration
monitoring:
  test_success_rate:
    threshold: 95%
    alert_channels: ["slack", "email"]
  
  test_duration:
    threshold: 30m
    alert_channels: ["slack"]
  
  flaky_tests:
    threshold: 3_failures_in_24h
    alert_channels: ["email"]
```

### Metrics Collection
- Test execution time
- Success/failure rates
- Browser-specific performance
- Flaky test identification

## Troubleshooting CI/CD Issues

### Common Problems
1. **Service startup timeouts**
2. **Browser installation failures**
3. **Database connection issues**
4. **Network connectivity problems**
5. **Resource constraints**

### Debug Strategies
- Enable verbose logging
- Collect service logs
- Use debug mode for failing tests
- Check resource usage
- Verify environment variables

## Integration Examples

The CI/CD configurations provided can be adapted for:
- **Staging environment testing**
- **Production smoke tests**
- **Performance regression testing**
- **Visual regression testing**
- **API integration testing**

Each configuration includes proper service orchestration, test execution, and artifact collection to ensure reliable E2E testing in automated environments.
