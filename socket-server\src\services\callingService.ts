// socket-server/src/services/callingService.ts
import { PrismaClient } from '@prisma/client';

export interface CallData {
  id: string;
  conversationId: string;
  callerId: string;
  calleeId: string;
  callType: 'audio' | 'video';
  status: 'initiated' | 'ringing' | 'answered' | 'active' | 'ended' | 'missed' | 'declined' | 'failed';
  sessionId: string;
  initiatedAt: Date;
  answeredAt?: Date;
  endedAt?: Date;
  callerSdp?: string;
  calleeSdp?: string;
}

export interface CallEvent {
  id: string;
  callId: string;
  eventType: string;
  userId: string;
  eventData: Record<string, any>;
  timestamp: Date;
}

export interface CallQualityMetric {
  id: string;
  callId: string;
  userId: string;
  packetLoss?: number;
  jitter?: number;
  roundTripTime?: number;
  bandwidthUpload?: number;
  bandwidthDownload?: number;
  audioLevel?: number;
  audioQualityScore?: number;
  videoResolution?: string;
  videoFramerate?: number;
  videoQualityScore?: number;
  recordedAt: Date;
}

export class CallingService {
  constructor(private prisma: PrismaClient) {}

  async createCall(data: {
    conversationId: string;
    callerId: string;
    calleeId: string;
    callType: 'audio' | 'video';
    sessionId: string;
  }): Promise<CallData> {
    const call = await this.prisma.call.create({
      data: {
        conversation_id: data.conversationId,
        caller_id: data.callerId,
        callee_id: data.calleeId,
        call_type: data.callType,
        session_id: data.sessionId,
        status: 'initiated',
        initiated_at: new Date(),
        quality_issues: {},
        metadata: {},
      },
    });

    return this.mapCallFromDb(call);
  }

  async getCall(callId: string): Promise<CallData | null> {
    const call = await this.prisma.call.findUnique({
      where: { id: callId },
    });

    return call ? this.mapCallFromDb(call) : null;
  }

  async updateCallStatus(
    callId: string,
    status: CallData['status'],
    additionalData?: Partial<Pick<CallData, 'answeredAt' | 'endedAt' | 'callerSdp' | 'calleeSdp'>>
  ): Promise<CallData> {
    const updateData: any = { status };

    if (additionalData?.answeredAt) {
      updateData.answered_at = additionalData.answeredAt;
    }
    if (additionalData?.endedAt) {
      updateData.ended_at = additionalData.endedAt;
    }
    if (additionalData?.callerSdp !== undefined) {
      updateData.caller_sdp = additionalData.callerSdp;
    }
    if (additionalData?.calleeSdp !== undefined) {
      updateData.callee_sdp = additionalData.calleeSdp;
    }

    const call = await this.prisma.call.update({
      where: { id: callId },
      data: updateData,
    });

    return this.mapCallFromDb(call);
  }

  async createCallEvent(data: {
    callId: string;
    eventType: string;
    userId: string;
    eventData?: Record<string, any>;
  }): Promise<CallEvent> {
    const event = await this.prisma.callEvent.create({
      data: {
        call_id: data.callId,
        event_type: data.eventType,
        user_id: data.userId,
        event_data: data.eventData || {},
        timestamp: new Date(),
      },
    });

    return this.mapCallEventFromDb(event);
  }

  async createQualityMetric(data: {
    callId: string;
    userId: string;
    packetLoss?: number;
    jitter?: number;
    roundTripTime?: number;
    bandwidthUpload?: number;
    bandwidthDownload?: number;
    audioLevel?: number;
    audioQualityScore?: number;
    videoResolution?: string;
    videoFramerate?: number;
    videoQualityScore?: number;
  }): Promise<CallQualityMetric> {
    const metric = await this.prisma.callQualityMetric.create({
      data: {
        call_id: data.callId,
        user_id: data.userId,
        packet_loss: data.packetLoss,
        jitter: data.jitter,
        round_trip_time: data.roundTripTime,
        bandwidth_upload: data.bandwidthUpload,
        bandwidth_download: data.bandwidthDownload,
        audio_level: data.audioLevel,
        audio_quality_score: data.audioQualityScore,
        video_resolution: data.videoResolution,
        video_framerate: data.videoFramerate,
        video_quality_score: data.videoQualityScore,
        recorded_at: new Date(),
      },
    });

    return this.mapQualityMetricFromDb(metric);
  }

  async getActiveCallsForUser(userId: string): Promise<CallData[]> {
    const calls = await this.prisma.call.findMany({
      where: {
        OR: [
          { caller_id: userId },
          { callee_id: userId },
        ],
        status: {
          in: ['initiated', 'ringing', 'answered', 'active'],
        },
      },
      orderBy: { initiated_at: 'desc' },
    });

    return calls.map(call => this.mapCallFromDb(call));
  }

  async getCallHistory(userId: string, limit: number = 50): Promise<CallData[]> {
    const calls = await this.prisma.call.findMany({
      where: {
        OR: [
          { caller_id: userId },
          { callee_id: userId },
        ],
      },
      orderBy: { initiated_at: 'desc' },
      take: limit,
    });

    return calls.map(call => this.mapCallFromDb(call));
  }

  async getConversationWithParticipants(conversationId: string): Promise<{
    id: string;
    participants: { userId: string; role: string }[];
  } | null> {
    const conversation = await this.prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        participants: {
          where: { is_active: true },
          select: {
            userId: true,
            role: true,
          },
        },
      },
    });

    if (!conversation) {
      return null;
    }

    return {
      id: conversation.id,
      participants: conversation.participants.map((p: any) => ({
        userId: p.userId,
        role: p.role,
      })),
    };
  }

  private mapCallFromDb(call: any): CallData {
    return {
      id: call.id,
      conversationId: call.conversation_id,
      callerId: call.caller_id,
      calleeId: call.callee_id,
      callType: call.call_type,
      status: call.status,
      sessionId: call.session_id,
      initiatedAt: call.initiated_at,
      answeredAt: call.answered_at,
      endedAt: call.ended_at,
      callerSdp: call.caller_sdp,
      calleeSdp: call.callee_sdp,
    };
  }

  private mapCallEventFromDb(event: any): CallEvent {
    return {
      id: event.id,
      callId: event.call_id,
      eventType: event.event_type,
      userId: event.user_id,
      eventData: event.event_data,
      timestamp: event.timestamp,
    };
  }

  private mapQualityMetricFromDb(metric: any): CallQualityMetric {
    return {
      id: metric.id,
      callId: metric.call_id,
      userId: metric.user_id,
      packetLoss: metric.packet_loss,
      jitter: metric.jitter,
      roundTripTime: metric.round_trip_time,
      bandwidthUpload: metric.bandwidth_upload,
      bandwidthDownload: metric.bandwidth_download,
      audioLevel: metric.audio_level,
      audioQualityScore: metric.audio_quality_score,
      videoResolution: metric.video_resolution,
      videoFramerate: metric.video_framerate,
      videoQualityScore: metric.video_quality_score,
      recordedAt: metric.recorded_at,
    };
  }
}
