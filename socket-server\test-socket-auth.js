// Test socket authentication with real JWT token
const io = require('socket.io-client');

// Real JWT token from Alice's login
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzU3MzExNzg0LCJpYXQiOjE3NTczMDgxODQsImp0aSI6ImRiMDk2OGM0YjliNzQ1YjY5YWY1NjRkOWZmMjZjNDExIiwidXNlcl9pZCI6ImEyZWMzNjBiLWI2ZjctNDRhMS04NmQyLWEwMDFkMGFkYmNiOCJ9.YMGcJm-V4RFo6t63ajoX3IXcV_I_x_Uk-mAvJ1LKOk0';

console.log('🔌 Testing socket authentication...');
console.log('Token (first 50 chars):', token.substring(0, 50) + '...');

// Connect to socket server with authentication
const socket = io('http://localhost:7000', {
  auth: {
    token: token
  },
  transports: ['websocket']
});

socket.on('connect', () => {
  console.log('✅ Socket connected successfully!');
  console.log('Socket ID:', socket.id);
  
  // Test a simple event
  socket.emit('test-event', { message: 'Hello from authenticated user!' });
});

socket.on('connect_error', (error) => {
  console.log('❌ Socket connection failed:');
  console.log('Error:', error.message);
  console.log('Error details:', error);
});

socket.on('disconnect', (reason) => {
  console.log('🔌 Socket disconnected:', reason);
});

socket.on('authenticated', (data) => {
  console.log('🔐 Authentication successful:', data);
});

socket.on('authentication_error', (error) => {
  console.log('🚫 Authentication failed:', error);
});

// Keep the script running for a few seconds
setTimeout(() => {
  console.log('\n🔌 Disconnecting...');
  socket.disconnect();
  process.exit(0);
}, 5000);