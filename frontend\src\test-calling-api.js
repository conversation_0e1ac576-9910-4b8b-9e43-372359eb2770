// Test script to verify calling API endpoints work from frontend
// Run this in browser console or as a standalone test

const API_BASE = 'http://localhost:5000/api';

// Test data
const testData = {
  conversation_id: '550e8400-e29b-41d4-a716-446655440000',
  call_type: 'audio'
};

// Get auth token from localStorage (assuming user is logged in)
const token = localStorage.getItem('token');

if (!token) {
  console.error('❌ No auth token found. Please log in first.');
} else {
  console.log('🔑 Using token:', token.substring(0, 20) + '...');
}

// Test function to make API calls
async function testCallingAPI() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };

  try {
    // Test 1: Initiate Call
    console.log('\n🚀 Testing initiate call...');
    const initiateResponse = await fetch(`${API_BASE}/calling/initiate/`, {
      method: 'POST',
      headers,
      body: JSON.stringify(testData)
    });
    
    const initiateResult = await initiateResponse.json();
    console.log('📞 Initiate Response:', initiateResponse.status, initiateResult);
    
    if (initiateResponse.ok && initiateResult.id) {
      const callId = initiateResult.id;
      console.log('✅ Call initiated successfully! Call ID:', callId);
      
      // Test 2: Answer Call (will fail if same user, but tests endpoint)
      console.log('\n📞 Testing answer call...');
      const answerResponse = await fetch(`${API_BASE}/calling/${callId}/answer/`, {
        method: 'POST',
        headers
      });
      const answerResult = await answerResponse.json();
      console.log('📞 Answer Response:', answerResponse.status, answerResult);
      
      // Test 3: End Call
      console.log('\n🔚 Testing end call...');
      const endResponse = await fetch(`${API_BASE}/calling/${callId}/end/`, {
        method: 'POST',
        headers
      });
      const endResult = await endResponse.json();
      console.log('🔚 End Response:', endResponse.status, endResult);
      
    } else {
      console.error('❌ Failed to initiate call');
    }
    
    // Test 4: Get Call History
    console.log('\n📋 Testing call history...');
    const historyResponse = await fetch(`${API_BASE}/calling/history/`, {
      method: 'GET',
      headers
    });
    const historyResult = await historyResponse.json();
    console.log('📋 History Response:', historyResponse.status, historyResult);
    
  } catch (error) {
    console.error('❌ API Test Error:', error);
  }
}

// Export for use
if (typeof window !== 'undefined') {
  window.testCallingAPI = testCallingAPI;
  console.log('🧪 Test function loaded! Run testCallingAPI() in console to test.');
}

// Auto-run if token exists
if (token) {
  testCallingAPI();
}