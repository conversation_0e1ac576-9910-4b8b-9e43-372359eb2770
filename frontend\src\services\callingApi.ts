// frontend/src/services/callingApi.ts
import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQueryWithAuth } from './api';

export interface Call {
  id: string;
  conversation: string;
  caller: {
    id: string;
    username: string;
    first_name: string;
    last_name: string;
    profile_picture?: string;
  };
  callee: {
    id: string;
    username: string;
    first_name: string;
    last_name: string;
    profile_picture?: string;
  };
  call_type: 'audio' | 'video';
  status: 'initiated' | 'ringing' | 'answered' | 'active' | 'ended' | 'missed' | 'declined' | 'failed';
  initiated_at: string;
  answered_at?: string;
  ended_at?: string;
  duration?: string;
  session_id: string;
  caller_sdp?: string;
  callee_sdp?: string;
  quality_rating?: number;
  quality_issues: string[];
  metadata: Record<string, any>;
}

export interface CallEvent {
  id: string;
  call: string;
  event_type: string;
  user: string;
  event_data: Record<string, any>;
  timestamp: string;
}

export interface CallQualityMetric {
  id: string;
  call: string;
  user: string;
  packet_loss?: number;
  jitter?: number;
  round_trip_time?: number;
  bandwidth_upload?: number;
  bandwidth_download?: number;
  audio_level?: number;
  audio_quality_score?: number;
  video_resolution?: string;
  video_framerate?: number;
  video_quality_score?: number;
  recorded_at: string;
}

export interface InitiateCallRequest {
  conversationId: string;
  callType: 'audio' | 'video';
}

export interface UpdateSDPRequest {
  type: 'offer' | 'answer';
  sdp: string;
}

export interface CallQualityReportRequest {
  packet_loss?: number;
  jitter?: number;
  round_trip_time?: number;
  bandwidth_upload?: number;
  bandwidth_download?: number;
  audio_level?: number;
  audio_quality_score?: number;
  video_resolution?: string;
  video_framerate?: number;
  video_quality_score?: number;
}

export const callingApi = createApi({
  reducerPath: 'callingApi',
  baseQuery: baseQueryWithAuth,
  tagTypes: ['Call', 'CallHistory'],
  endpoints: (builder) => ({
    // Initiate a new call
    initiateCall: builder.mutation<Call, InitiateCallRequest>({
      query: (data) => ({
        url: '/calling/initiate/',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['CallHistory'],
    }),

    // Answer an incoming call
    answerCall: builder.mutation<Call, string>({
      query: (callId) => ({
        url: `/calling/${callId}/answer/`,
        method: 'POST',
      }),
      invalidatesTags: ['CallHistory'],
    }),

    // Decline a call
    declineCall: builder.mutation<{ message: string }, string>({
      query: (callId) => ({
        url: `/calling/${callId}/decline/`,
        method: 'POST',
      }),
      invalidatesTags: ['CallHistory'],
    }),

    // End a call
    endCall: builder.mutation<{ message: string }, string>({
      query: (callId) => ({
        url: `/calling/${callId}/end/`,
        method: 'POST',
      }),
      invalidatesTags: ['CallHistory'],
    }),

    // Update SDP for WebRTC negotiation
    updateCallSDP: builder.mutation<{ message: string }, { callId: string; data: UpdateSDPRequest }>({
      query: ({ callId, data }) => ({
        url: `/calling/${callId}/sdp/`,
        method: 'POST',
        body: data,
      }),
    }),

    // Report call quality metrics
    reportCallQuality: builder.mutation<{ message: string }, { callId: string; data: CallQualityReportRequest }>({
      query: ({ callId, data }) => ({
        url: `/calling/${callId}/quality/`,
        method: 'POST',
        body: data,
      }),
    }),

    // Get call history
    getCallHistory: builder.query<Call[], void>({
      query: () => '/calling/history/',
      providesTags: ['CallHistory'],
    }),

    // Get call details
    getCallDetail: builder.query<Call, string>({
      query: (callId) => `/calling/${callId}/`,
      providesTags: (result, error, callId) => [{ type: 'Call', id: callId }],
    }),
  }),
});

export const {
  useInitiateCallMutation,
  useAnswerCallMutation,
  useDeclineCallMutation,
  useEndCallMutation,
  useUpdateCallSDPMutation,
  useReportCallQualityMutation,
  useGetCallHistoryQuery,
  useGetCallDetailQuery,
} = callingApi;
